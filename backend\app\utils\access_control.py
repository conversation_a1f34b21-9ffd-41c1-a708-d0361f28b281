"""
访问控制系统
实现基于角色的访问控制(RBAC)和细粒度权限管理
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Union
from enum import Enum
from dataclasses import dataclass, asdict
import json

from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from ..utils.cache import get_cache_manager
from .audit_logger import get_audit_logger, AuditLevel


class Permission(Enum):
    """权限枚举"""
    # 用户管理
    USER_READ = "user:read"
    USER_WRITE = "user:write"
    USER_DELETE = "user:delete"
    
    # 交易管理
    TRANSACTION_READ = "transaction:read"
    TRANSACTION_WRITE = "transaction:write"
    TRANSACTION_DELETE = "transaction:delete"
    TRANSACTION_BULK = "transaction:bulk"
    
    # 类别管理
    CATEGORY_READ = "category:read"
    CATEGORY_WRITE = "category:write"
    CATEGORY_DELETE = "category:delete"
    
    # 预算管理
    BUDGET_READ = "budget:read"
    BUDGET_WRITE = "budget:write"
    BUDGET_DELETE = "budget:delete"
    
    # AI功能
    AI_USE = "ai:use"
    AI_CONFIG = "ai:config"
    AI_ADMIN = "ai:admin"
    
    # 分析功能
    ANALYTICS_READ = "analytics:read"
    ANALYTICS_ADVANCED = "analytics:advanced"
    
    # 系统管理
    SYSTEM_CONFIG = "system:config"
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_AUDIT = "system:audit"
    
    # API管理
    API_KEY_READ = "api_key:read"
    API_KEY_WRITE = "api_key:write"
    API_KEY_DELETE = "api_key:delete"
    API_KEY_ROTATE = "api_key:rotate"


class Role(Enum):
    """角色枚举"""
    GUEST = "guest"
    USER = "user"
    PREMIUM_USER = "premium_user"
    ADMIN = "admin"
    SUPER_ADMIN = "super_admin"


@dataclass
class AccessPolicy:
    """访问策略"""
    id: str
    name: str
    description: str
    permissions: List[Permission]
    conditions: Dict  # 访问条件
    created_at: datetime
    updated_at: datetime
    active: bool = True

    def to_dict(self) -> Dict:
        """转换为字典"""
        data = asdict(self)
        data['permissions'] = [p.value for p in self.permissions]
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        return data


@dataclass
class UserRole:
    """用户角色"""
    user_id: str
    role: Role
    granted_by: str
    granted_at: datetime
    expires_at: Optional[datetime] = None
    active: bool = True

    def to_dict(self) -> Dict:
        """转换为字典"""
        data = asdict(self)
        data['role'] = self.role.value
        data['granted_at'] = self.granted_at.isoformat()
        if self.expires_at:
            data['expires_at'] = self.expires_at.isoformat()
        return data


class AccessControlManager:
    """访问控制管理器"""

    def __init__(self):
        self.cache_manager = get_cache_manager()
        self.audit_logger = get_audit_logger()
        
        # 角色权限映射
        self.role_permissions = {
            Role.GUEST: [
                Permission.USER_READ,
            ],
            Role.USER: [
                Permission.USER_READ,
                Permission.USER_WRITE,
                Permission.TRANSACTION_READ,
                Permission.TRANSACTION_WRITE,
                Permission.TRANSACTION_DELETE,
                Permission.CATEGORY_READ,
                Permission.CATEGORY_WRITE,
                Permission.BUDGET_READ,
                Permission.BUDGET_WRITE,
                Permission.AI_USE,
                Permission.ANALYTICS_READ,
                Permission.API_KEY_READ,
                Permission.API_KEY_WRITE,
            ],
            Role.PREMIUM_USER: [
                # 包含USER的所有权限
                Permission.USER_READ,
                Permission.USER_WRITE,
                Permission.TRANSACTION_READ,
                Permission.TRANSACTION_WRITE,
                Permission.TRANSACTION_DELETE,
                Permission.TRANSACTION_BULK,
                Permission.CATEGORY_READ,
                Permission.CATEGORY_WRITE,
                Permission.CATEGORY_DELETE,
                Permission.BUDGET_READ,
                Permission.BUDGET_WRITE,
                Permission.BUDGET_DELETE,
                Permission.AI_USE,
                Permission.AI_CONFIG,
                Permission.ANALYTICS_READ,
                Permission.ANALYTICS_ADVANCED,
                Permission.API_KEY_READ,
                Permission.API_KEY_WRITE,
                Permission.API_KEY_DELETE,
                Permission.API_KEY_ROTATE,
            ],
            Role.ADMIN: [
                # 包含PREMIUM_USER的所有权限，加上管理权限
                Permission.USER_READ,
                Permission.USER_WRITE,
                Permission.USER_DELETE,
                Permission.TRANSACTION_READ,
                Permission.TRANSACTION_WRITE,
                Permission.TRANSACTION_DELETE,
                Permission.TRANSACTION_BULK,
                Permission.CATEGORY_READ,
                Permission.CATEGORY_WRITE,
                Permission.CATEGORY_DELETE,
                Permission.BUDGET_READ,
                Permission.BUDGET_WRITE,
                Permission.BUDGET_DELETE,
                Permission.AI_USE,
                Permission.AI_CONFIG,
                Permission.AI_ADMIN,
                Permission.ANALYTICS_READ,
                Permission.ANALYTICS_ADVANCED,
                Permission.SYSTEM_CONFIG,
                Permission.SYSTEM_AUDIT,
                Permission.API_KEY_READ,
                Permission.API_KEY_WRITE,
                Permission.API_KEY_DELETE,
                Permission.API_KEY_ROTATE,
            ],
            Role.SUPER_ADMIN: [
                # 所有权限
                *list(Permission)
            ]
        }
        
        # 用户角色缓存
        self.user_roles: Dict[str, List[UserRole]] = {}
        self.access_policies: Dict[str, AccessPolicy] = {}

    async def assign_role(
        self,
        user_id: str,
        role: Role,
        granted_by: str,
        expires_at: Optional[datetime] = None
    ) -> bool:
        """分配角色给用户"""
        try:
            # 检查授权者权限
            if not await self.has_permission(granted_by, Permission.USER_WRITE):
                raise PermissionError("无权限分配角色")
            
            user_role = UserRole(
                user_id=user_id,
                role=role,
                granted_by=granted_by,
                granted_at=datetime.now(),
                expires_at=expires_at
            )
            
            # 添加到用户角色列表
            if user_id not in self.user_roles:
                self.user_roles[user_id] = []
            
            self.user_roles[user_id].append(user_role)
            
            # 清除用户权限缓存
            await self._clear_user_permission_cache(user_id)
            
            # 记录审计日志
            await self.audit_logger.log_event(
                event_type=self.audit_logger.AuditEventType.SYSTEM_CONFIG,
                level=AuditLevel.MEDIUM,
                user_id=granted_by,
                action="assign_role",
                details={
                    "target_user_id": user_id,
                    "role": role.value,
                    "expires_at": expires_at.isoformat() if expires_at else None
                }
            )
            
            logger.info(f"已为用户 {user_id} 分配角色 {role.value}")
            return True
            
        except Exception as e:
            logger.error(f"分配角色失败: {e}")
            return False

    async def revoke_role(self, user_id: str, role: Role, revoked_by: str) -> bool:
        """撤销用户角色"""
        try:
            # 检查撤销者权限
            if not await self.has_permission(revoked_by, Permission.USER_WRITE):
                raise PermissionError("无权限撤销角色")
            
            user_roles = self.user_roles.get(user_id, [])
            
            # 查找并撤销角色
            for user_role in user_roles:
                if user_role.role == role and user_role.active:
                    user_role.active = False
                    
                    # 清除用户权限缓存
                    await self._clear_user_permission_cache(user_id)
                    
                    # 记录审计日志
                    await self.audit_logger.log_event(
                        event_type=self.audit_logger.AuditEventType.SYSTEM_CONFIG,
                        level=AuditLevel.MEDIUM,
                        user_id=revoked_by,
                        action="revoke_role",
                        details={
                            "target_user_id": user_id,
                            "role": role.value
                        }
                    )
                    
                    logger.info(f"已撤销用户 {user_id} 的角色 {role.value}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"撤销角色失败: {e}")
            return False

    async def get_user_roles(self, user_id: str) -> List[Role]:
        """获取用户角色"""
        try:
            # 从缓存获取
            cache_key = f"user_roles:{user_id}"
            cached_roles = await self.cache_manager.get(cache_key)
            if cached_roles:
                return [Role(role) for role in json.loads(cached_roles)]
            
            # 获取活跃的角色
            user_roles = self.user_roles.get(user_id, [])
            current_time = datetime.now()
            
            active_roles = []
            for user_role in user_roles:
                if (user_role.active and 
                    (user_role.expires_at is None or user_role.expires_at > current_time)):
                    active_roles.append(user_role.role)
            
            # 如果没有角色，分配默认角色
            if not active_roles:
                active_roles = [Role.USER]
                await self.assign_role(user_id, Role.USER, "system")
            
            # 缓存结果
            await self.cache_manager.set(
                cache_key,
                json.dumps([role.value for role in active_roles]),
                ttl=3600  # 1小时
            )
            
            return active_roles
            
        except Exception as e:
            logger.error(f"获取用户角色失败: {e}")
            return [Role.USER]  # 默认角色

    async def get_user_permissions(self, user_id: str) -> Set[Permission]:
        """获取用户权限"""
        try:
            # 从缓存获取
            cache_key = f"user_permissions:{user_id}"
            cached_permissions = await self.cache_manager.get(cache_key)
            if cached_permissions:
                return {Permission(perm) for perm in json.loads(cached_permissions)}
            
            # 获取用户角色
            user_roles = await self.get_user_roles(user_id)
            
            # 合并所有角色的权限
            all_permissions = set()
            for role in user_roles:
                role_permissions = self.role_permissions.get(role, [])
                all_permissions.update(role_permissions)
            
            # 缓存结果
            await self.cache_manager.set(
                cache_key,
                json.dumps([perm.value for perm in all_permissions]),
                ttl=3600  # 1小时
            )
            
            return all_permissions
            
        except Exception as e:
            logger.error(f"获取用户权限失败: {e}")
            return set()

    async def has_permission(self, user_id: str, permission: Permission) -> bool:
        """检查用户是否有特定权限"""
        try:
            user_permissions = await self.get_user_permissions(user_id)
            return permission in user_permissions
            
        except Exception as e:
            logger.error(f"检查用户权限失败: {e}")
            return False

    async def has_any_permission(self, user_id: str, permissions: List[Permission]) -> bool:
        """检查用户是否有任一权限"""
        try:
            user_permissions = await self.get_user_permissions(user_id)
            return any(perm in user_permissions for perm in permissions)
            
        except Exception as e:
            logger.error(f"检查用户权限失败: {e}")
            return False

    async def has_all_permissions(self, user_id: str, permissions: List[Permission]) -> bool:
        """检查用户是否有所有权限"""
        try:
            user_permissions = await self.get_user_permissions(user_id)
            return all(perm in user_permissions for perm in permissions)
            
        except Exception as e:
            logger.error(f"检查用户权限失败: {e}")
            return False

    async def check_resource_access(
        self,
        user_id: str,
        resource_type: str,
        resource_id: str,
        action: str
    ) -> bool:
        """检查资源访问权限"""
        try:
            # 构建权限字符串
            permission_str = f"{resource_type}:{action}"
            
            # 查找对应的权限
            try:
                permission = Permission(permission_str)
            except ValueError:
                logger.warning(f"未知权限: {permission_str}")
                return False
            
            # 检查基本权限
            if not await self.has_permission(user_id, permission):
                return False
            
            # 检查资源所有权（如果是用户自己的资源）
            if resource_type in ["transaction", "budget", "category"]:
                return await self._check_resource_ownership(user_id, resource_type, resource_id)
            
            return True
            
        except Exception as e:
            logger.error(f"检查资源访问权限失败: {e}")
            return False

    async def _check_resource_ownership(
        self,
        user_id: str,
        resource_type: str,
        resource_id: str
    ) -> bool:
        """检查资源所有权"""
        try:
            # 这里应该查询数据库检查资源所有权
            # 目前返回True（简化实现）
            return True
            
        except Exception as e:
            logger.error(f"检查资源所有权失败: {e}")
            return False

    async def _clear_user_permission_cache(self, user_id: str):
        """清除用户权限缓存"""
        try:
            cache_keys = [
                f"user_roles:{user_id}",
                f"user_permissions:{user_id}"
            ]
            
            for cache_key in cache_keys:
                await self.cache_manager.delete(cache_key)
                
        except Exception as e:
            logger.error(f"清除用户权限缓存失败: {e}")

    async def create_access_policy(
        self,
        name: str,
        description: str,
        permissions: List[Permission],
        conditions: Dict,
        created_by: str
    ) -> Optional[str]:
        """创建访问策略"""
        try:
            # 检查创建者权限
            if not await self.has_permission(created_by, Permission.SYSTEM_ADMIN):
                raise PermissionError("无权限创建访问策略")
            
            import uuid
            policy_id = str(uuid.uuid4())
            
            policy = AccessPolicy(
                id=policy_id,
                name=name,
                description=description,
                permissions=permissions,
                conditions=conditions,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            self.access_policies[policy_id] = policy
            
            # 记录审计日志
            await self.audit_logger.log_event(
                event_type=self.audit_logger.AuditEventType.SYSTEM_CONFIG,
                level=AuditLevel.MEDIUM,
                user_id=created_by,
                action="create_access_policy",
                details={
                    "policy_id": policy_id,
                    "policy_name": name,
                    "permissions_count": len(permissions)
                }
            )
            
            logger.info(f"已创建访问策略: {policy_id}")
            return policy_id
            
        except Exception as e:
            logger.error(f"创建访问策略失败: {e}")
            return None

    async def get_access_summary(self, user_id: str) -> Dict:
        """获取用户访问权限摘要"""
        try:
            user_roles = await self.get_user_roles(user_id)
            user_permissions = await self.get_user_permissions(user_id)
            
            return {
                "user_id": user_id,
                "roles": [role.value for role in user_roles],
                "permissions_count": len(user_permissions),
                "permissions": [perm.value for perm in user_permissions],
                "is_admin": Role.ADMIN in user_roles or Role.SUPER_ADMIN in user_roles,
                "is_premium": Role.PREMIUM_USER in user_roles or Role.ADMIN in user_roles or Role.SUPER_ADMIN in user_roles
            }
            
        except Exception as e:
            logger.error(f"获取访问权限摘要失败: {e}")
            return {}


# 全局访问控制管理器实例
_access_control_manager = None


def get_access_control_manager() -> AccessControlManager:
    """获取访问控制管理器实例"""
    global _access_control_manager
    if _access_control_manager is None:
        _access_control_manager = AccessControlManager()
    return _access_control_manager
