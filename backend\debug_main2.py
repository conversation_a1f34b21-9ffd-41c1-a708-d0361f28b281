#!/usr/bin/env python3
"""
调试版本2 - 逐步添加中间件
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
import uvicorn
from loguru import logger
from contextlib import asynccontextmanager

# 导入配置
try:
    from app.config import settings
    print("✅ 配置导入成功")
except Exception as e:
    print(f"❌ 配置导入失败: {e}")
    # 创建默认配置
    class DefaultSettings:
        DEBUG = True
        ALLOWED_ORIGINS = ["*"]
        ALLOWED_HOSTS = ["*"]
    settings = DefaultSettings()

# 导入数据库
try:
    from app.database.connection import init_db, close_db
    print("✅ 数据库模块导入成功")
except Exception as e:
    print(f"❌ 数据库模块导入失败: {e}")
    init_db = None
    close_db = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    print("🚀 应用启动中...")
    if init_db:
        try:
            await init_db()
            print("✅ 数据库初始化成功")
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
    yield
    print("🛑 应用关闭中...")
    if close_db:
        try:
            await close_db()
            print("✅ 数据库关闭成功")
        except Exception as e:
            print(f"❌ 数据库关闭失败: {e}")

# 创建应用
app = FastAPI(
    title="AI记账应用API调试版",
    description="智能记账应用的后端API服务调试版",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan,
)

# 基础中间件
print("添加基础中间件...")
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)

@app.get("/")
async def root():
    return {"message": "Hello from debug app v2"}

@app.get("/health")
async def health():
    return {"status": "ok", "message": "服务器运行正常"}

# 添加一个简单的异常处理器来调试
@app.exception_handler(Exception)
async def debug_exception_handler(request, exc):
    import traceback
    error_traceback = traceback.format_exc()
    logger.error(f"异常: {str(exc)}")
    logger.error(f"堆栈: {error_traceback}")
    print(f"ERROR: {str(exc)}")
    print(f"TRACEBACK: {error_traceback}")
    
    from fastapi.responses import JSONResponse
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": f"调试错误: {str(exc)}",
            "traceback": error_traceback
        }
    )

if __name__ == "__main__":
    print("启动调试服务器v2...")
    uvicorn.run(app, host="0.0.0.0", port=8006, log_level="debug")
