import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/api_config.dart';

class SecurityService {
  static const String _baseUrl = '${ApiConfig.baseUrl}/security';
  
  Future<String?> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  Future<Map<String, String>> _getHeaders() async {
    final token = await _getAuthToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// 获取安全监控状态
  Future<Map<String, dynamic>> getSecurityStatus() async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/monitor/status'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['data'] as Map<String, dynamic>;
        } else {
          throw Exception(data['message'] ?? 'Failed to get security status');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to get security status: $e');
    }
  }

  /// 获取安全警报
  Future<List<Map<String, dynamic>>> getSecurityAlerts({
    bool? resolved,
    String? threatLevel,
    int limit = 100,
  }) async {
    try {
      final headers = await _getHeaders();
      final queryParams = <String, String>{
        'limit': limit.toString(),
      };
      
      if (resolved != null) {
        queryParams['resolved'] = resolved.toString();
      }
      
      if (threatLevel != null) {
        queryParams['threat_level'] = threatLevel;
      }

      final uri = Uri.parse('$_baseUrl/monitor/alerts').replace(
        queryParameters: queryParams,
      );

      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return List<Map<String, dynamic>>.from(data['data']);
        } else {
          throw Exception(data['message'] ?? 'Failed to get security alerts');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to get security alerts: $e');
    }
  }

  /// 获取最近的审计事件
  Future<List<Map<String, dynamic>>> getRecentAuditEvents({
    String? eventType,
    int limit = 100,
  }) async {
    try {
      final headers = await _getHeaders();
      final queryParams = <String, String>{
        'limit': limit.toString(),
      };
      
      if (eventType != null) {
        queryParams['event_type'] = eventType;
      }

      final uri = Uri.parse('$_baseUrl/audit/recent').replace(
        queryParameters: queryParams,
      );

      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return List<Map<String, dynamic>>.from(data['data']);
        } else {
          throw Exception(data['message'] ?? 'Failed to get audit events');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to get audit events: $e');
    }
  }

  /// 搜索审计事件
  Future<List<Map<String, dynamic>>> searchAuditEvents({
    DateTime? startTime,
    DateTime? endTime,
    String? eventType,
    String? userId,
    String? level,
    int limit = 100,
  }) async {
    try {
      final headers = await _getHeaders();
      final requestBody = <String, dynamic>{
        'limit': limit,
      };
      
      if (startTime != null) {
        requestBody['start_time'] = startTime.toIso8601String();
      }
      
      if (endTime != null) {
        requestBody['end_time'] = endTime.toIso8601String();
      }
      
      if (eventType != null) {
        requestBody['event_type'] = eventType;
      }
      
      if (userId != null) {
        requestBody['user_id'] = userId;
      }
      
      if (level != null) {
        requestBody['level'] = level;
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/audit/search'),
        headers: headers,
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return List<Map<String, dynamic>>.from(data['data']);
        } else {
          throw Exception(data['message'] ?? 'Failed to search audit events');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to search audit events: $e');
    }
  }

  /// 安排密钥轮换
  Future<Map<String, dynamic>> scheduleKeyRotation({
    required String provider,
    String reason = 'manual',
    int delayHours = 0,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final headers = await _getHeaders();
      final requestBody = {
        'provider': provider,
        'reason': reason,
        'delay_hours': delayHours,
        if (metadata != null) 'metadata': metadata,
      };

      final response = await http.post(
        Uri.parse('$_baseUrl/key-rotation/schedule'),
        headers: headers,
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['data'] as Map<String, dynamic>;
        } else {
          throw Exception(data['message'] ?? 'Failed to schedule key rotation');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to schedule key rotation: $e');
    }
  }

  /// 获取密钥轮换状态
  Future<Map<String, dynamic>> getKeyRotationStatus(String rotationId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/key-rotation/$rotationId/status'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['data'] as Map<String, dynamic>;
        } else {
          throw Exception(data['message'] ?? 'Failed to get rotation status');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to get rotation status: $e');
    }
  }

  /// 取消密钥轮换
  Future<bool> cancelKeyRotation(String rotationId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.delete(
        Uri.parse('$_baseUrl/key-rotation/$rotationId'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'] == true;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to cancel key rotation: $e');
    }
  }

  /// 获取密钥轮换历史
  Future<List<Map<String, dynamic>>> getKeyRotationHistory({
    String? provider,
    int limit = 50,
  }) async {
    try {
      final headers = await _getHeaders();
      final queryParams = <String, String>{
        'limit': limit.toString(),
      };
      
      if (provider != null) {
        queryParams['provider'] = provider;
      }

      final uri = Uri.parse('$_baseUrl/key-rotation/history').replace(
        queryParameters: queryParams,
      );

      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return List<Map<String, dynamic>>.from(data['data']);
        } else {
          throw Exception(data['message'] ?? 'Failed to get rotation history');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to get rotation history: $e');
    }
  }

  /// 分配用户角色
  Future<bool> assignUserRole({
    required String userId,
    required String role,
    DateTime? expiresAt,
  }) async {
    try {
      final headers = await _getHeaders();
      final requestBody = <String, dynamic>{
        'user_id': userId,
        'role': role,
      };
      
      if (expiresAt != null) {
        requestBody['expires_at'] = expiresAt.toIso8601String();
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/access/assign-role'),
        headers: headers,
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'] == true;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to assign user role: $e');
    }
  }

  /// 获取用户权限
  Future<Map<String, dynamic>> getUserPermissions(String userId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/access/permissions/$userId'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['data'] as Map<String, dynamic>;
        } else {
          throw Exception(data['message'] ?? 'Failed to get user permissions');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to get user permissions: $e');
    }
  }

  /// 安全系统健康检查
  Future<Map<String, dynamic>> getSecurityHealth() async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/health'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['data'] as Map<String, dynamic>;
        } else {
          throw Exception(data['message'] ?? 'Security system unhealthy');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to check security health: $e');
    }
  }

  /// 获取安全配置
  Future<Map<String, dynamic>> getSecurityConfig() async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/config'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['data'] as Map<String, dynamic>;
        } else {
          throw Exception(data['message'] ?? 'Failed to get security config');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to get security config: $e');
    }
  }

  /// 更新安全配置
  Future<bool> updateSecurityConfig(Map<String, dynamic> config) async {
    try {
      final headers = await _getHeaders();
      final response = await http.put(
        Uri.parse('$_baseUrl/config'),
        headers: headers,
        body: json.encode(config),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'] == true;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to update security config: $e');
    }
  }

  /// 导出安全报告
  Future<Map<String, dynamic>> exportSecurityReport({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? includeTypes,
  }) async {
    try {
      final headers = await _getHeaders();
      final requestBody = <String, dynamic>{};
      
      if (startDate != null) {
        requestBody['start_date'] = startDate.toIso8601String();
      }
      
      if (endDate != null) {
        requestBody['end_date'] = endDate.toIso8601String();
      }
      
      if (includeTypes != null) {
        requestBody['include_types'] = includeTypes;
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/reports/export'),
        headers: headers,
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['data'] as Map<String, dynamic>;
        } else {
          throw Exception(data['message'] ?? 'Failed to export security report');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to export security report: $e');
    }
  }
}
