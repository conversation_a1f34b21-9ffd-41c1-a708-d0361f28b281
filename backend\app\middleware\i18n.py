"""
国际化中间件
自动检测和设置用户语言偏好
"""

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Optional
import re
from loguru import logger

from ..utils.i18n import get_i18n_manager, SupportedLanguage


class I18nMiddleware(BaseHTTPMiddleware):
    """国际化中间件"""
    
    def __init__(self, app, default_language: SupportedLanguage = SupportedLanguage.CHINESE):
        super().__init__(app)
        self.default_language = default_language
        self.i18n_manager = get_i18n_manager()
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        try:
            # 检测语言偏好
            detected_language = self._detect_language(request)
            
            # 设置语言
            if detected_language:
                self.i18n_manager.set_language(detected_language)
            
            # 在请求状态中保存语言信息
            request.state.language = detected_language or self.default_language
            request.state.i18n_manager = self.i18n_manager
            
            # 处理请求
            response = await call_next(request)
            
            # 在响应头中添加语言信息
            if hasattr(request.state, 'language'):
                response.headers["Content-Language"] = request.state.language.value
            
            return response
            
        except Exception as e:
            logger.error(f"国际化中间件处理失败: {e}")
            # 如果中间件失败，继续处理请求但使用默认语言
            request.state.language = self.default_language
            request.state.i18n_manager = self.i18n_manager
            return await call_next(request)
    
    def _detect_language(self, request: Request) -> Optional[SupportedLanguage]:
        """检测用户语言偏好"""
        
        # 1. 检查URL参数中的语言设置
        lang_param = request.query_params.get('lang') or request.query_params.get('language')
        if lang_param:
            try:
                return SupportedLanguage(lang_param)
            except ValueError:
                pass
        
        # 2. 检查请求头中的语言设置
        accept_language = request.headers.get('Accept-Language')
        if accept_language:
            detected = self._parse_accept_language(accept_language)
            if detected:
                return detected
        
        # 3. 检查自定义语言头
        custom_language = request.headers.get('X-Language') or request.headers.get('X-Locale')
        if custom_language:
            try:
                return SupportedLanguage(custom_language)
            except ValueError:
                pass
        
        # 4. 检查Cookie中的语言设置
        language_cookie = request.cookies.get('language') or request.cookies.get('locale')
        if language_cookie:
            try:
                return SupportedLanguage(language_cookie)
            except ValueError:
                pass
        
        # 5. 根据用户代理检测（简单实现）
        user_agent = request.headers.get('User-Agent', '')
        if user_agent:
            detected = self._detect_from_user_agent(user_agent)
            if detected:
                return detected
        
        return None
    
    def _parse_accept_language(self, accept_language: str) -> Optional[SupportedLanguage]:
        """解析Accept-Language头"""
        try:
            # 解析Accept-Language头，格式如: zh-CN,zh;q=0.9,en;q=0.8
            languages = []
            
            for lang_item in accept_language.split(','):
                lang_item = lang_item.strip()
                
                # 分离语言代码和权重
                if ';q=' in lang_item:
                    lang_code, weight_str = lang_item.split(';q=', 1)
                    try:
                        weight = float(weight_str)
                    except ValueError:
                        weight = 1.0
                else:
                    lang_code = lang_item
                    weight = 1.0
                
                lang_code = lang_code.strip()
                languages.append((lang_code, weight))
            
            # 按权重排序
            languages.sort(key=lambda x: x[1], reverse=True)
            
            # 尝试匹配支持的语言
            for lang_code, _ in languages:
                # 直接匹配
                try:
                    return SupportedLanguage(lang_code)
                except ValueError:
                    pass
                
                # 尝试匹配主语言代码
                main_lang = lang_code.split('-')[0].lower()
                
                if main_lang == 'zh':
                    # 中文处理
                    if 'tw' in lang_code.lower() or 'hk' in lang_code.lower():
                        return SupportedLanguage.TRADITIONAL_CHINESE
                    else:
                        return SupportedLanguage.CHINESE
                elif main_lang == 'en':
                    return SupportedLanguage.ENGLISH
        
        except Exception as e:
            logger.debug(f"解析Accept-Language失败: {e}")
        
        return None
    
    def _detect_from_user_agent(self, user_agent: str) -> Optional[SupportedLanguage]:
        """从User-Agent检测语言（简单实现）"""
        try:
            user_agent_lower = user_agent.lower()
            
            # 检测中文相关标识
            chinese_indicators = [
                'zh-cn', 'zh_cn', 'chinese', 'china',
                'baidu', 'qq', 'wechat', 'weibo'
            ]
            
            for indicator in chinese_indicators:
                if indicator in user_agent_lower:
                    return SupportedLanguage.CHINESE
            
            # 检测繁体中文
            traditional_indicators = [
                'zh-tw', 'zh_tw', 'zh-hk', 'zh_hk', 'taiwan', 'hongkong'
            ]
            
            for indicator in traditional_indicators:
                if indicator in user_agent_lower:
                    return SupportedLanguage.TRADITIONAL_CHINESE
            
        except Exception as e:
            logger.debug(f"从User-Agent检测语言失败: {e}")
        
        return None


def get_request_language(request: Request) -> SupportedLanguage:
    """从请求中获取语言设置"""
    return getattr(request.state, 'language', SupportedLanguage.CHINESE)


def get_request_i18n_manager(request: Request):
    """从请求中获取国际化管理器"""
    return getattr(request.state, 'i18n_manager', get_i18n_manager())


def t_request(request: Request, key: str, **kwargs) -> str:
    """基于请求的翻译函数"""
    try:
        i18n_manager = get_request_i18n_manager(request)
        language = get_request_language(request)
        return i18n_manager.get_text(key, language, **kwargs)
    except Exception as e:
        logger.error(f"请求翻译失败: {e}")
        return key
