"""
上下文感知AI分类系统
基于用户历史数据、时间、地点等上下文信息提供更准确的交易分类
"""

import json
import re
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from collections import defaultdict, Counter
from dataclasses import dataclass
import asyncio

from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from sqlalchemy.orm import selectinload

from .ai_client import AIClientFactory, BaseAIClient
from .ai_config import AIModelConfig, AIProvider
from ..models.category import Category
from ..models.transaction import Transaction
from ..models.user import User
from ..utils.cache import get_cache_manager
from ..utils.structured_logger import structured_logger, LogCategory


@dataclass
class ContextualFeatures:
    """上下文特征"""
    # 时间特征
    hour_of_day: int
    day_of_week: int
    day_of_month: int
    month: int
    is_weekend: bool
    is_holiday: bool
    
    # 历史特征
    similar_transactions: List[Dict[str, Any]]
    user_category_preferences: Dict[str, float]
    recent_category_usage: Dict[str, int]
    
    # 金额特征
    amount_percentile: float
    is_large_amount: bool
    typical_amount_for_category: Dict[str, float]
    
    # 描述特征
    description_keywords: List[str]
    merchant_patterns: List[str]
    
    # 地理特征（如果可用）
    location: Optional[str] = None
    nearby_merchants: List[str] = None


class ContextualAIClassifier:
    """上下文感知AI分类器"""
    
    def __init__(self):
        self.ai_client: Optional[BaseAIClient] = None
        self.cache_ttl = 3600  # 1小时缓存
        
        # 关键词模式
        self.category_keywords = {
            "餐饮": ["餐厅", "饭店", "咖啡", "奶茶", "外卖", "美食", "食堂", "快餐"],
            "交通": ["地铁", "公交", "出租车", "滴滴", "uber", "加油", "停车", "高速"],
            "购物": ["超市", "商场", "淘宝", "京东", "拼多多", "服装", "化妆品"],
            "娱乐": ["电影", "KTV", "游戏", "健身", "旅游", "酒吧", "演出"],
            "医疗": ["医院", "药店", "体检", "牙科", "眼科", "诊所"],
            "教育": ["学费", "培训", "书籍", "课程", "学校"],
            "住房": ["房租", "物业", "水电", "燃气", "宽带", "装修"],
            "通讯": ["话费", "流量", "宽带", "手机"],
        }
        
        # 时间模式
        self.time_patterns = {
            "早餐": (6, 10),
            "午餐": (11, 14),
            "晚餐": (17, 21),
            "夜宵": (21, 24),
            "工作日通勤": (7, 9, [0, 1, 2, 3, 4]),  # 周一到周五
            "周末娱乐": (19, 23, [5, 6]),  # 周六周日
        }
    
    async def initialize(self, ai_config: AIModelConfig):
        """初始化AI客户端"""
        try:
            factory = AIClientFactory()
            self.ai_client = await factory.create_client(ai_config)
            logger.info(f"上下文感知AI分类器初始化成功: {ai_config.provider}")
        except Exception as e:
            logger.error(f"初始化AI客户端失败: {e}")
            raise
    
    async def extract_contextual_features(
        self,
        transaction_description: str,
        amount: float,
        transaction_date: datetime,
        user_id: str,
        db: AsyncSession,
        location: Optional[str] = None
    ) -> ContextualFeatures:
        """提取上下文特征"""
        
        # 时间特征
        hour_of_day = transaction_date.hour
        day_of_week = transaction_date.weekday()
        day_of_month = transaction_date.day
        month = transaction_date.month
        is_weekend = day_of_week >= 5
        is_holiday = await self._is_holiday(transaction_date)
        
        # 获取用户历史数据
        similar_transactions = await self._get_similar_transactions(
            user_id, transaction_description, amount, db
        )
        
        user_category_preferences = await self._get_user_category_preferences(
            user_id, db
        )
        
        recent_category_usage = await self._get_recent_category_usage(
            user_id, db
        )
        
        # 金额特征
        amount_percentile = await self._calculate_amount_percentile(
            user_id, amount, db
        )
        
        is_large_amount = amount_percentile > 0.8
        
        typical_amount_for_category = await self._get_typical_amounts_by_category(
            user_id, db
        )
        
        # 描述特征
        description_keywords = self._extract_keywords(transaction_description)
        merchant_patterns = self._extract_merchant_patterns(transaction_description)
        
        # 地理特征
        nearby_merchants = []
        if location:
            nearby_merchants = await self._get_nearby_merchants(location, db)
        
        return ContextualFeatures(
            hour_of_day=hour_of_day,
            day_of_week=day_of_week,
            day_of_month=day_of_month,
            month=month,
            is_weekend=is_weekend,
            is_holiday=is_holiday,
            similar_transactions=similar_transactions,
            user_category_preferences=user_category_preferences,
            recent_category_usage=recent_category_usage,
            amount_percentile=amount_percentile,
            is_large_amount=is_large_amount,
            typical_amount_for_category=typical_amount_for_category,
            description_keywords=description_keywords,
            merchant_patterns=merchant_patterns,
            location=location,
            nearby_merchants=nearby_merchants
        )
    
    async def classify_with_context(
        self,
        transaction_description: str,
        amount: float,
        transaction_date: datetime,
        available_categories: List[Dict[str, Any]],
        user_id: str,
        db: AsyncSession,
        location: Optional[str] = None
    ) -> Dict[str, Any]:
        """基于上下文进行智能分类"""
        
        if not self.ai_client:
            raise RuntimeError("AI客户端未初始化")
        
        # 提取上下文特征
        features = await self.extract_contextual_features(
            transaction_description, amount, transaction_date, user_id, db, location
        )
        
        # 尝试从缓存获取结果
        cache = await get_cache_manager()
        cache_key = self._generate_context_cache_key(
            user_id, transaction_description, amount, features
        )
        
        cached_result = await cache.get(cache_key)
        if cached_result:
            logger.info(f"使用缓存的上下文分类结果: {transaction_description}")
            return cached_result
        
        # 基于规则的预分类
        rule_based_suggestions = await self._rule_based_classification(
            transaction_description, amount, features, available_categories
        )
        
        # 构建增强的提示词
        enhanced_prompt = await self._build_contextual_prompt(
            transaction_description, amount, features, available_categories,
            rule_based_suggestions
        )
        
        try:
            # 调用AI进行分类
            ai_response = await self.ai_client.categorize_transaction(enhanced_prompt)
            
            # 解析AI响应
            result = await self._parse_ai_response(ai_response, available_categories)
            
            # 应用上下文后处理
            final_result = await self._apply_contextual_post_processing(
                result, features, rule_based_suggestions
            )
            
            # 缓存结果
            await cache.set(cache_key, final_result, self.cache_ttl)
            
            # 记录分类日志
            structured_logger.log_ai_request(
                provider=self.ai_client.provider,
                model=self.ai_client.model,
                operation="contextual_classification",
                duration=0.0,  # 实际应该记录真实时间
                success=True,
                user_id=user_id,
                extra_data={
                    "description": transaction_description,
                    "amount": amount,
                    "predicted_category": final_result.get("category_name"),
                    "confidence": final_result.get("confidence", 0),
                    "context_features_used": len(features.__dict__)
                }
            )
            
            return final_result
            
        except Exception as e:
            logger.error(f"上下文AI分类失败: {e}")
            # 降级到规则分类
            return rule_based_suggestions[0] if rule_based_suggestions else {
                "category_name": "其他",
                "confidence": 0.3,
                "reasoning": f"AI分类失败，使用默认分类: {str(e)}"
            }
    
    def _generate_context_cache_key(
        self, user_id: str, description: str, amount: float, features: ContextualFeatures
    ) -> str:
        """生成上下文缓存键"""
        context_hash = hashlib.md5(
            f"{description}_{amount}_{features.hour_of_day}_{features.day_of_week}_{len(features.similar_transactions)}".encode()
        ).hexdigest()
        return f"contextual_classification:{user_id}:{context_hash}"

    async def _get_similar_transactions(
        self, user_id: str, description: str, amount: float, db: AsyncSession
    ) -> List[Dict[str, Any]]:
        """获取相似交易"""
        try:
            # 查找金额相近的交易
            amount_range = amount * 0.3  # 30%的金额范围

            query = select(Transaction).where(
                and_(
                    Transaction.user_id == user_id,
                    Transaction.amount.between(amount - amount_range, amount + amount_range)
                )
            ).options(selectinload(Transaction.category)).limit(10)

            result = await db.execute(query)
            transactions = result.scalars().all()

            similar_transactions = []
            for trans in transactions:
                # 计算描述相似度
                similarity = self._calculate_text_similarity(description, trans.description)
                if similarity > 0.3:  # 相似度阈值
                    similar_transactions.append({
                        "description": trans.description,
                        "amount": float(trans.amount),
                        "category_name": trans.category.name if trans.category else "未分类",
                        "similarity": similarity,
                        "transaction_date": trans.transaction_date.isoformat()
                    })

            # 按相似度排序
            similar_transactions.sort(key=lambda x: x["similarity"], reverse=True)
            return similar_transactions[:5]  # 返回最相似的5个

        except Exception as e:
            logger.error(f"获取相似交易失败: {e}")
            return []

    async def _get_user_category_preferences(
        self, user_id: str, db: AsyncSession
    ) -> Dict[str, float]:
        """获取用户分类偏好"""
        try:
            # 统计用户各分类的使用频率
            query = select(
                Category.name,
                func.count(Transaction.id).label('count')
            ).join(Transaction).where(
                Transaction.user_id == user_id
            ).group_by(Category.name)

            result = await db.execute(query)
            category_counts = result.all()

            total_transactions = sum(count for _, count in category_counts)
            if total_transactions == 0:
                return {}

            preferences = {}
            for category_name, count in category_counts:
                preferences[category_name] = count / total_transactions

            return preferences

        except Exception as e:
            logger.error(f"获取用户分类偏好失败: {e}")
            return {}

    async def _get_recent_category_usage(
        self, user_id: str, db: AsyncSession, days: int = 30
    ) -> Dict[str, int]:
        """获取最近分类使用情况"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)

            query = select(
                Category.name,
                func.count(Transaction.id).label('count')
            ).join(Transaction).where(
                and_(
                    Transaction.user_id == user_id,
                    Transaction.transaction_date >= cutoff_date
                )
            ).group_by(Category.name)

            result = await db.execute(query)
            recent_usage = {name: count for name, count in result.all()}

            return recent_usage

        except Exception as e:
            logger.error(f"获取最近分类使用情况失败: {e}")
            return {}

    async def _calculate_amount_percentile(
        self, user_id: str, amount: float, db: AsyncSession
    ) -> float:
        """计算金额百分位"""
        try:
            # 获取用户所有交易金额
            query = select(Transaction.amount).where(Transaction.user_id == user_id)
            result = await db.execute(query)
            amounts = [float(amt) for amt in result.scalars().all()]

            if not amounts:
                return 0.5  # 默认中位数

            amounts.sort()
            position = sum(1 for amt in amounts if amt <= amount)
            percentile = position / len(amounts)

            return percentile

        except Exception as e:
            logger.error(f"计算金额百分位失败: {e}")
            return 0.5

    async def _get_typical_amounts_by_category(
        self, user_id: str, db: AsyncSession
    ) -> Dict[str, float]:
        """获取各分类的典型金额"""
        try:
            query = select(
                Category.name,
                func.avg(Transaction.amount).label('avg_amount')
            ).join(Transaction).where(
                Transaction.user_id == user_id
            ).group_by(Category.name)

            result = await db.execute(query)
            typical_amounts = {
                name: float(avg_amount)
                for name, avg_amount in result.all()
            }

            return typical_amounts

        except Exception as e:
            logger.error(f"获取典型金额失败: {e}")
            return {}

    def _extract_keywords(self, description: str) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取
        keywords = []
        description_lower = description.lower()

        for category, category_keywords in self.category_keywords.items():
            for keyword in category_keywords:
                if keyword in description_lower:
                    keywords.append(keyword)

        return keywords

    def _extract_merchant_patterns(self, description: str) -> List[str]:
        """提取商户模式"""
        patterns = []

        # 提取可能的商户名称（简化实现）
        # 通常商户名称在描述的开头或包含特定模式
        words = description.split()
        if len(words) > 0:
            # 第一个词可能是商户名
            patterns.append(words[0])

        # 查找常见的商户模式
        merchant_patterns = [
            r'(\w+店)', r'(\w+餐厅)', r'(\w+超市)', r'(\w+医院)',
            r'(\w+银行)', r'(\w+加油站)', r'(\w+药店)'
        ]

        for pattern in merchant_patterns:
            matches = re.findall(pattern, description)
            patterns.extend(matches)

        return patterns

    async def _get_nearby_merchants(self, location: str, db: AsyncSession) -> List[str]:
        """获取附近商户（简化实现）"""
        # 这里应该集成地理位置服务，暂时返回空列表
        return []

    async def _is_holiday(self, date: datetime) -> bool:
        """检查是否为节假日（简化实现）"""
        # 这里应该集成节假日API，暂时只检查一些固定节日
        holidays = [
            (1, 1),   # 元旦
            (2, 14),  # 情人节
            (5, 1),   # 劳动节
            (10, 1),  # 国庆节
            (12, 25), # 圣诞节
        ]

        return (date.month, date.day) in holidays

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简化实现）"""
        # 使用简单的词汇重叠度计算相似度
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    async def _rule_based_classification(
        self,
        description: str,
        amount: float,
        features: ContextualFeatures,
        available_categories: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """基于规则的分类"""
        suggestions = []

        # 基于关键词的分类
        for keyword in features.description_keywords:
            for category, category_keywords in self.category_keywords.items():
                if keyword in category_keywords:
                    # 检查分类是否在可用分类中
                    for cat in available_categories:
                        if cat["name"] == category:
                            suggestions.append({
                                "category_name": category,
                                "confidence": 0.7,
                                "reasoning": f"基于关键词 '{keyword}' 的规则分类"
                            })
                            break

        # 基于时间模式的分类
        hour = features.hour_of_day
        day_of_week = features.day_of_week

        # 餐饮时间模式
        if 6 <= hour <= 10:
            suggestions.append({
                "category_name": "餐饮",
                "confidence": 0.6,
                "reasoning": "早餐时间段"
            })
        elif 11 <= hour <= 14:
            suggestions.append({
                "category_name": "餐饮",
                "confidence": 0.7,
                "reasoning": "午餐时间段"
            })
        elif 17 <= hour <= 21:
            suggestions.append({
                "category_name": "餐饮",
                "confidence": 0.7,
                "reasoning": "晚餐时间段"
            })

        # 通勤时间模式
        if day_of_week < 5 and (7 <= hour <= 9 or 17 <= hour <= 19):
            suggestions.append({
                "category_name": "交通",
                "confidence": 0.6,
                "reasoning": "工作日通勤时间"
            })

        # 基于历史相似交易
        for similar_trans in features.similar_transactions[:3]:  # 只考虑最相似的3个
            if similar_trans["similarity"] > 0.7:
                suggestions.append({
                    "category_name": similar_trans["category_name"],
                    "confidence": 0.8 * similar_trans["similarity"],
                    "reasoning": f"基于相似历史交易: {similar_trans['description']}"
                })

        # 基于金额特征
        if features.is_large_amount:
            # 大额支出通常是特殊分类
            large_amount_categories = ["住房", "医疗", "教育", "购物"]
            for cat_name in large_amount_categories:
                if cat_name in features.user_category_preferences:
                    suggestions.append({
                        "category_name": cat_name,
                        "confidence": 0.5,
                        "reasoning": "大额支出模式"
                    })

        # 去重并按置信度排序
        unique_suggestions = {}
        for suggestion in suggestions:
            cat_name = suggestion["category_name"]
            if cat_name not in unique_suggestions or suggestion["confidence"] > unique_suggestions[cat_name]["confidence"]:
                unique_suggestions[cat_name] = suggestion

        result = list(unique_suggestions.values())
        result.sort(key=lambda x: x["confidence"], reverse=True)

        return result[:3]  # 返回前3个建议

    async def _build_contextual_prompt(
        self,
        description: str,
        amount: float,
        features: ContextualFeatures,
        available_categories: List[Dict[str, Any]],
        rule_suggestions: List[Dict[str, Any]]
    ) -> str:
        """构建上下文增强的提示词"""

        # 基础信息
        prompt = f"""请对以下交易进行智能分类：

交易描述：{description}
交易金额：{amount}元
交易时间：{features.hour_of_day}点，星期{features.day_of_week + 1}
是否周末：{'是' if features.is_weekend else '否'}
是否节假日：{'是' if features.is_holiday else '否'}

"""

        # 上下文信息
        if features.similar_transactions:
            prompt += "历史相似交易：\n"
            for trans in features.similar_transactions[:3]:
                prompt += f"- {trans['description']} ({trans['amount']}元) -> {trans['category_name']} (相似度: {trans['similarity']:.2f})\n"
            prompt += "\n"

        if features.user_category_preferences:
            prompt += "用户分类偏好（使用频率）：\n"
            sorted_prefs = sorted(features.user_category_preferences.items(), key=lambda x: x[1], reverse=True)
            for cat_name, freq in sorted_prefs[:5]:
                prompt += f"- {cat_name}: {freq:.1%}\n"
            prompt += "\n"

        if features.description_keywords:
            prompt += f"识别的关键词：{', '.join(features.description_keywords)}\n\n"

        if features.amount_percentile:
            prompt += f"金额百分位：{features.amount_percentile:.1%}（{'大额' if features.is_large_amount else '正常'}支出）\n\n"

        # 规则建议
        if rule_suggestions:
            prompt += "基于规则的分类建议：\n"
            for suggestion in rule_suggestions:
                prompt += f"- {suggestion['category_name']} (置信度: {suggestion['confidence']:.1%}) - {suggestion['reasoning']}\n"
            prompt += "\n"

        # 可用分类
        prompt += "可选分类：\n"
        for category in available_categories:
            prompt += f"- {category['name']}: {category.get('description', '')}\n"

        prompt += """
请基于以上所有信息，选择最合适的分类，并提供：
1. 分类名称
2. 置信度（0-1）
3. 分类理由

请以JSON格式返回：
{
    "category_name": "分类名称",
    "confidence": 0.85,
    "reasoning": "详细的分类理由"
}"""

        return prompt

    async def _parse_ai_response(
        self, ai_response: str, available_categories: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """解析AI响应"""
        try:
            # 尝试解析JSON响应
            if ai_response.strip().startswith('{'):
                result = json.loads(ai_response)
            else:
                # 如果不是JSON格式，尝试从文本中提取信息
                result = self._extract_from_text_response(ai_response)

            # 验证分类名称是否有效
            category_name = result.get("category_name", "其他")
            valid_categories = [cat["name"] for cat in available_categories]

            if category_name not in valid_categories:
                # 尝试找到最相似的分类
                category_name = self._find_closest_category(category_name, valid_categories)

            return {
                "category_name": category_name,
                "confidence": min(max(result.get("confidence", 0.5), 0.0), 1.0),
                "reasoning": result.get("reasoning", "AI自动分类")
            }

        except Exception as e:
            logger.error(f"解析AI响应失败: {e}")
            return {
                "category_name": "其他",
                "confidence": 0.3,
                "reasoning": f"解析失败: {str(e)}"
            }

    def _extract_from_text_response(self, text: str) -> Dict[str, Any]:
        """从文本响应中提取分类信息"""
        result = {
            "category_name": "其他",
            "confidence": 0.5,
            "reasoning": "文本解析"
        }

        # 简单的文本解析逻辑
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if '分类' in line or 'category' in line.lower():
                # 提取分类名称
                for category_name in self.category_keywords.keys():
                    if category_name in line:
                        result["category_name"] = category_name
                        break
            elif '置信度' in line or 'confidence' in line.lower():
                # 提取置信度
                import re
                confidence_match = re.search(r'(\d+\.?\d*)%?', line)
                if confidence_match:
                    confidence = float(confidence_match.group(1))
                    if confidence > 1:
                        confidence /= 100  # 转换百分比
                    result["confidence"] = confidence

        return result

    def _find_closest_category(self, category_name: str, valid_categories: List[str]) -> str:
        """找到最相似的有效分类"""
        if not valid_categories:
            return "其他"

        # 计算与每个有效分类的相似度
        similarities = []
        for valid_cat in valid_categories:
            similarity = self._calculate_text_similarity(category_name, valid_cat)
            similarities.append((valid_cat, similarity))

        # 返回相似度最高的分类
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[0][0]

    async def _apply_contextual_post_processing(
        self,
        ai_result: Dict[str, Any],
        features: ContextualFeatures,
        rule_suggestions: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """应用上下文后处理"""

        # 如果AI置信度较低，考虑规则建议
        if ai_result["confidence"] < 0.6 and rule_suggestions:
            best_rule = rule_suggestions[0]
            if best_rule["confidence"] > ai_result["confidence"]:
                return {
                    "category_name": best_rule["category_name"],
                    "confidence": best_rule["confidence"],
                    "reasoning": f"AI置信度较低，采用规则分类: {best_rule['reasoning']}"
                }

        # 基于用户偏好调整置信度
        category_name = ai_result["category_name"]
        if category_name in features.user_category_preferences:
            preference_boost = features.user_category_preferences[category_name] * 0.2
            ai_result["confidence"] = min(ai_result["confidence"] + preference_boost, 1.0)
            ai_result["reasoning"] += f" (基于用户偏好调整: +{preference_boost:.1%})"

        # 基于最近使用情况调整
        if category_name in features.recent_category_usage:
            recent_usage = features.recent_category_usage[category_name]
            if recent_usage > 5:  # 最近经常使用
                usage_boost = min(recent_usage / 50, 0.1)  # 最多提升10%
                ai_result["confidence"] = min(ai_result["confidence"] + usage_boost, 1.0)
                ai_result["reasoning"] += f" (最近常用调整: +{usage_boost:.1%})"

        return ai_result

    async def get_classification_explanation(
        self,
        transaction_description: str,
        amount: float,
        transaction_date: datetime,
        predicted_category: str,
        user_id: str,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """获取分类解释"""

        features = await self.extract_contextual_features(
            transaction_description, amount, transaction_date, user_id, db
        )

        explanation = {
            "predicted_category": predicted_category,
            "factors": [],
            "confidence_factors": [],
            "alternative_suggestions": []
        }

        # 分析影响因素
        if features.description_keywords:
            explanation["factors"].append({
                "type": "关键词匹配",
                "details": f"识别到关键词: {', '.join(features.description_keywords)}",
                "impact": "高"
            })

        if features.similar_transactions:
            explanation["factors"].append({
                "type": "历史相似交易",
                "details": f"找到{len(features.similar_transactions)}个相似交易",
                "impact": "高"
            })

        # 时间因素
        time_factor = self._analyze_time_factor(features)
        if time_factor:
            explanation["factors"].append(time_factor)

        # 金额因素
        if features.is_large_amount:
            explanation["factors"].append({
                "type": "金额特征",
                "details": f"大额支出 (第{features.amount_percentile:.0%}百分位)",
                "impact": "中"
            })

        return explanation

    def _analyze_time_factor(self, features: ContextualFeatures) -> Optional[Dict[str, Any]]:
        """分析时间因素"""
        hour = features.hour_of_day

        if 6 <= hour <= 10:
            return {
                "type": "时间模式",
                "details": "早餐时间段，可能是餐饮消费",
                "impact": "中"
            }
        elif 11 <= hour <= 14:
            return {
                "type": "时间模式",
                "details": "午餐时间段，可能是餐饮消费",
                "impact": "中"
            }
        elif 17 <= hour <= 21:
            return {
                "type": "时间模式",
                "details": "晚餐时间段，可能是餐饮消费",
                "impact": "中"
            }
        elif features.is_weekend and 19 <= hour <= 23:
            return {
                "type": "时间模式",
                "details": "周末晚上，可能是娱乐消费",
                "impact": "中"
            }

        return None
