@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    AI记账报销软件 - 后端服务启动脚本
echo ========================================
echo.

echo 📍 当前目录: %CD%
echo.

echo 🔧 激活虚拟环境...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ 虚拟环境激活失败！
    echo 请确保虚拟环境已正确创建。
    pause
    exit /b 1
)

echo ✅ 虚拟环境已激活
echo.

echo 🚀 启动后端服务...
echo 📖 API文档地址: http://localhost:8000/docs
echo 🔧 ReDoc文档地址: http://localhost:8000/redoc
echo 💡 按 Ctrl+C 停止服务
echo.

python -m app.main

echo.
echo 👋 服务已停止
pause
