"""
Redis缓存管理器
提供智能缓存功能，优化AI分类性能
"""

import json
import hashlib
import asyncio
from typing import Any, Optional, Dict, List, Union
from datetime import datetime, timedelta
import redis.asyncio as redis
from loguru import logger
import os


class CacheManager:
    """Redis缓存管理器"""
    
    def __init__(self, redis_url: Optional[str] = None):
        """
        初始化缓存管理器
        
        Args:
            redis_url: Redis连接URL，如果不提供则从环境变量获取
        """
        self.redis_url = redis_url or os.getenv("REDIS_URL", "redis://localhost:6379/0")
        self.redis_client: Optional[redis.Redis] = None
        self.is_connected = False
        
        # 缓存配置
        self.default_ttl = 3600  # 默认1小时过期
        self.categorization_ttl = 7200  # 分类结果缓存2小时
        self.user_pattern_ttl = 86400  # 用户模式缓存24小时
        self.ai_response_ttl = 1800  # AI响应缓存30分钟
    
    async def connect(self):
        """连接Redis"""
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # 测试连接
            await self.redis_client.ping()
            self.is_connected = True
            logger.info("Redis缓存连接成功")
            
        except Exception as e:
            logger.warning(f"Redis连接失败，将使用内存缓存: {e}")
            self.is_connected = False
            # 使用内存缓存作为备选方案
            self._memory_cache: Dict[str, Dict] = {}
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.redis_client:
            await self.redis_client.close()
            self.is_connected = False
            logger.info("Redis连接已断开")
    
    def _generate_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """
        生成缓存键
        
        Args:
            prefix: 键前缀
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            缓存键
        """
        # 创建唯一标识符
        key_data = {
            "args": args,
            "kwargs": sorted(kwargs.items())
        }
        key_str = json.dumps(key_data, sort_keys=True, ensure_ascii=False)
        key_hash = hashlib.md5(key_str.encode()).hexdigest()[:12]
        
        return f"ai_accounting:{prefix}:{key_hash}"
    
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值

        Args:
            key: 缓存键

        Returns:
            缓存值，如果不存在返回None
        """
        try:
            # 如果未连接，尝试连接
            if not self.is_connected:
                await self.connect()

            if self.is_connected and self.redis_client:
                value = await self.redis_client.get(key)
                if value:
                    return json.loads(value)
            else:
                # 使用内存缓存
                cache_item = self._memory_cache.get(key)
                if cache_item and cache_item["expires"] > datetime.now():
                    return cache_item["value"]
                elif cache_item:
                    # 过期了，删除
                    del self._memory_cache[key]
            
            return None
            
        except Exception as e:
            logger.error(f"获取缓存失败 {key}: {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒），如果不提供使用默认值
            
        Returns:
            是否设置成功
        """
        try:
            ttl = ttl or self.default_ttl
            value_str = json.dumps(value, ensure_ascii=False, default=str)
            
            if self.is_connected and self.redis_client:
                await self.redis_client.setex(key, ttl, value_str)
            else:
                # 使用内存缓存
                self._memory_cache[key] = {
                    "value": value,
                    "expires": datetime.now() + timedelta(seconds=ttl)
                }
            
            return True
            
        except Exception as e:
            logger.error(f"设置缓存失败 {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """
        删除缓存
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        try:
            if self.is_connected and self.redis_client:
                await self.redis_client.delete(key)
            else:
                self._memory_cache.pop(key, None)
            
            return True
            
        except Exception as e:
            logger.error(f"删除缓存失败 {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """
        检查缓存是否存在
        
        Args:
            key: 缓存键
            
        Returns:
            是否存在
        """
        try:
            if self.is_connected and self.redis_client:
                return await self.redis_client.exists(key) > 0
            else:
                cache_item = self._memory_cache.get(key)
                return cache_item is not None and cache_item["expires"] > datetime.now()
            
        except Exception as e:
            logger.error(f"检查缓存存在性失败 {key}: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """
        清除匹配模式的缓存
        
        Args:
            pattern: 匹配模式
            
        Returns:
            清除的缓存数量
        """
        try:
            count = 0
            if self.is_connected and self.redis_client:
                keys = await self.redis_client.keys(pattern)
                if keys:
                    count = await self.redis_client.delete(*keys)
            else:
                # 内存缓存模式匹配
                import fnmatch
                keys_to_delete = [
                    key for key in self._memory_cache.keys()
                    if fnmatch.fnmatch(key, pattern)
                ]
                for key in keys_to_delete:
                    del self._memory_cache[key]
                count = len(keys_to_delete)
            
            logger.info(f"清除缓存模式 {pattern}: {count} 个键")
            return count
            
        except Exception as e:
            logger.error(f"清除缓存模式失败 {pattern}: {e}")
            return 0
    
    # 专用缓存方法
    
    async def cache_categorization_result(
        self, 
        user_id: str, 
        description: str, 
        amount: float,
        transaction_type: str,
        result: Dict[str, Any]
    ) -> bool:
        """缓存分类结果"""
        key = self._generate_cache_key(
            "categorization",
            user_id=user_id,
            description=description.lower().strip(),
            amount=amount,
            transaction_type=transaction_type
        )
        return await self.set(key, result, self.categorization_ttl)
    
    async def get_categorization_result(
        self,
        user_id: str,
        description: str,
        amount: float,
        transaction_type: str
    ) -> Optional[Dict[str, Any]]:
        """获取缓存的分类结果"""
        key = self._generate_cache_key(
            "categorization",
            user_id=user_id,
            description=description.lower().strip(),
            amount=amount,
            transaction_type=transaction_type
        )
        return await self.get(key)
    
    async def cache_user_patterns(self, user_id: str, patterns: Dict[str, Any]) -> bool:
        """缓存用户消费模式"""
        key = self._generate_cache_key("user_patterns", user_id=user_id)
        return await self.set(key, patterns, self.user_pattern_ttl)
    
    async def get_user_patterns(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户消费模式"""
        key = self._generate_cache_key("user_patterns", user_id=user_id)
        return await self.get(key)
    
    async def cache_ai_response(
        self,
        provider: str,
        model: str,
        prompt_hash: str,
        response: Dict[str, Any]
    ) -> bool:
        """缓存AI响应"""
        key = self._generate_cache_key(
            "ai_response",
            provider=provider,
            model=model,
            prompt_hash=prompt_hash
        )
        return await self.set(key, response, self.ai_response_ttl)
    
    async def get_ai_response(
        self,
        provider: str,
        model: str,
        prompt_hash: str
    ) -> Optional[Dict[str, Any]]:
        """获取缓存的AI响应"""
        key = self._generate_cache_key(
            "ai_response",
            provider=provider,
            model=model,
            prompt_hash=prompt_hash
        )
        return await self.get(key)
    
    async def invalidate_user_cache(self, user_id: str) -> int:
        """清除用户相关的所有缓存"""
        pattern = f"ai_accounting:*:*user_id*{user_id}*"
        return await self.clear_pattern(pattern)
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            if self.is_connected and self.redis_client:
                info = await self.redis_client.info()
                return {
                    "connected": True,
                    "used_memory": info.get("used_memory_human", "N/A"),
                    "connected_clients": info.get("connected_clients", 0),
                    "total_commands_processed": info.get("total_commands_processed", 0),
                    "keyspace_hits": info.get("keyspace_hits", 0),
                    "keyspace_misses": info.get("keyspace_misses", 0),
                }
            else:
                return {
                    "connected": False,
                    "memory_cache_size": len(self._memory_cache),
                    "cache_type": "memory"
                }
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {"error": str(e)}


# 全局缓存管理器实例
_cache_manager: Optional[CacheManager] = None


def get_cache_manager_sync() -> CacheManager:
    """获取全局缓存管理器实例（同步版本）"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = CacheManager()
        # 注意：这里不连接Redis，将在第一次使用时连接
    return _cache_manager

async def get_cache_manager() -> CacheManager:
    """获取全局缓存管理器实例"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = CacheManager()
        await _cache_manager.connect()
    return _cache_manager


async def close_cache_manager():
    """关闭全局缓存管理器"""
    global _cache_manager
    if _cache_manager:
        await _cache_manager.disconnect()
        _cache_manager = None


# 装饰器函数
def cache_result(ttl: int = 3600, key_prefix: str = "default"):
    """
    缓存装饰器
    
    Args:
        ttl: 缓存过期时间（秒）
        key_prefix: 缓存键前缀
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            cache = await get_cache_manager()
            
            # 生成缓存键
            cache_key = cache._generate_cache_key(key_prefix, *args, **kwargs)
            
            # 尝试从缓存获取
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {cache_key}")
                return cached_result
            
            # 执行函数
            result = await func(*args, **kwargs)
            
            # 缓存结果
            await cache.set(cache_key, result, ttl)
            logger.debug(f"缓存设置: {cache_key}")
            
            return result
        
        return wrapper
    return decorator
