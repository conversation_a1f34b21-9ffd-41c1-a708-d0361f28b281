"""
简化的监控API端点 - 用于测试
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Response
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from pydantic import BaseModel
from ..auth.dependencies import get_current_user
from ..models.user import User
from ..utils.performance_monitor import performance_monitor
from ..utils.metrics_collector import metrics_collector
from ..utils.structured_logger import structured_logger, LogCategory, LogLevel
import json

router = APIRouter()


class HealthResponse(BaseModel):
    status: str
    timestamp: float
    uptime: float
    cpu_percent: float
    memory_percent: float
    disk_usage: float
    active_connections: int
    response_time_avg: float
    error_rate: float


@router.get("/health", response_model=HealthResponse)
async def get_system_health():
    """获取系统健康状态"""
    try:
        health = await performance_monitor.get_system_health()
        
        return HealthResponse(
            status=health.status,
            timestamp=datetime.now().timestamp(),
            uptime=health.uptime,
            cpu_percent=health.cpu_percent,
            memory_percent=health.memory_percent,
            disk_usage=health.disk_usage,
            active_connections=health.active_connections,
            response_time_avg=health.response_time_avg,
            error_rate=health.error_rate
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统健康状态失败: {str(e)}")


@router.get("/metrics")
async def get_system_metrics(
    format: str = Query("json", description="输出格式: json 或 prometheus"),
    current_user: User = Depends(get_current_user)
):
    """获取系统指标"""
    try:
        if format == "prometheus":
            prometheus_data = metrics_collector.export_prometheus_format()
            return Response(
                content=prometheus_data,
                media_type="text/plain; version=0.0.4; charset=utf-8"
            )
        else:
            # JSON格式
            samples = metrics_collector.collect_all_samples()
            metrics_data = {}
            
            for sample in samples:
                metric_name = sample.name
                if metric_name not in metrics_data:
                    metrics_data[metric_name] = {
                        "type": sample.type,
                        "help": sample.help,
                        "samples": []
                    }
                
                metrics_data[metric_name]["samples"].append({
                    "labels": sample.labels,
                    "value": sample.value,
                    "timestamp": sample.timestamp
                })
            
            return {
                "metrics": metrics_data,
                "timestamp": datetime.now().isoformat(),
                "total_metrics": len(metrics_data)
            }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统指标失败: {str(e)}")


@router.get("/performance/overview")
async def get_performance_overview(current_user: User = Depends(get_current_user)):
    """获取性能概览"""
    try:
        # 获取系统健康状态
        health = await performance_monitor.get_system_health()
        
        # 获取缓存统计
        cache_stats = await performance_monitor.get_cache_stats()
        
        # 获取数据库统计
        db_stats = await performance_monitor.get_database_stats()
        
        return {
            "system_health": {
                "status": health.status,
                "uptime": health.uptime,
                "cpu_percent": health.cpu_percent,
                "memory_percent": health.memory_percent,
                "disk_usage": health.disk_usage,
                "active_connections": health.active_connections,
                "response_time_avg": health.response_time_avg,
                "error_rate": health.error_rate
            },
            "cache_stats": cache_stats,
            "database_stats": db_stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取性能概览失败: {str(e)}")


@router.get("/logs/search")
async def search_logs(
    category: Optional[str] = Query(None, description="日志分类"),
    level: Optional[str] = Query(None, description="日志级别"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    user_id: Optional[str] = Query(None, description="用户ID"),
    limit: int = Query(100, description="返回条数限制"),
    current_user: User = Depends(get_current_user)
):
    """搜索日志"""
    try:
        # 转换参数
        log_category = None
        if category:
            try:
                log_category = LogCategory(category)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"无效的日志分类: {category}")
        
        log_level = None
        if level:
            try:
                log_level = LogLevel(level)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"无效的日志级别: {level}")
        
        # 搜索日志
        logs = await structured_logger.search_logs(
            category=log_category,
            level=log_level,
            start_time=start_time,
            end_time=end_time,
            user_id=user_id,
            limit=limit
        )
        
        return {
            "logs": logs,
            "total": len(logs),
            "filters": {
                "category": category,
                "level": level,
                "start_time": start_time.isoformat() if start_time else None,
                "end_time": end_time.isoformat() if end_time else None,
                "user_id": user_id,
                "limit": limit
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索日志失败: {str(e)}")


@router.get("/logs/statistics")
async def get_log_statistics(
    hours: int = Query(24, description="统计时间范围（小时）"),
    current_user: User = Depends(get_current_user)
):
    """获取日志统计信息"""
    try:
        stats = await structured_logger.get_log_statistics(hours)
        return stats
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取日志统计失败: {str(e)}")


@router.get("/dashboard/data")
async def get_dashboard_data(current_user: User = Depends(get_current_user)):
    """获取监控仪表板数据"""
    try:
        # 获取系统健康状态
        health = await performance_monitor.get_system_health()
        
        # 获取性能趋势
        trends = await performance_monitor.get_performance_trends(hours=24)
        
        # 获取端点统计
        endpoint_stats = await performance_monitor.get_endpoint_stats()
        
        # 获取缓存统计
        cache_stats = await performance_monitor.get_cache_stats()
        
        # 获取数据库统计
        db_stats = await performance_monitor.get_database_stats()
        
        return {
            "system_health": {
                "status": health.status,
                "uptime": health.uptime,
                "cpu_percent": health.cpu_percent,
                "memory_percent": health.memory_percent,
                "disk_usage": health.disk_usage,
                "active_connections": health.active_connections,
                "response_time_avg": health.response_time_avg,
                "error_rate": health.error_rate
            },
            "performance_trends": trends,
            "endpoint_stats": endpoint_stats,
            "cache_stats": cache_stats,
            "database_stats": db_stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取仪表板数据失败: {str(e)}")
