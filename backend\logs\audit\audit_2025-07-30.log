{
  "id": "4d3c4df7c37ad3e15f1825be3575dc0d",
  "timestamp": "2025-07-30T09:37:18.239967",
  "event_type": "system_error",
  "level": "high",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "middleware_error",
  "details": {
    "error": "'coroutine' object has no attribute 'get'",
    "endpoint": "/health",
    "method": "GET"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "********************************",
  "timestamp": "2025-07-30T09:42:35.485797",
  "event_type": "system_error",
  "level": "high",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "middleware_error",
  "details": {
    "error": "'coroutine' object has no attribute 'get'",
    "endpoint": "/health",
    "method": "GET"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "cfe57493c9bee301324d84e438c011ae",
  "timestamp": "2025-07-30T10:08:21.562609",
  "event_type": "api_access",
  "level": "low",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "api_request",
  "details": {
    "method": "GET",
    "path": "/",
    "query_params": {},
    "status_code": 200,
    "duration_ms": 4030.33,
    "user_id": null,
    "ip_address": "127.0.0.1",
    "user_agent": "python-httpx/0.28.1",
    "timestamp": "2025-07-30T10:08:21.562609"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "fec65cd78d9f2364e923633eace6518b",
  "timestamp": "2025-07-30T10:08:42.079338",
  "event_type": "api_access",
  "level": "low",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "api_request",
  "details": {
    "method": "GET",
    "path": "/",
    "query_params": {},
    "status_code": 200,
    "duration_ms": 4014.44,
    "user_id": null,
    "ip_address": "127.0.0.1",
    "user_agent": "python-httpx/0.28.1",
    "timestamp": "2025-07-30T10:08:42.079338"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "9a4c2c3c70cfece9546a9b7536e62a4e",
  "timestamp": "2025-07-30T10:13:46.496939",
  "event_type": "api_access",
  "level": "low",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "api_request",
  "details": {
    "method": "GET",
    "path": "/",
    "query_params": {},
    "status_code": 200,
    "duration_ms": 4025.23,
    "user_id": null,
    "ip_address": "127.0.0.1",
    "user_agent": "python-httpx/0.28.1",
    "timestamp": "2025-07-30T10:13:46.496939"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "a9e610e95d8a2c9208114a99a7bcfa24",
  "timestamp": "2025-07-30T10:33:32.393608",
  "event_type": "api_access",
  "level": "low",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "api_request",
  "details": {
    "method": "POST",
    "path": "/api/v1/auth/register",
    "query_params": {},
    "status_code": 400,
    "duration_ms": 4072.29,
    "user_id": null,
    "ip_address": "127.0.0.1",
    "user_agent": "python-httpx/0.28.1",
    "timestamp": "2025-07-30T10:33:32.393608"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "07dfce0aba39e6d9e8d00adb15b3d743",
  "timestamp": "2025-07-30T10:33:36.406277",
  "event_type": "api_error",
  "level": "medium",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "api_error",
  "details": {
    "method": "POST",
    "path": "/api/v1/auth/register",
    "query_params": {},
    "status_code": 400,
    "duration_ms": 4072.29,
    "user_id": null,
    "ip_address": "127.0.0.1",
    "user_agent": "python-httpx/0.28.1",
    "timestamp": "2025-07-30T10:33:32.393608"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "bed35a0cbc3efe8e2088051f2de7884c",
  "timestamp": "2025-07-30T10:33:45.672297",
  "event_type": "api_access",
  "level": "low",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "api_request",
  "details": {
    "method": "POST",
    "path": "/api/v1/auth/register",
    "query_params": {},
    "status_code": 400,
    "duration_ms": 4018.95,
    "user_id": null,
    "ip_address": "127.0.0.1",
    "user_agent": "python-httpx/0.28.1",
    "timestamp": "2025-07-30T10:33:45.672297"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "3c7ca02414dd37504030f9d4fae74910",
  "timestamp": "2025-07-30T10:33:49.679162",
  "event_type": "api_error",
  "level": "medium",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "api_error",
  "details": {
    "method": "POST",
    "path": "/api/v1/auth/register",
    "query_params": {},
    "status_code": 400,
    "duration_ms": 4018.95,
    "user_id": null,
    "ip_address": "127.0.0.1",
    "user_agent": "python-httpx/0.28.1",
    "timestamp": "2025-07-30T10:33:45.672297"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "b2b6d3c9bfa0029bfff58a0d4c24526a",
  "timestamp": "2025-07-30T10:34:25.912130",
  "event_type": "api_access",
  "level": "low",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "api_request",
  "details": {
    "method": "POST",
    "path": "/api/v1/auth/register",
    "query_params": {},
    "status_code": 400,
    "duration_ms": 4025.8,
    "user_id": null,
    "ip_address": "127.0.0.1",
    "user_agent": "python-httpx/0.28.1",
    "timestamp": "2025-07-30T10:34:25.912130"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "6c293e216f28824dea2930bb58b8e434",
  "timestamp": "2025-07-30T10:34:29.924388",
  "event_type": "api_error",
  "level": "medium",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "api_error",
  "details": {
    "method": "POST",
    "path": "/api/v1/auth/register",
    "query_params": {},
    "status_code": 400,
    "duration_ms": 4025.8,
    "user_id": null,
    "ip_address": "127.0.0.1",
    "user_agent": "python-httpx/0.28.1",
    "timestamp": "2025-07-30T10:34:25.912130"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "a17e8ff075afea35a7d96da0d3fbb658",
  "timestamp": "2025-07-30T10:35:19.068837",
  "event_type": "api_access",
  "level": "low",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "api_request",
  "details": {
    "method": "POST",
    "path": "/api/v1/auth/register",
    "query_params": {},
    "status_code": 400,
    "duration_ms": 4033.85,
    "user_id": null,
    "ip_address": "127.0.0.1",
    "user_agent": "python-httpx/0.28.1",
    "timestamp": "2025-07-30T10:35:19.068837"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "e9bba2c2cbea26340db3097ad238f27b",
  "timestamp": "2025-07-30T10:35:23.079335",
  "event_type": "api_error",
  "level": "medium",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "api_error",
  "details": {
    "method": "POST",
    "path": "/api/v1/auth/register",
    "query_params": {},
    "status_code": 400,
    "duration_ms": 4033.85,
    "user_id": null,
    "ip_address": "127.0.0.1",
    "user_agent": "python-httpx/0.28.1",
    "timestamp": "2025-07-30T10:35:19.068837"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "fe2927d39c750e6fb6de45419b9fe04f",
  "timestamp": "2025-07-30T10:35:31.413281",
  "event_type": "api_access",
  "level": "low",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "api_request",
  "details": {
    "method": "POST",
    "path": "/api/v1/auth/login",
    "query_params": {},
    "status_code": 200,
    "duration_ms": 4317.7,
    "user_id": null,
    "ip_address": "127.0.0.1",
    "user_agent": "python-httpx/0.28.1",
    "timestamp": "2025-07-30T10:35:31.413281"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "fde856f735808cae47549f487c573e80",
  "timestamp": "2025-07-30T10:35:39.663234",
  "event_type": "api_access",
  "level": "low",
  "user_id": "cc80f617-b43c-4746-97f2-e6c22edf2d50",
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "api_request",
  "details": {
    "method": "POST",
    "path": "/api/v1/batch/transactions",
    "query_params": {},
    "status_code": 500,
    "duration_ms": 4238.78,
    "user_id": "cc80f617-b43c-4746-97f2-e6c22edf2d50",
    "ip_address": "127.0.0.1",
    "user_agent": "python-httpx/0.28.1",
    "timestamp": "2025-07-30T10:35:39.663234"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "5babbffa6a0b93fc3335747ad7093948",
  "timestamp": "2025-07-30T10:35:43.671251",
  "event_type": "api_error",
  "level": "medium",
  "user_id": "cc80f617-b43c-4746-97f2-e6c22edf2d50",
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "api_error",
  "details": {
    "method": "POST",
    "path": "/api/v1/batch/transactions",
    "query_params": {},
    "status_code": 500,
    "duration_ms": 4238.78,
    "user_id": "cc80f617-b43c-4746-97f2-e6c22edf2d50",
    "ip_address": "127.0.0.1",
    "user_agent": "python-httpx/0.28.1",
    "timestamp": "2025-07-30T10:35:39.663234"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "0d660a1bc616cdbf20d42c4a8410fa9e",
  "timestamp": "2025-07-30T10:36:03.604361",
  "event_type": "api_access",
  "level": "low",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "api_request",
  "details": {
    "method": "GET",
    "path": "/",
    "query_params": {},
    "status_code": 200,
    "duration_ms": 4025.05,
    "user_id": null,
    "ip_address": "127.0.0.1",
    "user_agent": "python-httpx/0.28.1",
    "timestamp": "2025-07-30T10:36:03.604361"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
{
  "id": "298904d2d02dde655e71b18b5cabe8ca",
  "timestamp": "2025-07-30T10:36:11.936394",
  "event_type": "api_access",
  "level": "low",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "api_request",
  "details": {
    "method": "POST",
    "path": "/api/v1/auth/login",
    "query_params": {},
    "status_code": 200,
    "duration_ms": 4324.18,
    "user_id": null,
    "ip_address": "127.0.0.1",
    "user_agent": "python-httpx/0.28.1",
    "timestamp": "2025-07-30T10:36:11.936394"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
