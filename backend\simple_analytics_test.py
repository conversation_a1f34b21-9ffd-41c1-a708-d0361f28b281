#!/usr/bin/env python3
import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("📊 测试高级分析功能...")

try:
    from app.services.analytics import get_analytics_service
    
    async def test():
        analytics_service = await get_analytics_service()
        print("✅ 分析服务初始化成功")
        
        # 测试季节性因子计算
        monthly_trend = [
            {"month": "2024-01", "amount": 1000},
            {"month": "2024-02", "amount": 1200},
            {"month": "2024-03", "amount": 800},
        ]
        
        seasonal_factors = analytics_service._calculate_seasonal_factors(monthly_trend)
        print(f"✅ 季节性因子计算: {len(seasonal_factors)}个月份")
        
        # 测试趋势计算
        amounts = [100, 120, 110, 130, 125]
        trend_slope = analytics_service._calculate_linear_trend(amounts)
        print(f"✅ 趋势计算: 斜率 = {trend_slope:.4f}")
        
        # 测试置信区间计算
        confidence_interval = analytics_service._calculate_confidence_interval(amounts, 120)
        print(f"✅ 置信区间: {confidence_interval[0]:.2f} - {confidence_interval[1]:.2f}")
        
        print("🎉 分析功能基础测试完成!")
    
    asyncio.run(test())
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

print("📊 测试结束")
