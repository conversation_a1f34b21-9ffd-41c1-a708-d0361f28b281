{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.131183", "seconds": 1.131183}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:40:38.641704+08:00", "timestamp": 1753836038.641704}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.132255", "seconds": 1.132255}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:40:38.642776+08:00", "timestamp": 1753836038.642776}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.133185", "seconds": 1.133185}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:40:38.643706+08:00", "timestamp": 1753836038.643706}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.133185", "seconds": 1.133185}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:40:38.643706+08:00", "timestamp": 1753836038.643706}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.229201", "seconds": 5.229201}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:40:42.739722+08:00", "timestamp": 1753836042.739722}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.334960", "seconds": 5.33496}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:40:42.845481+08:00", "timestamp": 1753836042.845481}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.335956", "seconds": 5.335956}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:40:42.846477+08:00", "timestamp": 1753836042.846477}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:21.552873", "seconds": 21.552873}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:40:59.063394+08:00", "timestamp": 1753836059.063394}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:21.559788", "seconds": 21.559788}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:40:59.070309+08:00", "timestamp": 1753836059.070309}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:21.559788", "seconds": 21.559788}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:40:59.070309+08:00", "timestamp": 1753836059.070309}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:21.560789", "seconds": 21.560789}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:40:59.071310+08:00", "timestamp": 1753836059.07131}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:21.561870", "seconds": 21.56187}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:40:59.072391+08:00", "timestamp": 1753836059.072391}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:21.561870", "seconds": 21.56187}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:40:59.072391+08:00", "timestamp": 1753836059.072391}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:21.562870", "seconds": 21.56287}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 261, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:40:59.073391+08:00", "timestamp": 1753836059.073391}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:01:30.273159", "seconds": 90.273159}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:42:07.783680+08:00", "timestamp": 1753836127.78368}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:01:30.274159", "seconds": 90.274159}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:42:07.784680+08:00", "timestamp": 1753836127.78468}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:01:30.275172", "seconds": 90.275172}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:42:07.785693+08:00", "timestamp": 1753836127.785693}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:01:30.275172", "seconds": 90.275172}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:42:07.785693+08:00", "timestamp": 1753836127.785693}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:01:30.276168", "seconds": 90.276168}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:42:07.786689+08:00", "timestamp": 1753836127.786689}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:01:30.276168", "seconds": 90.276168}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 261, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 58272, "name": "MainProcess"}, "thread": {"id": 47896, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:42:07.786689+08:00", "timestamp": 1753836127.786689}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.099961", "seconds": 1.099961}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 55504, "name": "MainProcess"}, "thread": {"id": 18272, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:48:29.614555+08:00", "timestamp": 1753836509.614555}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.099961", "seconds": 1.099961}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 55504, "name": "MainProcess"}, "thread": {"id": 18272, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:48:29.614555+08:00", "timestamp": 1753836509.614555}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.100955", "seconds": 1.100955}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 55504, "name": "MainProcess"}, "thread": {"id": 18272, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:48:29.615549+08:00", "timestamp": 1753836509.615549}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.100955", "seconds": 1.100955}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 55504, "name": "MainProcess"}, "thread": {"id": 18272, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:48:29.615549+08:00", "timestamp": 1753836509.615549}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.188749", "seconds": 5.188749}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 55504, "name": "MainProcess"}, "thread": {"id": 18272, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:48:33.703343+08:00", "timestamp": 1753836513.703343}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.210863", "seconds": 5.210863}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 55504, "name": "MainProcess"}, "thread": {"id": 18272, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:48:33.725457+08:00", "timestamp": 1753836513.725457}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.211863", "seconds": 5.211863}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 55504, "name": "MainProcess"}, "thread": {"id": 18272, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:48:33.726457+08:00", "timestamp": 1753836513.726457}}}
{"text": "正在关闭数据库连接...\n", "record": {"elapsed": {"repr": "0:00:05.211863", "seconds": 5.211863}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 34, "message": "正在关闭数据库连接...", "module": "main", "name": "app.main", "process": {"id": 55504, "name": "MainProcess"}, "thread": {"id": 18272, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:48:33.726457+08:00", "timestamp": 1753836513.726457}}}
{"text": "数据库引擎已关闭\n", "record": {"elapsed": {"repr": "0:00:05.212872", "seconds": 5.212872}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "close_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 90, "message": "数据库引擎已关闭", "module": "connection", "name": "app.database.connection", "process": {"id": 55504, "name": "MainProcess"}, "thread": {"id": 18272, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:48:33.727466+08:00", "timestamp": 1753836513.727466}}}
{"text": "数据库连接已关闭\n", "record": {"elapsed": {"repr": "0:00:05.212872", "seconds": 5.212872}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "数据库连接已关闭", "module": "main", "name": "app.main", "process": {"id": 55504, "name": "MainProcess"}, "thread": {"id": 18272, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:48:33.727466+08:00", "timestamp": 1753836513.727466}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.017214", "seconds": 1.017214}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 41260, "name": "MainProcess"}, "thread": {"id": 55864, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:49:30.516012+08:00", "timestamp": 1753836570.516012}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.018217", "seconds": 1.018217}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 41260, "name": "MainProcess"}, "thread": {"id": 55864, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:49:30.517015+08:00", "timestamp": 1753836570.517015}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.018217", "seconds": 1.018217}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 41260, "name": "MainProcess"}, "thread": {"id": 55864, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:49:30.517015+08:00", "timestamp": 1753836570.517015}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.019217", "seconds": 1.019217}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 41260, "name": "MainProcess"}, "thread": {"id": 55864, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:49:30.518015+08:00", "timestamp": 1753836570.518015}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.108913", "seconds": 5.108913}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 41260, "name": "MainProcess"}, "thread": {"id": 55864, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:49:34.607711+08:00", "timestamp": 1753836574.607711}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.119420", "seconds": 5.11942}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 41260, "name": "MainProcess"}, "thread": {"id": 55864, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:49:34.618218+08:00", "timestamp": 1753836574.618218}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.119420", "seconds": 5.11942}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 41260, "name": "MainProcess"}, "thread": {"id": 55864, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:49:34.618218+08:00", "timestamp": 1753836574.618218}}}
{"text": "正在关闭数据库连接...\n", "record": {"elapsed": {"repr": "0:00:05.119420", "seconds": 5.11942}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 34, "message": "正在关闭数据库连接...", "module": "main", "name": "app.main", "process": {"id": 41260, "name": "MainProcess"}, "thread": {"id": 55864, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:49:34.618218+08:00", "timestamp": 1753836574.618218}}}
{"text": "数据库引擎已关闭\n", "record": {"elapsed": {"repr": "0:00:05.119420", "seconds": 5.11942}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "close_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 90, "message": "数据库引擎已关闭", "module": "connection", "name": "app.database.connection", "process": {"id": 41260, "name": "MainProcess"}, "thread": {"id": 55864, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:49:34.618218+08:00", "timestamp": 1753836574.618218}}}
{"text": "数据库连接已关闭\n", "record": {"elapsed": {"repr": "0:00:05.120908", "seconds": 5.120908}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "数据库连接已关闭", "module": "main", "name": "app.main", "process": {"id": 41260, "name": "MainProcess"}, "thread": {"id": 55864, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:49:34.619706+08:00", "timestamp": 1753836574.619706}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.041070", "seconds": 1.04107}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 47924, "name": "MainProcess"}, "thread": {"id": 58784, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:49:56.542419+08:00", "timestamp": 1753836596.542419}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.041070", "seconds": 1.04107}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 47924, "name": "MainProcess"}, "thread": {"id": 58784, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:49:56.542419+08:00", "timestamp": 1753836596.542419}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.042080", "seconds": 1.04208}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 47924, "name": "MainProcess"}, "thread": {"id": 58784, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:49:56.543429+08:00", "timestamp": 1753836596.543429}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.042080", "seconds": 1.04208}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 47924, "name": "MainProcess"}, "thread": {"id": 58784, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:49:56.543429+08:00", "timestamp": 1753836596.543429}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.130312", "seconds": 5.130312}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 47924, "name": "MainProcess"}, "thread": {"id": 58784, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:00.631661+08:00", "timestamp": 1753836600.631661}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.182938", "seconds": 5.182938}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 47924, "name": "MainProcess"}, "thread": {"id": 58784, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:00.684287+08:00", "timestamp": 1753836600.684287}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.183573", "seconds": 5.183573}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 47924, "name": "MainProcess"}, "thread": {"id": 58784, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:00.684922+08:00", "timestamp": 1753836600.684922}}}
{"text": "正在关闭数据库连接...\n", "record": {"elapsed": {"repr": "0:00:05.184669", "seconds": 5.184669}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 34, "message": "正在关闭数据库连接...", "module": "main", "name": "app.main", "process": {"id": 47924, "name": "MainProcess"}, "thread": {"id": 58784, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:00.686018+08:00", "timestamp": 1753836600.686018}}}
{"text": "数据库引擎已关闭\n", "record": {"elapsed": {"repr": "0:00:05.184669", "seconds": 5.184669}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "close_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 90, "message": "数据库引擎已关闭", "module": "connection", "name": "app.database.connection", "process": {"id": 47924, "name": "MainProcess"}, "thread": {"id": 58784, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:00.686018+08:00", "timestamp": 1753836600.686018}}}
{"text": "数据库连接已关闭\n", "record": {"elapsed": {"repr": "0:00:05.184669", "seconds": 5.184669}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "数据库连接已关闭", "module": "main", "name": "app.main", "process": {"id": 47924, "name": "MainProcess"}, "thread": {"id": 58784, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:00.686018+08:00", "timestamp": 1753836600.686018}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.049285", "seconds": 1.049285}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 58200, "name": "MainProcess"}, "thread": {"id": 54524, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:21.539083+08:00", "timestamp": 1753836621.539083}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.050285", "seconds": 1.050285}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 58200, "name": "MainProcess"}, "thread": {"id": 54524, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:21.540083+08:00", "timestamp": 1753836621.540083}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.050285", "seconds": 1.050285}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 58200, "name": "MainProcess"}, "thread": {"id": 54524, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:21.540083+08:00", "timestamp": 1753836621.540083}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.050285", "seconds": 1.050285}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 58200, "name": "MainProcess"}, "thread": {"id": 54524, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:21.540083+08:00", "timestamp": 1753836621.540083}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.147644", "seconds": 5.147644}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 58200, "name": "MainProcess"}, "thread": {"id": 54524, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:25.637442+08:00", "timestamp": 1753836625.637442}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.166939", "seconds": 5.166939}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 58200, "name": "MainProcess"}, "thread": {"id": 54524, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:25.656737+08:00", "timestamp": 1753836625.656737}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.166939", "seconds": 5.166939}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 58200, "name": "MainProcess"}, "thread": {"id": 54524, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:25.656737+08:00", "timestamp": 1753836625.656737}}}
{"text": "正在关闭数据库连接...\n", "record": {"elapsed": {"repr": "0:00:05.167939", "seconds": 5.167939}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 34, "message": "正在关闭数据库连接...", "module": "main", "name": "app.main", "process": {"id": 58200, "name": "MainProcess"}, "thread": {"id": 54524, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:25.657737+08:00", "timestamp": 1753836625.657737}}}
{"text": "数据库引擎已关闭\n", "record": {"elapsed": {"repr": "0:00:05.168939", "seconds": 5.168939}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "close_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 90, "message": "数据库引擎已关闭", "module": "connection", "name": "app.database.connection", "process": {"id": 58200, "name": "MainProcess"}, "thread": {"id": 54524, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:25.658737+08:00", "timestamp": 1753836625.658737}}}
{"text": "数据库连接已关闭\n", "record": {"elapsed": {"repr": "0:00:05.168939", "seconds": 5.168939}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "数据库连接已关闭", "module": "main", "name": "app.main", "process": {"id": 58200, "name": "MainProcess"}, "thread": {"id": 54524, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:25.658737+08:00", "timestamp": 1753836625.658737}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.192814", "seconds": 1.192814}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 39256, "name": "MainProcess"}, "thread": {"id": 24960, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:43.829915+08:00", "timestamp": 1753836643.829915}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.193247", "seconds": 1.193247}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 39256, "name": "MainProcess"}, "thread": {"id": 24960, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:43.830348+08:00", "timestamp": 1753836643.830348}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.193800", "seconds": 1.1938}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 39256, "name": "MainProcess"}, "thread": {"id": 24960, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:43.830901+08:00", "timestamp": 1753836643.830901}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.194336", "seconds": 1.194336}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 39256, "name": "MainProcess"}, "thread": {"id": 24960, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:43.831437+08:00", "timestamp": 1753836643.831437}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.294532", "seconds": 5.294532}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 39256, "name": "MainProcess"}, "thread": {"id": 24960, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:47.931633+08:00", "timestamp": 1753836647.931633}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.322489", "seconds": 5.322489}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 39256, "name": "MainProcess"}, "thread": {"id": 24960, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:47.959590+08:00", "timestamp": 1753836647.95959}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.322489", "seconds": 5.322489}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 39256, "name": "MainProcess"}, "thread": {"id": 24960, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:47.959590+08:00", "timestamp": 1753836647.95959}}}
{"text": "正在关闭数据库连接...\n", "record": {"elapsed": {"repr": "0:00:05.323493", "seconds": 5.323493}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 34, "message": "正在关闭数据库连接...", "module": "main", "name": "app.main", "process": {"id": 39256, "name": "MainProcess"}, "thread": {"id": 24960, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:47.960594+08:00", "timestamp": 1753836647.960594}}}
{"text": "数据库引擎已关闭\n", "record": {"elapsed": {"repr": "0:00:05.324480", "seconds": 5.32448}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "close_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 90, "message": "数据库引擎已关闭", "module": "connection", "name": "app.database.connection", "process": {"id": 39256, "name": "MainProcess"}, "thread": {"id": 24960, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:47.961581+08:00", "timestamp": 1753836647.961581}}}
{"text": "数据库连接已关闭\n", "record": {"elapsed": {"repr": "0:00:05.325071", "seconds": 5.325071}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "数据库连接已关闭", "module": "main", "name": "app.main", "process": {"id": 39256, "name": "MainProcess"}, "thread": {"id": 24960, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:50:47.962172+08:00", "timestamp": 1753836647.962172}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.188830", "seconds": 1.18883}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:51:22.658382+08:00", "timestamp": 1753836682.658382}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.188830", "seconds": 1.18883}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:51:22.658382+08:00", "timestamp": 1753836682.658382}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.189830", "seconds": 1.18983}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:51:22.659382+08:00", "timestamp": 1753836682.659382}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.189830", "seconds": 1.18983}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:51:22.659382+08:00", "timestamp": 1753836682.659382}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.279785", "seconds": 5.279785}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:51:26.749337+08:00", "timestamp": 1753836686.749337}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.306327", "seconds": 5.306327}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:51:26.775879+08:00", "timestamp": 1753836686.775879}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.306327", "seconds": 5.306327}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:51:26.775879+08:00", "timestamp": 1753836686.775879}}}
{"text": "语言已切换到: zh-CN\n", "record": {"elapsed": {"repr": "0:00:54.408329", "seconds": 54.408329}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "set_language", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 370, "message": "语言已切换到: zh-CN", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:15.877881+08:00", "timestamp": 1753836735.877881}}}
{"text": "语言已切换到: zh-CN\n", "record": {"elapsed": {"repr": "0:00:57.309094", "seconds": 57.309094}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "set_language", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 370, "message": "语言已切换到: zh-CN", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:18.778646+08:00", "timestamp": 1753836738.778646}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:57.310361", "seconds": 57.310361}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:18.779913+08:00", "timestamp": 1753836738.779913}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:57.310361", "seconds": 57.310361}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:18.779913+08:00", "timestamp": 1753836738.779913}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:57.310361", "seconds": 57.310361}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:18.779913+08:00", "timestamp": 1753836738.779913}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:57.311864", "seconds": 57.311864}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:18.781416+08:00", "timestamp": 1753836738.781416}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:57.312373", "seconds": 57.312373}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:18.781925+08:00", "timestamp": 1753836738.781925}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:57.313375", "seconds": 57.313375}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 261, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:18.782927+08:00", "timestamp": 1753836738.782927}}}
{"text": "语言已切换到: zh-CN\n", "record": {"elapsed": {"repr": "0:00:57.715670", "seconds": 57.71567}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "set_language", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 370, "message": "语言已切换到: zh-CN", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:19.185222+08:00", "timestamp": 1753836739.185222}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:57.716669", "seconds": 57.716669}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:19.186221+08:00", "timestamp": 1753836739.186221}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:57.716669", "seconds": 57.716669}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:19.186221+08:00", "timestamp": 1753836739.186221}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:57.717670", "seconds": 57.71767}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:19.187222+08:00", "timestamp": 1753836739.187222}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:57.717670", "seconds": 57.71767}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:19.187222+08:00", "timestamp": 1753836739.187222}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:57.717670", "seconds": 57.71767}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:19.187222+08:00", "timestamp": 1753836739.187222}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:57.718669", "seconds": 57.718669}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 261, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:19.188221+08:00", "timestamp": 1753836739.188221}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:58.488743", "seconds": 58.488743}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:19.958295+08:00", "timestamp": 1753836739.958295}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:58.489862", "seconds": 58.489862}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:19.959414+08:00", "timestamp": 1753836739.959414}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:58.489862", "seconds": 58.489862}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:19.959414+08:00", "timestamp": 1753836739.959414}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:58.490774", "seconds": 58.490774}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:19.960326+08:00", "timestamp": 1753836739.960326}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:58.490774", "seconds": 58.490774}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:19.960326+08:00", "timestamp": 1753836739.960326}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:58.491861", "seconds": 58.491861}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:19.961413+08:00", "timestamp": 1753836739.961413}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:58.492470", "seconds": 58.49247}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 261, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:52:19.962022+08:00", "timestamp": 1753836739.962022}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:02:29.098722", "seconds": 149.098722}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:53:50.568274+08:00", "timestamp": 1753836830.568274}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:02:29.099696", "seconds": 149.099696}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:53:50.569248+08:00", "timestamp": 1753836830.569248}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:02:29.099696", "seconds": 149.099696}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:53:50.569248+08:00", "timestamp": 1753836830.569248}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:02:29.099696", "seconds": 149.099696}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:53:50.569248+08:00", "timestamp": 1753836830.569248}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:02:29.100669", "seconds": 149.100669}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:53:50.570221+08:00", "timestamp": 1753836830.570221}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:02:29.100669", "seconds": 149.100669}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 261, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:53:50.570221+08:00", "timestamp": 1753836830.570221}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:02:50.338507", "seconds": 170.338507}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:54:11.808059+08:00", "timestamp": 1753836851.808059}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:02:50.338507", "seconds": 170.338507}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:54:11.808059+08:00", "timestamp": 1753836851.808059}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:02:50.339481", "seconds": 170.339481}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:54:11.809033+08:00", "timestamp": 1753836851.809033}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:02:50.339481", "seconds": 170.339481}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:54:11.809033+08:00", "timestamp": 1753836851.809033}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:02:50.340454", "seconds": 170.340454}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:54:11.810006+08:00", "timestamp": 1753836851.810006}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:02:50.340454", "seconds": 170.340454}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 261, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:54:11.810006+08:00", "timestamp": 1753836851.810006}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:07:22.448585", "seconds": 442.448585}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:43.918137+08:00", "timestamp": 1753837123.918137}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:07:22.449558", "seconds": 442.449558}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:43.919110+08:00", "timestamp": 1753837123.91911}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:07:22.449558", "seconds": 442.449558}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:43.919110+08:00", "timestamp": 1753837123.91911}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:07:22.449558", "seconds": 442.449558}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:43.919110+08:00", "timestamp": 1753837123.91911}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:07:22.450532", "seconds": 442.450532}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:43.920084+08:00", "timestamp": 1753837123.920084}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:07:22.450532", "seconds": 442.450532}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 261, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:43.920084+08:00", "timestamp": 1753837123.920084}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:07:22.711736", "seconds": 442.711736}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.181288+08:00", "timestamp": 1753837124.181288}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:07:22.711736", "seconds": 442.711736}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.181288+08:00", "timestamp": 1753837124.181288}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:07:22.712741", "seconds": 442.712741}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.182293+08:00", "timestamp": 1753837124.182293}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:07:22.712741", "seconds": 442.712741}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.182293+08:00", "timestamp": 1753837124.182293}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:07:22.713715", "seconds": 442.713715}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.183267+08:00", "timestamp": 1753837124.183267}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:07:22.713715", "seconds": 442.713715}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 261, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.183267+08:00", "timestamp": 1753837124.183267}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:07:22.972720", "seconds": 442.97272}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.442272+08:00", "timestamp": 1753837124.442272}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:07:22.973694", "seconds": 442.973694}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.443246+08:00", "timestamp": 1753837124.443246}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:07:22.973694", "seconds": 442.973694}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.443246+08:00", "timestamp": 1753837124.443246}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:07:22.973694", "seconds": 442.973694}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.443246+08:00", "timestamp": 1753837124.443246}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:07:22.976616", "seconds": 442.976616}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.446168+08:00", "timestamp": 1753837124.446168}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:07:22.977590", "seconds": 442.97759}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 261, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.447142+08:00", "timestamp": 1753837124.447142}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:07:23.240004", "seconds": 443.240004}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.709556+08:00", "timestamp": 1753837124.709556}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:07:23.241508", "seconds": 443.241508}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.711060+08:00", "timestamp": 1753837124.71106}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:07:23.241508", "seconds": 443.241508}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.711060+08:00", "timestamp": 1753837124.71106}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:07:23.242505", "seconds": 443.242505}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.712057+08:00", "timestamp": 1753837124.712057}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:07:23.242505", "seconds": 443.242505}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.712057+08:00", "timestamp": 1753837124.712057}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:07:23.242505", "seconds": 443.242505}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 261, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 55368, "name": "MainProcess"}, "thread": {"id": 59756, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:58:44.712057+08:00", "timestamp": 1753837124.712057}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.495561", "seconds": 1.495561}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 43024, "name": "MainProcess"}, "thread": {"id": 41296, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:59:59.213391+08:00", "timestamp": 1753837199.213391}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.496536", "seconds": 1.496536}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 43024, "name": "MainProcess"}, "thread": {"id": 41296, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:59:59.214366+08:00", "timestamp": 1753837199.214366}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.497510", "seconds": 1.49751}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 43024, "name": "MainProcess"}, "thread": {"id": 41296, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:59:59.215340+08:00", "timestamp": 1753837199.21534}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.497510", "seconds": 1.49751}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 43024, "name": "MainProcess"}, "thread": {"id": 41296, "name": "MainThread"}, "time": {"repr": "2025-07-30 08:59:59.215340+08:00", "timestamp": 1753837199.21534}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.513780", "seconds": 5.51378}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 43024, "name": "MainProcess"}, "thread": {"id": 41296, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:03.231610+08:00", "timestamp": 1753837203.23161}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.529435", "seconds": 5.529435}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 43024, "name": "MainProcess"}, "thread": {"id": 41296, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:03.247265+08:00", "timestamp": 1753837203.247265}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.529435", "seconds": 5.529435}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 43024, "name": "MainProcess"}, "thread": {"id": 41296, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:03.247265+08:00", "timestamp": 1753837203.247265}}}
{"text": "正在关闭数据库连接...\n", "record": {"elapsed": {"repr": "0:00:05.530409", "seconds": 5.530409}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 34, "message": "正在关闭数据库连接...", "module": "main", "name": "app.main", "process": {"id": 43024, "name": "MainProcess"}, "thread": {"id": 41296, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:03.248239+08:00", "timestamp": 1753837203.248239}}}
{"text": "数据库引擎已关闭\n", "record": {"elapsed": {"repr": "0:00:05.530409", "seconds": 5.530409}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "close_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 90, "message": "数据库引擎已关闭", "module": "connection", "name": "app.database.connection", "process": {"id": 43024, "name": "MainProcess"}, "thread": {"id": 41296, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:03.248239+08:00", "timestamp": 1753837203.248239}}}
{"text": "数据库连接已关闭\n", "record": {"elapsed": {"repr": "0:00:05.531382", "seconds": 5.531382}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "数据库连接已关闭", "module": "main", "name": "app.main", "process": {"id": 43024, "name": "MainProcess"}, "thread": {"id": 41296, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:03.249212+08:00", "timestamp": 1753837203.249212}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.138214", "seconds": 1.138214}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 45140, "name": "MainProcess"}, "thread": {"id": 48572, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:14.774375+08:00", "timestamp": 1753837214.774375}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.138214", "seconds": 1.138214}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 45140, "name": "MainProcess"}, "thread": {"id": 48572, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:14.774375+08:00", "timestamp": 1753837214.774375}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.139099", "seconds": 1.139099}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 45140, "name": "MainProcess"}, "thread": {"id": 48572, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:14.775260+08:00", "timestamp": 1753837214.77526}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.139099", "seconds": 1.139099}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 45140, "name": "MainProcess"}, "thread": {"id": 48572, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:14.775260+08:00", "timestamp": 1753837214.77526}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.150196", "seconds": 5.150196}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 45140, "name": "MainProcess"}, "thread": {"id": 48572, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:18.786357+08:00", "timestamp": 1753837218.786357}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.162168", "seconds": 5.162168}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 45140, "name": "MainProcess"}, "thread": {"id": 48572, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:18.798329+08:00", "timestamp": 1753837218.798329}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.162168", "seconds": 5.162168}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 45140, "name": "MainProcess"}, "thread": {"id": 48572, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:18.798329+08:00", "timestamp": 1753837218.798329}}}
{"text": "正在关闭数据库连接...\n", "record": {"elapsed": {"repr": "0:00:05.163142", "seconds": 5.163142}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 34, "message": "正在关闭数据库连接...", "module": "main", "name": "app.main", "process": {"id": 45140, "name": "MainProcess"}, "thread": {"id": 48572, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:18.799303+08:00", "timestamp": 1753837218.799303}}}
{"text": "数据库引擎已关闭\n", "record": {"elapsed": {"repr": "0:00:05.163142", "seconds": 5.163142}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "close_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 90, "message": "数据库引擎已关闭", "module": "connection", "name": "app.database.connection", "process": {"id": 45140, "name": "MainProcess"}, "thread": {"id": 48572, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:18.799303+08:00", "timestamp": 1753837218.799303}}}
{"text": "数据库连接已关闭\n", "record": {"elapsed": {"repr": "0:00:05.163142", "seconds": 5.163142}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "数据库连接已关闭", "module": "main", "name": "app.main", "process": {"id": 45140, "name": "MainProcess"}, "thread": {"id": 48572, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:18.799303+08:00", "timestamp": 1753837218.799303}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.166978", "seconds": 1.166978}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 54484, "name": "MainProcess"}, "thread": {"id": 11364, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:29.989236+08:00", "timestamp": 1753837229.989236}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.167951", "seconds": 1.167951}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 54484, "name": "MainProcess"}, "thread": {"id": 11364, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:29.990209+08:00", "timestamp": 1753837229.990209}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.167951", "seconds": 1.167951}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 54484, "name": "MainProcess"}, "thread": {"id": 11364, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:29.990209+08:00", "timestamp": 1753837229.990209}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.167951", "seconds": 1.167951}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 54484, "name": "MainProcess"}, "thread": {"id": 11364, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:29.990209+08:00", "timestamp": 1753837229.990209}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.175033", "seconds": 5.175033}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 54484, "name": "MainProcess"}, "thread": {"id": 11364, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:33.997291+08:00", "timestamp": 1753837233.997291}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.187787", "seconds": 5.187787}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 54484, "name": "MainProcess"}, "thread": {"id": 11364, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:34.010045+08:00", "timestamp": 1753837234.010045}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.188791", "seconds": 5.188791}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 54484, "name": "MainProcess"}, "thread": {"id": 11364, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:34.011049+08:00", "timestamp": 1753837234.011049}}}
{"text": "正在关闭数据库连接...\n", "record": {"elapsed": {"repr": "0:00:05.188791", "seconds": 5.188791}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 34, "message": "正在关闭数据库连接...", "module": "main", "name": "app.main", "process": {"id": 54484, "name": "MainProcess"}, "thread": {"id": 11364, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:34.011049+08:00", "timestamp": 1753837234.011049}}}
{"text": "数据库引擎已关闭\n", "record": {"elapsed": {"repr": "0:00:05.189788", "seconds": 5.189788}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "close_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 90, "message": "数据库引擎已关闭", "module": "connection", "name": "app.database.connection", "process": {"id": 54484, "name": "MainProcess"}, "thread": {"id": 11364, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:34.012046+08:00", "timestamp": 1753837234.012046}}}
{"text": "数据库连接已关闭\n", "record": {"elapsed": {"repr": "0:00:05.189788", "seconds": 5.189788}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "数据库连接已关闭", "module": "main", "name": "app.main", "process": {"id": 54484, "name": "MainProcess"}, "thread": {"id": 11364, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:00:34.012046+08:00", "timestamp": 1753837234.012046}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.212872", "seconds": 1.212872}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:01:44.833380+08:00", "timestamp": 1753837304.83338}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.212872", "seconds": 1.212872}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:01:44.833380+08:00", "timestamp": 1753837304.83338}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.213845", "seconds": 1.213845}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:01:44.834353+08:00", "timestamp": 1753837304.834353}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.213845", "seconds": 1.213845}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:01:44.834353+08:00", "timestamp": 1753837304.834353}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.222968", "seconds": 5.222968}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:01:48.843476+08:00", "timestamp": 1753837308.843476}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.237210", "seconds": 5.23721}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:01:48.857718+08:00", "timestamp": 1753837308.857718}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.237210", "seconds": 5.23721}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:01:48.857718+08:00", "timestamp": 1753837308.857718}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:22.865728", "seconds": 22.865728}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:06.486236+08:00", "timestamp": 1753837326.486236}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:22.866695", "seconds": 22.866695}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:06.487203+08:00", "timestamp": 1753837326.487203}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:22.866695", "seconds": 22.866695}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:06.487203+08:00", "timestamp": 1753837326.487203}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:22.866695", "seconds": 22.866695}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:06.487203+08:00", "timestamp": 1753837326.487203}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:22.866695", "seconds": 22.866695}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:06.487203+08:00", "timestamp": 1753837326.487203}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:22.867668", "seconds": 22.867668}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:06.488176+08:00", "timestamp": 1753837326.488176}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:22.867668", "seconds": 22.867668}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 261, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:06.488176+08:00", "timestamp": 1753837326.488176}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:23.132850", "seconds": 23.13285}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:06.753358+08:00", "timestamp": 1753837326.753358}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:23.132850", "seconds": 23.13285}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:06.753358+08:00", "timestamp": 1753837326.753358}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:23.133823", "seconds": 23.133823}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:06.754331+08:00", "timestamp": 1753837326.754331}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:23.133823", "seconds": 23.133823}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:06.754331+08:00", "timestamp": 1753837326.754331}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:23.133823", "seconds": 23.133823}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:06.754331+08:00", "timestamp": 1753837326.754331}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:23.134796", "seconds": 23.134796}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 261, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:06.755304+08:00", "timestamp": 1753837326.755304}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:23.394827", "seconds": 23.394827}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:07.015335+08:00", "timestamp": 1753837327.015335}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:23.394827", "seconds": 23.394827}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:07.015335+08:00", "timestamp": 1753837327.015335}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:23.395800", "seconds": 23.3958}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:07.016308+08:00", "timestamp": 1753837327.016308}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:23.395800", "seconds": 23.3958}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:07.016308+08:00", "timestamp": 1753837327.016308}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:23.395800", "seconds": 23.3958}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:07.016308+08:00", "timestamp": 1753837327.016308}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:23.396775", "seconds": 23.396775}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 261, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:07.017283+08:00", "timestamp": 1753837327.017283}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:23.656729", "seconds": 23.656729}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:07.277237+08:00", "timestamp": 1753837327.277237}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:23.656729", "seconds": 23.656729}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:07.277237+08:00", "timestamp": 1753837327.277237}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:23.657703", "seconds": 23.657703}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:07.278211+08:00", "timestamp": 1753837327.278211}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:23.657703", "seconds": 23.657703}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:07.278211+08:00", "timestamp": 1753837327.278211}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:23.658676", "seconds": 23.658676}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:07.279184+08:00", "timestamp": 1753837327.279184}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:23.658676", "seconds": 23.658676}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 261, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 57076, "name": "MainProcess"}, "thread": {"id": 56104, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:02:07.279184+08:00", "timestamp": 1753837327.279184}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.231319", "seconds": 1.231319}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 29348, "name": "MainProcess"}, "thread": {"id": 36512, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:04:39.988427+08:00", "timestamp": 1753837479.988427}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.231319", "seconds": 1.231319}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 29348, "name": "MainProcess"}, "thread": {"id": 36512, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:04:39.988427+08:00", "timestamp": 1753837479.988427}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.232292", "seconds": 1.232292}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 29348, "name": "MainProcess"}, "thread": {"id": 36512, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:04:39.989400+08:00", "timestamp": 1753837479.9894}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.232292", "seconds": 1.232292}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 29348, "name": "MainProcess"}, "thread": {"id": 36512, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:04:39.989400+08:00", "timestamp": 1753837479.9894}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.240925", "seconds": 5.240925}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 29348, "name": "MainProcess"}, "thread": {"id": 36512, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:04:43.998033+08:00", "timestamp": 1753837483.998033}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.255208", "seconds": 5.255208}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 29348, "name": "MainProcess"}, "thread": {"id": 36512, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:04:44.012316+08:00", "timestamp": 1753837484.012316}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.255208", "seconds": 5.255208}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 29348, "name": "MainProcess"}, "thread": {"id": 36512, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:04:44.012316+08:00", "timestamp": 1753837484.012316}}}
{"text": "正在关闭数据库连接...\n", "record": {"elapsed": {"repr": "0:00:05.255208", "seconds": 5.255208}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 34, "message": "正在关闭数据库连接...", "module": "main", "name": "app.main", "process": {"id": 29348, "name": "MainProcess"}, "thread": {"id": 36512, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:04:44.012316+08:00", "timestamp": 1753837484.012316}}}
{"text": "数据库引擎已关闭\n", "record": {"elapsed": {"repr": "0:00:05.256711", "seconds": 5.256711}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "close_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 90, "message": "数据库引擎已关闭", "module": "connection", "name": "app.database.connection", "process": {"id": 29348, "name": "MainProcess"}, "thread": {"id": 36512, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:04:44.013819+08:00", "timestamp": 1753837484.013819}}}
{"text": "数据库连接已关闭\n", "record": {"elapsed": {"repr": "0:00:05.256711", "seconds": 5.256711}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "数据库连接已关闭", "module": "main", "name": "app.main", "process": {"id": 29348, "name": "MainProcess"}, "thread": {"id": 36512, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:04:44.013819+08:00", "timestamp": 1753837484.013819}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.209092", "seconds": 1.209092}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 48576, "name": "MainProcess"}, "thread": {"id": 50676, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:09:27.041272+08:00", "timestamp": 1753837767.041272}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.210068", "seconds": 1.210068}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 48576, "name": "MainProcess"}, "thread": {"id": 50676, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:09:27.042248+08:00", "timestamp": 1753837767.042248}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.210068", "seconds": 1.210068}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 48576, "name": "MainProcess"}, "thread": {"id": 50676, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:09:27.042248+08:00", "timestamp": 1753837767.042248}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.210068", "seconds": 1.210068}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 48576, "name": "MainProcess"}, "thread": {"id": 50676, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:09:27.042248+08:00", "timestamp": 1753837767.042248}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.221181", "seconds": 5.221181}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 48576, "name": "MainProcess"}, "thread": {"id": 50676, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:09:31.053361+08:00", "timestamp": 1753837771.053361}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.234423", "seconds": 5.234423}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 48576, "name": "MainProcess"}, "thread": {"id": 50676, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:09:31.066603+08:00", "timestamp": 1753837771.066603}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.234423", "seconds": 5.234423}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 48576, "name": "MainProcess"}, "thread": {"id": 50676, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:09:31.066603+08:00", "timestamp": 1753837771.066603}}}
{"text": "正在关闭数据库连接...\n", "record": {"elapsed": {"repr": "0:00:05.235396", "seconds": 5.235396}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 34, "message": "正在关闭数据库连接...", "module": "main", "name": "app.main", "process": {"id": 48576, "name": "MainProcess"}, "thread": {"id": 50676, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:09:31.067576+08:00", "timestamp": 1753837771.067576}}}
{"text": "数据库引擎已关闭\n", "record": {"elapsed": {"repr": "0:00:05.235396", "seconds": 5.235396}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "close_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 90, "message": "数据库引擎已关闭", "module": "connection", "name": "app.database.connection", "process": {"id": 48576, "name": "MainProcess"}, "thread": {"id": 50676, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:09:31.067576+08:00", "timestamp": 1753837771.067576}}}
{"text": "数据库连接已关闭\n", "record": {"elapsed": {"repr": "0:00:05.235396", "seconds": 5.235396}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "数据库连接已关闭", "module": "main", "name": "app.main", "process": {"id": 48576, "name": "MainProcess"}, "thread": {"id": 50676, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:09:31.067576+08:00", "timestamp": 1753837771.067576}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.176054", "seconds": 1.176054}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 57304, "name": "MainProcess"}, "thread": {"id": 55916, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:10:05.827309+08:00", "timestamp": 1753837805.827309}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.177030", "seconds": 1.17703}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 57304, "name": "MainProcess"}, "thread": {"id": 55916, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:10:05.828285+08:00", "timestamp": 1753837805.828285}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.177997", "seconds": 1.177997}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 57304, "name": "MainProcess"}, "thread": {"id": 55916, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:10:05.829252+08:00", "timestamp": 1753837805.829252}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.177997", "seconds": 1.177997}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 57304, "name": "MainProcess"}, "thread": {"id": 55916, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:10:05.829252+08:00", "timestamp": 1753837805.829252}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.197989", "seconds": 5.197989}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 57304, "name": "MainProcess"}, "thread": {"id": 55916, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:10:09.849244+08:00", "timestamp": 1753837809.849244}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.217985", "seconds": 5.217985}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 57304, "name": "MainProcess"}, "thread": {"id": 55916, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:10:09.869240+08:00", "timestamp": 1753837809.86924}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.217985", "seconds": 5.217985}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 57304, "name": "MainProcess"}, "thread": {"id": 55916, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:10:09.869240+08:00", "timestamp": 1753837809.86924}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:17.298674", "seconds": 17.298674}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 57304, "name": "MainProcess"}, "thread": {"id": 55916, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:10:21.949929+08:00", "timestamp": 1753837821.949929}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:17.298674", "seconds": 17.298674}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 57304, "name": "MainProcess"}, "thread": {"id": 55916, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:10:21.949929+08:00", "timestamp": 1753837821.949929}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:17.298674", "seconds": 17.298674}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 57304, "name": "MainProcess"}, "thread": {"id": 55916, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:10:21.949929+08:00", "timestamp": 1753837821.949929}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:17.300177", "seconds": 17.300177}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 57304, "name": "MainProcess"}, "thread": {"id": 55916, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:10:21.951432+08:00", "timestamp": 1753837821.951432}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:17.300177", "seconds": 17.300177}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 57304, "name": "MainProcess"}, "thread": {"id": 55916, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:10:21.951432+08:00", "timestamp": 1753837821.951432}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:17.306038", "seconds": 17.306038}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 263, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 57304, "name": "MainProcess"}, "thread": {"id": 55916, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:10:21.957293+08:00", "timestamp": 1753837821.957293}}}
{"text": "异常堆栈:   + Exception Group Traceback (most recent call last):\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 76, in collapse_excgroups\n  |     yield\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 177, in __call__\n  |     async with anyio.create_task_group() as task_group:\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\anyio\\_backends\\_asyncio.py\", line 767, in __aexit__\n  |     raise BaseExceptionGroup(\n  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)\n  +-+---------------- 1 ----------------\n    | Traceback (most recent call last):\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\errors.py\", line 165, in __call__\n    |     await self.app(scope, receive, _send)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 51, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 38, in dispatch\n    |     response = await call_next(request)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py\", line 53, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 434, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 430, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 188, in dispatch\n    |     event_type=AuditEventType.SYSTEM_ERROR,\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    | AttributeError: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n    +------------------------------------\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\errors.py\", line 165, in __call__\n    await self.app(scope, receive, _send)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 51, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 38, in dispatch\n    response = await call_next(request)\n               ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py\", line 53, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 434, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 430, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 188, in dispatch\n    event_type=AuditEventType.SYSTEM_ERROR,\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n\n", "record": {"elapsed": {"repr": "0:00:17.307012", "seconds": 17.307012}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 264, "message": "异常堆栈:   + Exception Group Traceback (most recent call last):\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 76, in collapse_excgroups\n  |     yield\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 177, in __call__\n  |     async with anyio.create_task_group() as task_group:\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\anyio\\_backends\\_asyncio.py\", line 767, in __aexit__\n  |     raise BaseExceptionGroup(\n  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)\n  +-+---------------- 1 ----------------\n    | Traceback (most recent call last):\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\errors.py\", line 165, in __call__\n    |     await self.app(scope, receive, _send)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 51, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 38, in dispatch\n    |     response = await call_next(request)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py\", line 53, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 434, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 430, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 188, in dispatch\n    |     event_type=AuditEventType.SYSTEM_ERROR,\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    | AttributeError: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n    +------------------------------------\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\errors.py\", line 165, in __call__\n    await self.app(scope, receive, _send)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 51, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 38, in dispatch\n    response = await call_next(request)\n               ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py\", line 53, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 434, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 430, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 188, in dispatch\n    event_type=AuditEventType.SYSTEM_ERROR,\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "module": "main", "name": "app.main", "process": {"id": 57304, "name": "MainProcess"}, "thread": {"id": 55916, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:10:21.958267+08:00", "timestamp": 1753837821.958267}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.112777", "seconds": 1.112777}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 59064, "name": "MainProcess"}, "thread": {"id": 60464, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:20:00.828282+08:00", "timestamp": 1753838400.828282}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.112777", "seconds": 1.112777}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 59064, "name": "MainProcess"}, "thread": {"id": 60464, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:20:00.828282+08:00", "timestamp": 1753838400.828282}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.113751", "seconds": 1.113751}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 59064, "name": "MainProcess"}, "thread": {"id": 60464, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:20:00.829256+08:00", "timestamp": 1753838400.829256}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.120754", "seconds": 5.120754}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 59064, "name": "MainProcess"}, "thread": {"id": 60464, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:20:04.836259+08:00", "timestamp": 1753838404.836259}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.136667", "seconds": 5.136667}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 59064, "name": "MainProcess"}, "thread": {"id": 60464, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:20:04.852172+08:00", "timestamp": 1753838404.852172}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:43.342791", "seconds": 43.342791}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 59064, "name": "MainProcess"}, "thread": {"id": 60464, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:20:43.058296+08:00", "timestamp": 1753838443.058296}}}
{"text": "数据库会话错误: 400: 邮箱已存在\n", "record": {"elapsed": {"repr": "0:00:43.372647", "seconds": 43.372647}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "get_db", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 103, "message": "数据库会话错误: 400: 邮箱已存在", "module": "connection", "name": "app.database.connection", "process": {"id": 59064, "name": "MainProcess"}, "thread": {"id": 60464, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:20:43.088152+08:00", "timestamp": 1753838443.088152}}}
{"text": "错误请求: {'method': 'POST', 'path': '/api/v1/auth/register', 'status_code': 400, 'process_time': 0.029, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}\n", "record": {"elapsed": {"repr": "0:00:43.373621", "seconds": 43.373621}, "exception": null, "extra": {}, "file": {"name": "rate_limit.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py"}, "function": "_log_request", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 278, "message": "错误请求: {'method': 'POST', 'path': '/api/v1/auth/register', 'status_code': 400, 'process_time': 0.029, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}", "module": "rate_limit", "name": "app.middleware.rate_limit", "process": {"id": 59064, "name": "MainProcess"}, "thread": {"id": 60464, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:20:43.089126+08:00", "timestamp": 1753838443.089126}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.015549", "seconds": 1.015549}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 45100, "name": "MainProcess"}, "thread": {"id": 17424, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:21:56.812798+08:00", "timestamp": 1753838516.812798}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.016551", "seconds": 1.016551}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 45100, "name": "MainProcess"}, "thread": {"id": 17424, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:21:56.813800+08:00", "timestamp": 1753838516.8138}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.016551", "seconds": 1.016551}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 45100, "name": "MainProcess"}, "thread": {"id": 17424, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:21:56.813800+08:00", "timestamp": 1753838516.8138}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.027776", "seconds": 5.027776}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 45100, "name": "MainProcess"}, "thread": {"id": 17424, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:22:00.825025+08:00", "timestamp": 1753838520.825025}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.037585", "seconds": 5.037585}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 45100, "name": "MainProcess"}, "thread": {"id": 17424, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:22:00.834834+08:00", "timestamp": 1753838520.834834}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.469205", "seconds": 1.469205}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 61776, "name": "MainProcess"}, "thread": {"id": 44416, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:29:07.112035+08:00", "timestamp": 1753838947.112035}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.470708", "seconds": 1.470708}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 61776, "name": "MainProcess"}, "thread": {"id": 44416, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:29:07.113538+08:00", "timestamp": 1753838947.113538}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.470708", "seconds": 1.470708}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 61776, "name": "MainProcess"}, "thread": {"id": 44416, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:29:07.113538+08:00", "timestamp": 1753838947.113538}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.470708", "seconds": 1.470708}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 61776, "name": "MainProcess"}, "thread": {"id": 44416, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:29:07.113538+08:00", "timestamp": 1753838947.113538}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.479786", "seconds": 5.479786}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 61776, "name": "MainProcess"}, "thread": {"id": 44416, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:29:11.122616+08:00", "timestamp": 1753838951.122616}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.505892", "seconds": 5.505892}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 61776, "name": "MainProcess"}, "thread": {"id": 44416, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:29:11.148722+08:00", "timestamp": 1753838951.148722}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.506866", "seconds": 5.506866}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 61776, "name": "MainProcess"}, "thread": {"id": 44416, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:29:11.149696+08:00", "timestamp": 1753838951.149696}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:28.465227", "seconds": 28.465227}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 61776, "name": "MainProcess"}, "thread": {"id": 44416, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:29:34.108057+08:00", "timestamp": 1753838974.108057}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:28.467173", "seconds": 28.467173}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 61776, "name": "MainProcess"}, "thread": {"id": 44416, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:29:34.110003+08:00", "timestamp": 1753838974.110003}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:28.468177", "seconds": 28.468177}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 61776, "name": "MainProcess"}, "thread": {"id": 44416, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:29:34.111007+08:00", "timestamp": 1753838974.111007}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:28.468177", "seconds": 28.468177}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 61776, "name": "MainProcess"}, "thread": {"id": 44416, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:29:34.111007+08:00", "timestamp": 1753838974.111007}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:28.469185", "seconds": 28.469185}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 61776, "name": "MainProcess"}, "thread": {"id": 44416, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:29:34.112015+08:00", "timestamp": 1753838974.112015}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:28.479077", "seconds": 28.479077}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 263, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 61776, "name": "MainProcess"}, "thread": {"id": 44416, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:29:34.121907+08:00", "timestamp": 1753838974.121907}}}
{"text": "异常堆栈:   + Exception Group Traceback (most recent call last):\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 76, in collapse_excgroups\n  |     yield\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 177, in __call__\n  |     async with anyio.create_task_group() as task_group:\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\anyio\\_backends\\_asyncio.py\", line 767, in __aexit__\n  |     raise BaseExceptionGroup(\n  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)\n  +-+---------------- 1 ----------------\n    | Traceback (most recent call last):\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\errors.py\", line 165, in __call__\n    |     await self.app(scope, receive, _send)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 51, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 38, in dispatch\n    |     response = await call_next(request)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py\", line 53, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 434, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 430, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 188, in dispatch\n    |     event_type=AuditEventType.SYSTEM_ERROR,\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    | AttributeError: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n    +------------------------------------\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\errors.py\", line 165, in __call__\n    await self.app(scope, receive, _send)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 51, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 38, in dispatch\n    response = await call_next(request)\n               ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py\", line 53, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 434, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 430, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 188, in dispatch\n    event_type=AuditEventType.SYSTEM_ERROR,\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n\n", "record": {"elapsed": {"repr": "0:00:28.480051", "seconds": 28.480051}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 264, "message": "异常堆栈:   + Exception Group Traceback (most recent call last):\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 76, in collapse_excgroups\n  |     yield\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 177, in __call__\n  |     async with anyio.create_task_group() as task_group:\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\anyio\\_backends\\_asyncio.py\", line 767, in __aexit__\n  |     raise BaseExceptionGroup(\n  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)\n  +-+---------------- 1 ----------------\n    | Traceback (most recent call last):\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\errors.py\", line 165, in __call__\n    |     await self.app(scope, receive, _send)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 51, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 38, in dispatch\n    |     response = await call_next(request)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py\", line 53, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 434, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 430, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 188, in dispatch\n    |     event_type=AuditEventType.SYSTEM_ERROR,\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    | AttributeError: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n    +------------------------------------\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\errors.py\", line 165, in __call__\n    await self.app(scope, receive, _send)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 51, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 38, in dispatch\n    response = await call_next(request)\n               ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py\", line 53, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 434, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 430, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 188, in dispatch\n    event_type=AuditEventType.SYSTEM_ERROR,\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "module": "main", "name": "app.main", "process": {"id": 61776, "name": "MainProcess"}, "thread": {"id": 44416, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:29:34.122881+08:00", "timestamp": 1753838974.122881}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.683413", "seconds": 1.683413}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 31072, "name": "MainProcess"}, "thread": {"id": 12040, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:32:03.441184+08:00", "timestamp": 1753839123.441184}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.684771", "seconds": 1.684771}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 31072, "name": "MainProcess"}, "thread": {"id": 12040, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:32:03.442542+08:00", "timestamp": 1753839123.442542}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.684771", "seconds": 1.684771}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 31072, "name": "MainProcess"}, "thread": {"id": 12040, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:32:03.442542+08:00", "timestamp": 1753839123.442542}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.684771", "seconds": 1.684771}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 31072, "name": "MainProcess"}, "thread": {"id": 12040, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:32:03.442542+08:00", "timestamp": 1753839123.442542}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.696432", "seconds": 5.696432}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 31072, "name": "MainProcess"}, "thread": {"id": 12040, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:32:07.454203+08:00", "timestamp": 1753839127.454203}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.715639", "seconds": 5.715639}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 31072, "name": "MainProcess"}, "thread": {"id": 12040, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:32:07.473410+08:00", "timestamp": 1753839127.47341}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.716142", "seconds": 5.716142}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 31072, "name": "MainProcess"}, "thread": {"id": 12040, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:32:07.473913+08:00", "timestamp": 1753839127.473913}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:55.061658", "seconds": 55.061658}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 31072, "name": "MainProcess"}, "thread": {"id": 12040, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:32:56.819429+08:00", "timestamp": 1753839176.819429}}}
{"text": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:55.061658", "seconds": 55.061658}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 433, "message": "频率限制中间件错误: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "security", "name": "app.middleware.security", "process": {"id": 31072, "name": "MainProcess"}, "thread": {"id": 12040, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:32:56.819429+08:00", "timestamp": 1753839176.819429}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:55.063161", "seconds": 55.063161}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 31072, "name": "MainProcess"}, "thread": {"id": 12040, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:32:56.820932+08:00", "timestamp": 1753839176.820932}}}
{"text": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:55.063161", "seconds": 55.063161}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 47, "message": "国际化中间件处理失败: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "i18n", "name": "app.middleware.i18n", "process": {"id": 31072, "name": "MainProcess"}, "thread": {"id": 12040, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:32:56.820932+08:00", "timestamp": 1753839176.820932}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:55.064167", "seconds": 55.064167}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 31072, "name": "MainProcess"}, "thread": {"id": 12040, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:32:56.821938+08:00", "timestamp": 1753839176.821938}}}
{"text": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "record": {"elapsed": {"repr": "0:00:55.074265", "seconds": 55.074265}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 263, "message": "未处理的异常: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'", "module": "main", "name": "app.main", "process": {"id": 31072, "name": "MainProcess"}, "thread": {"id": 12040, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:32:56.832036+08:00", "timestamp": 1753839176.832036}}}
{"text": "异常堆栈:   + Exception Group Traceback (most recent call last):\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 76, in collapse_excgroups\n  |     yield\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 177, in __call__\n  |     async with anyio.create_task_group() as task_group:\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\anyio\\_backends\\_asyncio.py\", line 767, in __aexit__\n  |     raise BaseExceptionGroup(\n  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)\n  +-+---------------- 1 ----------------\n    | Traceback (most recent call last):\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\errors.py\", line 165, in __call__\n    |     await self.app(scope, receive, _send)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 51, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 38, in dispatch\n    |     response = await call_next(request)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py\", line 53, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 434, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 430, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 188, in dispatch\n    |     event_type=AuditEventType.SYSTEM_ERROR,\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    | AttributeError: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n    +------------------------------------\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\errors.py\", line 165, in __call__\n    await self.app(scope, receive, _send)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 51, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 38, in dispatch\n    response = await call_next(request)\n               ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py\", line 53, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 434, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 430, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 188, in dispatch\n    event_type=AuditEventType.SYSTEM_ERROR,\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n\n", "record": {"elapsed": {"repr": "0:00:55.074265", "seconds": 55.074265}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "general_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 264, "message": "异常堆栈:   + Exception Group Traceback (most recent call last):\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 76, in collapse_excgroups\n  |     yield\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 177, in __call__\n  |     async with anyio.create_task_group() as task_group:\n  |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\anyio\\_backends\\_asyncio.py\", line 767, in __aexit__\n  |     raise BaseExceptionGroup(\n  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)\n  +-+---------------- 1 ----------------\n    | Traceback (most recent call last):\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\errors.py\", line 165, in __call__\n    |     await self.app(scope, receive, _send)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 51, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 38, in dispatch\n    |     response = await call_next(request)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py\", line 53, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 434, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 430, in dispatch\n    |     return await call_next(request)\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    |     raise app_exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    |     await self.app(scope, receive_or_disconnect, send_no_error)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    |     with recv_stream, send_stream, collapse_excgroups():\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    |     self.gen.throw(value)\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    |     raise exc\n    |   File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    |     response = await self.dispatch_func(request, call_next)\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |   File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 188, in dispatch\n    |     event_type=AuditEventType.SYSTEM_ERROR,\n    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    | AttributeError: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n    +------------------------------------\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\errors.py\", line 165, in __call__\n    await self.app(scope, receive, _send)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 51, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\i18n.py\", line 38, in dispatch\n    response = await call_next(request)\n               ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py\", line 53, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 434, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 430, in dispatch\n    return await call_next(request)\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\contextlib.py\", line 158, in __exit__\n    self.gen.throw(value)\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"E:\\Program Files\\Python3.12.3\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py\", line 188, in dispatch\n    event_type=AuditEventType.SYSTEM_ERROR,\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: type object 'AuditEventType' has no attribute 'SYSTEM_ERROR'\n", "module": "main", "name": "app.main", "process": {"id": 31072, "name": "MainProcess"}, "thread": {"id": 12040, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:32:56.832036+08:00", "timestamp": 1753839176.832036}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.245111", "seconds": 1.245111}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 65328, "name": "MainProcess"}, "thread": {"id": 50732, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:36:50.044867+08:00", "timestamp": 1753839410.044867}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.246085", "seconds": 1.246085}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 65328, "name": "MainProcess"}, "thread": {"id": 50732, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:36:50.045841+08:00", "timestamp": 1753839410.045841}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.247059", "seconds": 1.247059}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 65328, "name": "MainProcess"}, "thread": {"id": 50732, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:36:50.046815+08:00", "timestamp": 1753839410.046815}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.247059", "seconds": 1.247059}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 65328, "name": "MainProcess"}, "thread": {"id": 50732, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:36:50.046815+08:00", "timestamp": 1753839410.046815}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.256056", "seconds": 5.256056}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 65328, "name": "MainProcess"}, "thread": {"id": 50732, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:36:54.055812+08:00", "timestamp": 1753839414.055812}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.277164", "seconds": 5.277164}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 65328, "name": "MainProcess"}, "thread": {"id": 50732, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:36:54.076920+08:00", "timestamp": 1753839414.07692}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.277164", "seconds": 5.277164}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 65328, "name": "MainProcess"}, "thread": {"id": 50732, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:36:54.076920+08:00", "timestamp": 1753839414.07692}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:29.438265", "seconds": 29.438265}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 65328, "name": "MainProcess"}, "thread": {"id": 50732, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:37:18.238021+08:00", "timestamp": **********.238021}}}
{"text": "安全警报: system_error - {'error': \"'coroutine' object has no attribute 'get'\", 'endpoint': '/health', 'method': 'GET'}\n", "record": {"elapsed": {"repr": "0:00:29.440211", "seconds": 29.440211}, "exception": null, "extra": {}, "file": {"name": "audit_logger.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\audit_logger.py"}, "function": "_send_security_alert", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 282, "message": "安全警报: system_error - {'error': \"'coroutine' object has no attribute 'get'\", 'endpoint': '/health', 'method': 'GET'}", "module": "audit_logger", "name": "app.utils.audit_logger", "process": {"id": 65328, "name": "MainProcess"}, "thread": {"id": 50732, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:37:18.239967+08:00", "timestamp": **********.239967}}}
{"text": "缓存审计事件失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:29.442242", "seconds": 29.442242}, "exception": null, "extra": {}, "file": {"name": "audit_logger.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\audit_logger.py"}, "function": "_cache_recent_event", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 320, "message": "缓存审计事件失败: 'coroutine' object has no attribute 'get'", "module": "audit_logger", "name": "app.utils.audit_logger", "process": {"id": 65328, "name": "MainProcess"}, "thread": {"id": 50732, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:37:18.241998+08:00", "timestamp": **********.241998}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.281912", "seconds": 1.281912}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 64200, "name": "MainProcess"}, "thread": {"id": 42124, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:41:56.972926+08:00", "timestamp": 1753839716.972926}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.282886", "seconds": 1.282886}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 64200, "name": "MainProcess"}, "thread": {"id": 42124, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:41:56.973900+08:00", "timestamp": 1753839716.9739}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.283859", "seconds": 1.283859}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 64200, "name": "MainProcess"}, "thread": {"id": 42124, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:41:56.974873+08:00", "timestamp": 1753839716.974873}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.283859", "seconds": 1.283859}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 64200, "name": "MainProcess"}, "thread": {"id": 42124, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:41:56.974873+08:00", "timestamp": 1753839716.974873}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.295895", "seconds": 5.295895}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 64200, "name": "MainProcess"}, "thread": {"id": 42124, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:42:00.986909+08:00", "timestamp": 1753839720.986909}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.318426", "seconds": 5.318426}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 64200, "name": "MainProcess"}, "thread": {"id": 42124, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:42:01.009440+08:00", "timestamp": 1753839721.00944}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.318426", "seconds": 5.318426}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 64200, "name": "MainProcess"}, "thread": {"id": 42124, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:42:01.009440+08:00", "timestamp": 1753839721.00944}}}
{"text": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'\n", "record": {"elapsed": {"repr": "0:00:39.793810", "seconds": 39.79381}, "exception": null, "extra": {}, "file": {"name": "security.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\security.py"}, "function": "dispatch", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 184, "message": "安全中间件处理请求失败: 'coroutine' object has no attribute 'get'", "module": "security", "name": "app.middleware.security", "process": {"id": 64200, "name": "MainProcess"}, "thread": {"id": 42124, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:42:35.484824+08:00", "timestamp": **********.484824}}}
{"text": "安全警报: system_error - {'error': \"'coroutine' object has no attribute 'get'\", 'endpoint': '/health', 'method': 'GET'}\n", "record": {"elapsed": {"repr": "0:00:39.794783", "seconds": 39.794783}, "exception": null, "extra": {}, "file": {"name": "audit_logger.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\audit_logger.py"}, "function": "_send_security_alert", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 282, "message": "安全警报: system_error - {'error': \"'coroutine' object has no attribute 'get'\", 'endpoint': '/health', 'method': 'GET'}", "module": "audit_logger", "name": "app.utils.audit_logger", "process": {"id": 64200, "name": "MainProcess"}, "thread": {"id": 42124, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:42:35.485797+08:00", "timestamp": **********.485797}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:43.804012", "seconds": 43.804012}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 64200, "name": "MainProcess"}, "thread": {"id": 42124, "name": "MainThread"}, "time": {"repr": "2025-07-30 09:42:39.495026+08:00", "timestamp": 1753839759.495026}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.478154", "seconds": 1.478154}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 63800, "name": "MainProcess"}, "thread": {"id": 44656, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:05:04.283697+08:00", "timestamp": 1753841104.283697}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.478154", "seconds": 1.478154}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 63800, "name": "MainProcess"}, "thread": {"id": 44656, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:05:04.283697+08:00", "timestamp": 1753841104.283697}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.479128", "seconds": 1.479128}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 63800, "name": "MainProcess"}, "thread": {"id": 44656, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:05:04.284671+08:00", "timestamp": 1753841104.284671}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.479128", "seconds": 1.479128}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 63800, "name": "MainProcess"}, "thread": {"id": 44656, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:05:04.284671+08:00", "timestamp": 1753841104.284671}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.487273", "seconds": 5.487273}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 63800, "name": "MainProcess"}, "thread": {"id": 44656, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:05:08.292816+08:00", "timestamp": 1753841108.292816}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.505956", "seconds": 5.505956}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 63800, "name": "MainProcess"}, "thread": {"id": 44656, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:05:08.311499+08:00", "timestamp": 1753841108.311499}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.505956", "seconds": 5.505956}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 63800, "name": "MainProcess"}, "thread": {"id": 44656, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:05:08.311499+08:00", "timestamp": 1753841108.311499}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:37.136262", "seconds": 37.136262}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 63800, "name": "MainProcess"}, "thread": {"id": 44656, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:05:39.941805+08:00", "timestamp": 1753841139.941805}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:54.026078", "seconds": 54.026078}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 63800, "name": "MainProcess"}, "thread": {"id": 44656, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:05:56.831621+08:00", "timestamp": 1753841156.831621}}}
{"text": "慢请求: {'method': 'GET', 'path': '/', 'status_code': 401, 'process_time': 4.011, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}\n", "record": {"elapsed": {"repr": "0:00:54.029491", "seconds": 54.029491}, "exception": null, "extra": {}, "file": {"name": "rate_limit.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py"}, "function": "_log_request", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 276, "message": "慢请求: {'method': 'GET', 'path': '/', 'status_code': 401, 'process_time': 4.011, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}", "module": "rate_limit", "name": "app.middleware.rate_limit", "process": {"id": 63800, "name": "MainProcess"}, "thread": {"id": 44656, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:05:56.835034+08:00", "timestamp": 1753841156.835034}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.460174", "seconds": 1.460174}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 58992, "name": "MainProcess"}, "thread": {"id": 59164, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:07:47.247773+08:00", "timestamp": 1753841267.247773}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.460174", "seconds": 1.460174}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 58992, "name": "MainProcess"}, "thread": {"id": 59164, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:07:47.247773+08:00", "timestamp": 1753841267.247773}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.461148", "seconds": 1.461148}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 58992, "name": "MainProcess"}, "thread": {"id": 59164, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:07:47.248747+08:00", "timestamp": 1753841267.248747}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.462122", "seconds": 1.462122}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 58992, "name": "MainProcess"}, "thread": {"id": 59164, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:07:47.249721+08:00", "timestamp": 1753841267.249721}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.472201", "seconds": 5.472201}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 58992, "name": "MainProcess"}, "thread": {"id": 59164, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:07:51.259800+08:00", "timestamp": 1753841271.2598}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.488189", "seconds": 5.488189}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 58992, "name": "MainProcess"}, "thread": {"id": 59164, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:07:51.275788+08:00", "timestamp": 1753841271.275788}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.489163", "seconds": 5.489163}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 58992, "name": "MainProcess"}, "thread": {"id": 59164, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:07:51.276762+08:00", "timestamp": 1753841271.276762}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:35.753865", "seconds": 35.753865}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 58992, "name": "MainProcess"}, "thread": {"id": 59164, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:08:21.541464+08:00", "timestamp": 1753841301.541464}}}
{"text": "记录性能数据失败: loguru._logger.Logger.bind() got multiple values for keyword argument 'category'\n", "record": {"elapsed": {"repr": "0:00:35.773053", "seconds": 35.773053}, "exception": null, "extra": {}, "file": {"name": "performance.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\performance.py"}, "function": "_record_performance_data", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 93, "message": "记录性能数据失败: loguru._logger.Logger.bind() got multiple values for keyword argument 'category'", "module": "performance", "name": "app.middleware.performance", "process": {"id": 58992, "name": "MainProcess"}, "thread": {"id": 59164, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:08:21.560652+08:00", "timestamp": 1753841301.560652}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:39.782956", "seconds": 39.782956}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 58992, "name": "MainProcess"}, "thread": {"id": 59164, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:08:25.570555+08:00", "timestamp": 1753841305.570555}}}
{"text": "慢请求: {'method': 'GET', 'path': '/', 'status_code': 200, 'process_time': 8.039, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}\n", "record": {"elapsed": {"repr": "0:00:39.782956", "seconds": 39.782956}, "exception": null, "extra": {}, "file": {"name": "rate_limit.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py"}, "function": "_log_request", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 276, "message": "慢请求: {'method': 'GET', 'path': '/', 'status_code': 200, 'process_time': 8.039, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}", "module": "rate_limit", "name": "app.middleware.rate_limit", "process": {"id": 58992, "name": "MainProcess"}, "thread": {"id": 59164, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:08:25.570555+08:00", "timestamp": 1753841305.570555}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:56.287846", "seconds": 56.287846}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 58992, "name": "MainProcess"}, "thread": {"id": 59164, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:08:42.075445+08:00", "timestamp": 1753841322.075445}}}
{"text": "记录性能数据失败: loguru._logger.Logger.bind() got multiple values for keyword argument 'category'\n", "record": {"elapsed": {"repr": "0:00:56.291739", "seconds": 56.291739}, "exception": null, "extra": {}, "file": {"name": "performance.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\performance.py"}, "function": "_record_performance_data", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 93, "message": "记录性能数据失败: loguru._logger.Logger.bind() got multiple values for keyword argument 'category'", "module": "performance", "name": "app.middleware.performance", "process": {"id": 58992, "name": "MainProcess"}, "thread": {"id": 59164, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:08:42.079338+08:00", "timestamp": 1753841322.079338}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:01:00.297056", "seconds": 60.297056}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 58992, "name": "MainProcess"}, "thread": {"id": 59164, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:08:46.084655+08:00", "timestamp": 1753841326.084655}}}
{"text": "慢请求: {'method': 'GET', 'path': '/', 'status_code': 200, 'process_time': 8.021, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}\n", "record": {"elapsed": {"repr": "0:01:00.298029", "seconds": 60.298029}, "exception": null, "extra": {}, "file": {"name": "rate_limit.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py"}, "function": "_log_request", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 276, "message": "慢请求: {'method': 'GET', 'path': '/', 'status_code': 200, 'process_time': 8.021, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}", "module": "rate_limit", "name": "app.middleware.rate_limit", "process": {"id": 58992, "name": "MainProcess"}, "thread": {"id": 59164, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:08:46.085628+08:00", "timestamp": 1753841326.085628}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.425365", "seconds": 1.425365}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 60544, "name": "MainProcess"}, "thread": {"id": 56360, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:12:55.341662+08:00", "timestamp": 1753841575.341662}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.426339", "seconds": 1.426339}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 60544, "name": "MainProcess"}, "thread": {"id": 56360, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:12:55.342636+08:00", "timestamp": 1753841575.342636}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.427312", "seconds": 1.427312}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 60544, "name": "MainProcess"}, "thread": {"id": 56360, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:12:55.343609+08:00", "timestamp": 1753841575.343609}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.427312", "seconds": 1.427312}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 60544, "name": "MainProcess"}, "thread": {"id": 56360, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:12:55.343609+08:00", "timestamp": 1753841575.343609}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.437212", "seconds": 5.437212}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 60544, "name": "MainProcess"}, "thread": {"id": 56360, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:12:59.353509+08:00", "timestamp": 1753841579.353509}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.455466", "seconds": 5.455466}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 60544, "name": "MainProcess"}, "thread": {"id": 56360, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:12:59.371763+08:00", "timestamp": 1753841579.371763}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.455466", "seconds": 5.455466}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 60544, "name": "MainProcess"}, "thread": {"id": 56360, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:12:59.371763+08:00", "timestamp": 1753841579.371763}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:52.560146", "seconds": 52.560146}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 60544, "name": "MainProcess"}, "thread": {"id": 56360, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:13:46.476443+08:00", "timestamp": 1753841626.476443}}}
{"text": "{\"timestamp\": \"2025-07-30T10:13:46.494731\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.0009732246398925781 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.0009732246398925781, \"unit\": \"seconds\", \"endpoint\": \"GET /\", \"status_code\": 200, \"user_id\": null}}\n", "record": {"elapsed": {"repr": "0:00:52.578434", "seconds": 52.578434}, "exception": null, "extra": {"user_id": null, "request_id": null, "timestamp": "2025-07-30T10:13:46.494731", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.0009732246398925781 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "session_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.0009732246398925781, "unit": "seconds", "endpoint": "GET /", "status_code": 200, "user_id": null}}, "file": {"name": "structured_logger.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\structured_logger.py"}, "function": "_log_structured", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 200, "message": "{\"timestamp\": \"2025-07-30T10:13:46.494731\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.0009732246398925781 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.0009732246398925781, \"unit\": \"seconds\", \"endpoint\": \"GET /\", \"status_code\": 200, \"user_id\": null}}", "module": "structured_logger", "name": "app.utils.structured_logger", "process": {"id": 60544, "name": "MainProcess"}, "thread": {"id": 56360, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:13:46.494731+08:00", "timestamp": 1753841626.494731}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:56.586502", "seconds": 56.586502}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 60544, "name": "MainProcess"}, "thread": {"id": 56360, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:13:50.502799+08:00", "timestamp": 1753841630.502799}}}
{"text": "慢请求: {'method': 'GET', 'path': '/', 'status_code': 200, 'process_time': 8.032, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}\n", "record": {"elapsed": {"repr": "0:00:56.587466", "seconds": 56.587466}, "exception": null, "extra": {}, "file": {"name": "rate_limit.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py"}, "function": "_log_request", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 276, "message": "慢请求: {'method': 'GET', 'path': '/', 'status_code': 200, 'process_time': 8.032, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}", "module": "rate_limit", "name": "app.middleware.rate_limit", "process": {"id": 60544, "name": "MainProcess"}, "thread": {"id": 56360, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:13:50.503763+08:00", "timestamp": 1753841630.503763}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:01:53.928308", "seconds": 113.928308}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 60544, "name": "MainProcess"}, "thread": {"id": 56360, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:14:47.844605+08:00", "timestamp": 1753841687.844605}}}
{"text": "慢请求: {'method': 'POST', 'path': '/api/v1/auth/register', 'status_code': 401, 'process_time': 4.007, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}\n", "record": {"elapsed": {"repr": "0:01:53.928308", "seconds": 113.928308}, "exception": null, "extra": {}, "file": {"name": "rate_limit.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py"}, "function": "_log_request", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 276, "message": "慢请求: {'method': 'POST', 'path': '/api/v1/auth/register', 'status_code': 401, 'process_time': 4.007, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}", "module": "rate_limit", "name": "app.middleware.rate_limit", "process": {"id": 60544, "name": "MainProcess"}, "thread": {"id": 56360, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:14:47.844605+08:00", "timestamp": 1753841687.844605}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.497760", "seconds": 1.49776}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:31:30.433426+08:00", "timestamp": 1753842690.433426}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.497760", "seconds": 1.49776}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:31:30.433426+08:00", "timestamp": 1753842690.433426}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.498733", "seconds": 1.498733}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:31:30.434399+08:00", "timestamp": 1753842690.434399}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.498733", "seconds": 1.498733}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:31:30.434399+08:00", "timestamp": 1753842690.434399}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.516802", "seconds": 5.516802}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:31:34.452468+08:00", "timestamp": 1753842694.452468}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.537530", "seconds": 5.53753}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:31:34.473196+08:00", "timestamp": 1753842694.473196}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.538503", "seconds": 5.538503}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:31:34.474169+08:00", "timestamp": 1753842694.474169}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:02:03.396885", "seconds": 123.396885}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:33:32.332551+08:00", "timestamp": 1753842812.332551}}}
{"text": "数据库会话错误: 400: 邮箱已存在\n", "record": {"elapsed": {"repr": "0:02:03.445549", "seconds": 123.445549}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "get_db", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 103, "message": "数据库会话错误: 400: 邮箱已存在", "module": "connection", "name": "app.database.connection", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:33:32.381215+08:00", "timestamp": 1753842812.381215}}}
{"text": "HTTP异常: 400 - 邮箱已存在\n", "record": {"elapsed": {"repr": "0:02:03.446585", "seconds": 123.446585}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "http_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 246, "message": "HTTP异常: 400 - 邮箱已存在", "module": "main", "name": "app.main", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:33:32.382251+08:00", "timestamp": 1753842812.382251}}}
{"text": "HTTP错误: 400 - POST /api/v1/auth/register\n", "record": {"elapsed": {"repr": "0:02:03.446585", "seconds": 123.446585}, "exception": null, "extra": {}, "file": {"name": "performance.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\performance.py"}, "function": "_record_error", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 308, "message": "HTTP错误: 400 - POST /api/v1/auth/register", "module": "performance", "name": "app.middleware.performance", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:33:32.382251+08:00", "timestamp": 1753842812.382251}}}
{"text": "{\"timestamp\": \"2025-07-30T10:33:32.390684\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.03194069862365723 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.03194069862365723, \"unit\": \"seconds\", \"endpoint\": \"POST /api/v1/auth/register\", \"status_code\": 400, \"user_id\": null}}\n", "record": {"elapsed": {"repr": "0:02:03.455018", "seconds": 123.455018}, "exception": null, "extra": {"user_id": null, "request_id": null, "timestamp": "2025-07-30T10:33:32.390684", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.03194069862365723 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "session_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.03194069862365723, "unit": "seconds", "endpoint": "POST /api/v1/auth/register", "status_code": 400, "user_id": null}}, "file": {"name": "structured_logger.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\structured_logger.py"}, "function": "_log_structured", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 200, "message": "{\"timestamp\": \"2025-07-30T10:33:32.390684\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.03194069862365723 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.03194069862365723, \"unit\": \"seconds\", \"endpoint\": \"POST /api/v1/auth/register\", \"status_code\": 400, \"user_id\": null}}", "module": "structured_logger", "name": "app.utils.structured_logger", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:33:32.390684+08:00", "timestamp": 1753842812.390684}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:02:07.469637", "seconds": 127.469637}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:33:36.405303+08:00", "timestamp": 1753842816.405303}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:02:11.481180", "seconds": 131.48118}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:33:40.416846+08:00", "timestamp": 1753842820.416846}}}
{"text": "慢请求: {'method': 'POST', 'path': '/api/v1/auth/register', 'status_code': 400, 'process_time': 12.096, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}\n", "record": {"elapsed": {"repr": "0:02:11.482148", "seconds": 131.482148}, "exception": null, "extra": {}, "file": {"name": "rate_limit.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py"}, "function": "_log_request", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 276, "message": "慢请求: {'method': 'POST', 'path': '/api/v1/auth/register', 'status_code': 400, 'process_time': 12.096, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}", "module": "rate_limit", "name": "app.middleware.rate_limit", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:33:40.417814+08:00", "timestamp": 1753842820.417814}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:02:16.728233", "seconds": 136.728233}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:33:45.663899+08:00", "timestamp": 1753842825.663899}}}
{"text": "数据库会话错误: 400: 邮箱已存在\n", "record": {"elapsed": {"repr": "0:02:16.733155", "seconds": 136.733155}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "get_db", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 103, "message": "数据库会话错误: 400: 邮箱已存在", "module": "connection", "name": "app.database.connection", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:33:45.668821+08:00", "timestamp": 1753842825.668821}}}
{"text": "HTTP异常: 400 - 邮箱已存在\n", "record": {"elapsed": {"repr": "0:02:16.734662", "seconds": 136.734662}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "http_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 246, "message": "HTTP异常: 400 - 邮箱已存在", "module": "main", "name": "app.main", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:33:45.670328+08:00", "timestamp": 1753842825.670328}}}
{"text": "HTTP错误: 400 - POST /api/v1/auth/register\n", "record": {"elapsed": {"repr": "0:02:16.734662", "seconds": 136.734662}, "exception": null, "extra": {}, "file": {"name": "performance.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\performance.py"}, "function": "_record_error", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 308, "message": "HTTP错误: 400 - POST /api/v1/auth/register", "module": "performance", "name": "app.middleware.performance", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:33:45.670328+08:00", "timestamp": 1753842825.670328}}}
{"text": "{\"timestamp\": \"2025-07-30T10:33:45.671323\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.006429195404052734 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.006429195404052734, \"unit\": \"seconds\", \"endpoint\": \"POST /api/v1/auth/register\", \"status_code\": 400, \"user_id\": null}}\n", "record": {"elapsed": {"repr": "0:02:16.735657", "seconds": 136.735657}, "exception": null, "extra": {"user_id": null, "request_id": null, "timestamp": "2025-07-30T10:33:45.671323", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.006429195404052734 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "session_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.006429195404052734, "unit": "seconds", "endpoint": "POST /api/v1/auth/register", "status_code": 400, "user_id": null}}, "file": {"name": "structured_logger.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\structured_logger.py"}, "function": "_log_structured", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 200, "message": "{\"timestamp\": \"2025-07-30T10:33:45.671323\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.006429195404052734 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.006429195404052734, \"unit\": \"seconds\", \"endpoint\": \"POST /api/v1/auth/register\", \"status_code\": 400, \"user_id\": null}}", "module": "structured_logger", "name": "app.utils.structured_logger", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:33:45.671323+08:00", "timestamp": 1753842825.671323}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:02:20.743496", "seconds": 140.743496}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:33:49.679162+08:00", "timestamp": 1753842829.679162}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:02:24.751339", "seconds": 144.751339}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:33:53.687005+08:00", "timestamp": 1753842833.687005}}}
{"text": "慢请求: {'method': 'POST', 'path': '/api/v1/auth/register', 'status_code': 400, 'process_time': 12.035, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}\n", "record": {"elapsed": {"repr": "0:02:24.752899", "seconds": 144.752899}, "exception": null, "extra": {}, "file": {"name": "rate_limit.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py"}, "function": "_log_request", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 276, "message": "慢请求: {'method': 'POST', 'path': '/api/v1/auth/register', 'status_code': 400, 'process_time': 12.035, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}", "module": "rate_limit", "name": "app.middleware.rate_limit", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:33:53.688565+08:00", "timestamp": 1753842833.688565}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:02:56.958249", "seconds": 176.958249}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:34:25.893915+08:00", "timestamp": 1753842865.893915}}}
{"text": "数据库会话错误: 400: 邮箱已存在\n", "record": {"elapsed": {"repr": "0:02:56.974499", "seconds": 176.974499}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "get_db", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 103, "message": "数据库会话错误: 400: 邮箱已存在", "module": "connection", "name": "app.database.connection", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:34:25.910165+08:00", "timestamp": 1753842865.910165}}}
{"text": "HTTP异常: 400 - 邮箱已存在\n", "record": {"elapsed": {"repr": "0:02:56.974499", "seconds": 176.974499}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "http_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 246, "message": "HTTP异常: 400 - 邮箱已存在", "module": "main", "name": "app.main", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:34:25.910165+08:00", "timestamp": 1753842865.910165}}}
{"text": "HTTP错误: 400 - POST /api/v1/auth/register\n", "record": {"elapsed": {"repr": "0:02:56.975491", "seconds": 176.975491}, "exception": null, "extra": {}, "file": {"name": "performance.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\performance.py"}, "function": "_record_error", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 308, "message": "HTTP错误: 400 - POST /api/v1/auth/register", "module": "performance", "name": "app.middleware.performance", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:34:25.911157+08:00", "timestamp": 1753842865.911157}}}
{"text": "{\"timestamp\": \"2025-07-30T10:34:25.912130\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.006928443908691406 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.006928443908691406, \"unit\": \"seconds\", \"endpoint\": \"POST /api/v1/auth/register\", \"status_code\": 400, \"user_id\": null}}\n", "record": {"elapsed": {"repr": "0:02:56.976464", "seconds": 176.976464}, "exception": null, "extra": {"user_id": null, "request_id": null, "timestamp": "2025-07-30T10:34:25.912130", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.006928443908691406 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "session_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.006928443908691406, "unit": "seconds", "endpoint": "POST /api/v1/auth/register", "status_code": 400, "user_id": null}}, "file": {"name": "structured_logger.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\structured_logger.py"}, "function": "_log_structured", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 200, "message": "{\"timestamp\": \"2025-07-30T10:34:25.912130\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.006928443908691406 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.006928443908691406, \"unit\": \"seconds\", \"endpoint\": \"POST /api/v1/auth/register\", \"status_code\": 400, \"user_id\": null}}", "module": "structured_logger", "name": "app.utils.structured_logger", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:34:25.912130+08:00", "timestamp": 1753842865.91213}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:03:00.986775", "seconds": 180.986775}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:34:29.922441+08:00", "timestamp": 1753842869.922441}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:03:04.996129", "seconds": 184.996129}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:34:33.931795+08:00", "timestamp": 1753842873.931795}}}
{"text": "慢请求: {'method': 'POST', 'path': '/api/v1/auth/register', 'status_code': 400, 'process_time': 12.047, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}\n", "record": {"elapsed": {"repr": "0:03:04.998100", "seconds": 184.9981}, "exception": null, "extra": {}, "file": {"name": "rate_limit.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py"}, "function": "_log_request", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 276, "message": "慢请求: {'method': 'POST', 'path': '/api/v1/auth/register', 'status_code': 400, 'process_time': 12.047, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}", "module": "rate_limit", "name": "app.middleware.rate_limit", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:34:33.933766+08:00", "timestamp": 1753842873.933766}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:03:50.107797", "seconds": 230.107797}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:19.043463+08:00", "timestamp": 1753842919.043463}}}
{"text": "数据库会话错误: 400: 邮箱已存在\n", "record": {"elapsed": {"repr": "0:03:50.129272", "seconds": 230.129272}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "get_db", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 103, "message": "数据库会话错误: 400: 邮箱已存在", "module": "connection", "name": "app.database.connection", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:19.064938+08:00", "timestamp": 1753842919.064938}}}
{"text": "HTTP异常: 400 - 邮箱已存在\n", "record": {"elapsed": {"repr": "0:03:50.130245", "seconds": 230.130245}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "http_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 246, "message": "HTTP异常: 400 - 邮箱已存在", "module": "main", "name": "app.main", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:19.065911+08:00", "timestamp": 1753842919.065911}}}
{"text": "HTTP错误: 400 - POST /api/v1/auth/register\n", "record": {"elapsed": {"repr": "0:03:50.130245", "seconds": 230.130245}, "exception": null, "extra": {}, "file": {"name": "performance.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\performance.py"}, "function": "_record_error", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 308, "message": "HTTP错误: 400 - POST /api/v1/auth/register", "module": "performance", "name": "app.middleware.performance", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:19.065911+08:00", "timestamp": 1753842919.065911}}}
{"text": "{\"timestamp\": \"2025-07-30T10:35:19.068837\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.009354352951049805 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.009354352951049805, \"unit\": \"seconds\", \"endpoint\": \"POST /api/v1/auth/register\", \"status_code\": 400, \"user_id\": null}}\n", "record": {"elapsed": {"repr": "0:03:50.133171", "seconds": 230.133171}, "exception": null, "extra": {"user_id": null, "request_id": null, "timestamp": "2025-07-30T10:35:19.068837", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.009354352951049805 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "session_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.009354352951049805, "unit": "seconds", "endpoint": "POST /api/v1/auth/register", "status_code": 400, "user_id": null}}, "file": {"name": "structured_logger.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\structured_logger.py"}, "function": "_log_structured", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 200, "message": "{\"timestamp\": \"2025-07-30T10:35:19.068837\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.009354352951049805 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.009354352951049805, \"unit\": \"seconds\", \"endpoint\": \"POST /api/v1/auth/register\", \"status_code\": 400, \"user_id\": null}}", "module": "structured_logger", "name": "app.utils.structured_logger", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:19.068837+08:00", "timestamp": 1753842919.068837}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:03:54.143669", "seconds": 234.143669}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:23.079335+08:00", "timestamp": 1753842923.079335}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:03:58.154576", "seconds": 238.154576}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:27.090242+08:00", "timestamp": 1753842927.090242}}}
{"text": "慢请求: {'method': 'POST', 'path': '/api/v1/auth/register', 'status_code': 400, 'process_time': 12.056, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}\n", "record": {"elapsed": {"repr": "0:03:58.155584", "seconds": 238.155584}, "exception": null, "extra": {}, "file": {"name": "rate_limit.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py"}, "function": "_log_request", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 276, "message": "慢请求: {'method': 'POST', 'path': '/api/v1/auth/register', 'status_code': 400, 'process_time': 12.056, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}", "module": "rate_limit", "name": "app.middleware.rate_limit", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:27.091250+08:00", "timestamp": 1753842927.09125}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:04:02.165726", "seconds": 242.165726}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:31.101392+08:00", "timestamp": 1753842931.101392}}}
{"text": "{\"timestamp\": \"2025-07-30T10:35:31.412310\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.30794572830200195 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.30794572830200195, \"unit\": \"seconds\", \"endpoint\": \"POST /api/v1/auth/login\", \"status_code\": 200, \"user_id\": null}}\n", "record": {"elapsed": {"repr": "0:04:02.476644", "seconds": 242.476644}, "exception": null, "extra": {"user_id": null, "request_id": null, "timestamp": "2025-07-30T10:35:31.412310", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.30794572830200195 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "session_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.30794572830200195, "unit": "seconds", "endpoint": "POST /api/v1/auth/login", "status_code": 200, "user_id": null}}, "file": {"name": "structured_logger.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\structured_logger.py"}, "function": "_log_structured", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 200, "message": "{\"timestamp\": \"2025-07-30T10:35:31.412310\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.30794572830200195 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.30794572830200195, \"unit\": \"seconds\", \"endpoint\": \"POST /api/v1/auth/login\", \"status_code\": 200, \"user_id\": null}}", "module": "structured_logger", "name": "app.utils.structured_logger", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:31.412310+08:00", "timestamp": 1753842931.41231}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:04:06.483528", "seconds": 246.483528}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:35.419194+08:00", "timestamp": 1753842935.419194}}}
{"text": "慢请求: {'method': 'POST', 'path': '/api/v1/auth/login', 'status_code': 200, 'process_time': 8.324, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}\n", "record": {"elapsed": {"repr": "0:04:06.483528", "seconds": 246.483528}, "exception": null, "extra": {}, "file": {"name": "rate_limit.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py"}, "function": "_log_request", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 276, "message": "慢请求: {'method': 'POST', 'path': '/api/v1/auth/login', 'status_code': 200, 'process_time': 8.324, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}", "module": "rate_limit", "name": "app.middleware.rate_limit", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:35.419194+08:00", "timestamp": 1753842935.419194}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:04:10.494869", "seconds": 250.494869}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:39.430535+08:00", "timestamp": 1753842939.430535}}}
{"text": "数据库会话错误: LogEntry.__init__() got an unexpected keyword argument 'error'\n", "record": {"elapsed": {"repr": "0:04:10.716803", "seconds": 250.716803}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "get_db", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 103, "message": "数据库会话错误: LogEntry.__init__() got an unexpected keyword argument 'error'", "module": "connection", "name": "app.database.connection", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:39.652469+08:00", "timestamp": 1753842939.652469}}}
{"text": "未捕获异常: POST /api/v1/batch/transactions - LogEntry.__init__() got an unexpected keyword argument 'error'\n", "record": {"elapsed": {"repr": "0:04:10.719723", "seconds": 250.719723}, "exception": null, "extra": {}, "file": {"name": "performance.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\performance.py"}, "function": "_record_exception", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 320, "message": "未捕获异常: POST /api/v1/batch/transactions - LogEntry.__init__() got an unexpected keyword argument 'error'", "module": "performance", "name": "app.middleware.performance", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:39.655389+08:00", "timestamp": 1753842939.655389}}}
{"text": "{\"timestamp\": \"2025-07-30T10:35:39.662261\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.21198654174804688 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.21198654174804688, \"unit\": \"seconds\", \"endpoint\": \"POST /api/v1/batch/transactions\", \"status_code\": 500, \"user_id\": \"cc80f617-b43c-4746-97f2-e6c22edf2d50\"}}\n", "record": {"elapsed": {"repr": "0:04:10.726595", "seconds": 250.726595}, "exception": null, "extra": {"user_id": null, "request_id": null, "timestamp": "2025-07-30T10:35:39.662261", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.21198654174804688 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "session_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.21198654174804688, "unit": "seconds", "endpoint": "POST /api/v1/batch/transactions", "status_code": 500, "user_id": "cc80f617-b43c-4746-97f2-e6c22edf2d50"}}, "file": {"name": "structured_logger.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\structured_logger.py"}, "function": "_log_structured", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 200, "message": "{\"timestamp\": \"2025-07-30T10:35:39.662261\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.21198654174804688 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.21198654174804688, \"unit\": \"seconds\", \"endpoint\": \"POST /api/v1/batch/transactions\", \"status_code\": 500, \"user_id\": \"cc80f617-b43c-4746-97f2-e6c22edf2d50\"}}", "module": "structured_logger", "name": "app.utils.structured_logger", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:39.662261+08:00", "timestamp": 1753842939.662261}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:04:14.735585", "seconds": 254.735585}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:43.671251+08:00", "timestamp": 1753842943.671251}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:04:18.744675", "seconds": 258.744675}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:47.680341+08:00", "timestamp": 1753842947.680341}}}
{"text": "慢请求: {'method': 'POST', 'path': '/api/v1/batch/transactions', 'status_code': 500, 'process_time': 12.257, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}\n", "record": {"elapsed": {"repr": "0:04:18.745685", "seconds": 258.745685}, "exception": null, "extra": {}, "file": {"name": "rate_limit.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py"}, "function": "_log_request", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 276, "message": "慢请求: {'method': 'POST', 'path': '/api/v1/batch/transactions', 'status_code': 500, 'process_time': 12.257, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}", "module": "rate_limit", "name": "app.middleware.rate_limit", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:35:47.681351+08:00", "timestamp": 1753842947.681351}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:04:34.646863", "seconds": 274.646863}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:36:03.582529+08:00", "timestamp": 1753842963.582529}}}
{"text": "{\"timestamp\": \"2025-07-30T10:36:03.603398\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.0009944438934326172 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.0009944438934326172, \"unit\": \"seconds\", \"endpoint\": \"GET /\", \"status_code\": 200, \"user_id\": null}}\n", "record": {"elapsed": {"repr": "0:04:34.667732", "seconds": 274.667732}, "exception": null, "extra": {"user_id": null, "request_id": null, "timestamp": "2025-07-30T10:36:03.603398", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.0009944438934326172 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "session_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.0009944438934326172, "unit": "seconds", "endpoint": "GET /", "status_code": 200, "user_id": null}}, "file": {"name": "structured_logger.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\structured_logger.py"}, "function": "_log_structured", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 200, "message": "{\"timestamp\": \"2025-07-30T10:36:03.603398\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.0009944438934326172 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.0009944438934326172, \"unit\": \"seconds\", \"endpoint\": \"GET /\", \"status_code\": 200, \"user_id\": null}}", "module": "structured_logger", "name": "app.utils.structured_logger", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:36:03.603398+08:00", "timestamp": 1753842963.603398}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:04:38.673569", "seconds": 278.673569}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:36:07.609235+08:00", "timestamp": 1753842967.609235}}}
{"text": "慢请求: {'method': 'GET', 'path': '/', 'status_code': 200, 'process_time': 8.031, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}\n", "record": {"elapsed": {"repr": "0:04:38.674572", "seconds": 278.674572}, "exception": null, "extra": {}, "file": {"name": "rate_limit.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py"}, "function": "_log_request", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 276, "message": "慢请求: {'method': 'GET', 'path': '/', 'status_code': 200, 'process_time': 8.031, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}", "module": "rate_limit", "name": "app.middleware.rate_limit", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:36:07.610238+08:00", "timestamp": 1753842967.610238}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:04:42.687890", "seconds": 282.68789}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:36:11.623556+08:00", "timestamp": 1753842971.623556}}}
{"text": "{\"timestamp\": \"2025-07-30T10:36:11.935421\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.3089447021484375 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.3089447021484375, \"unit\": \"seconds\", \"endpoint\": \"POST /api/v1/auth/login\", \"status_code\": 200, \"user_id\": null}}\n", "record": {"elapsed": {"repr": "0:04:42.999755", "seconds": 282.999755}, "exception": null, "extra": {"user_id": null, "request_id": null, "timestamp": "2025-07-30T10:36:11.935421", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.3089447021484375 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "session_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.3089447021484375, "unit": "seconds", "endpoint": "POST /api/v1/auth/login", "status_code": 200, "user_id": null}}, "file": {"name": "structured_logger.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\structured_logger.py"}, "function": "_log_structured", "level": {"icon": "🐞", "name": "DEBUG", "no": 10}, "line": 200, "message": "{\"timestamp\": \"2025-07-30T10:36:11.935421\", \"level\": \"DEBUG\", \"category\": \"performance\", \"message\": \"性能指标: api_response_time = 0.3089447021484375 seconds\", \"module\": \"E:\\\\relax\\\\python\\\\源码\\\\记账报销软件\\\\backend\\\\app\\\\utils\\\\performance_monitor.py\", \"function\": \"record_request\", \"line\": 111, \"user_id\": null, \"session_id\": null, \"request_id\": null, \"ip_address\": null, \"user_agent\": null, \"endpoint\": null, \"method\": null, \"status_code\": null, \"response_time\": null, \"error_type\": null, \"error_message\": null, \"stack_trace\": null, \"extra_data\": {\"metric_name\": \"api_response_time\", \"value\": 0.3089447021484375, \"unit\": \"seconds\", \"endpoint\": \"POST /api/v1/auth/login\", \"status_code\": 200, \"user_id\": null}}", "module": "structured_logger", "name": "app.utils.structured_logger", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:36:11.935421+08:00", "timestamp": 1753842971.935421}}}
{"text": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:04:47.016733", "seconds": 287.016733}, "exception": null, "extra": {}, "file": {"name": "cache.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\cache.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 55, "message": "Redis连接失败，将使用内存缓存: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "cache", "name": "app.utils.cache", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:36:15.952399+08:00", "timestamp": 1753842975.952399}}}
{"text": "慢请求: {'method': 'POST', 'path': '/api/v1/auth/login', 'status_code': 200, 'process_time': 8.341, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}\n", "record": {"elapsed": {"repr": "0:04:47.017707", "seconds": 287.017707}, "exception": null, "extra": {}, "file": {"name": "rate_limit.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\middleware\\rate_limit.py"}, "function": "_log_request", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 276, "message": "慢请求: {'method': 'POST', 'path': '/api/v1/auth/login', 'status_code': 200, 'process_time': 8.341, 'client_ip': '127.0.0.1', 'user_id': None, 'user_agent': 'python-httpx/0.28.1'}", "module": "rate_limit", "name": "app.middleware.rate_limit", "process": {"id": 38988, "name": "MainProcess"}, "thread": {"id": 66332, "name": "MainThread"}, "time": {"repr": "2025-07-30 10:36:15.953373+08:00", "timestamp": 1753842975.953373}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.150595", "seconds": 1.150595}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 59100, "name": "MainProcess"}, "thread": {"id": 54704, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:52:11.926223+08:00", "timestamp": 1753847531.926223}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.150595", "seconds": 1.150595}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 59100, "name": "MainProcess"}, "thread": {"id": 54704, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:52:11.926223+08:00", "timestamp": 1753847531.926223}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.151568", "seconds": 1.151568}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 59100, "name": "MainProcess"}, "thread": {"id": 54704, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:52:11.927196+08:00", "timestamp": 1753847531.927196}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.151568", "seconds": 1.151568}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 28, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 59100, "name": "MainProcess"}, "thread": {"id": 54704, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:52:11.927196+08:00", "timestamp": 1753847531.927196}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.160082", "seconds": 5.160082}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 59100, "name": "MainProcess"}, "thread": {"id": 54704, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:52:15.935710+08:00", "timestamp": 1753847535.93571}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.172794", "seconds": 5.172794}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 59100, "name": "MainProcess"}, "thread": {"id": 54704, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:52:15.948422+08:00", "timestamp": 1753847535.948422}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.172794", "seconds": 5.172794}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 30, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 59100, "name": "MainProcess"}, "thread": {"id": 54704, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:52:15.948422+08:00", "timestamp": 1753847535.948422}}}
{"text": "正在关闭数据库连接...\n", "record": {"elapsed": {"repr": "0:00:05.173797", "seconds": 5.173797}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 35, "message": "正在关闭数据库连接...", "module": "main", "name": "app.main", "process": {"id": 59100, "name": "MainProcess"}, "thread": {"id": 54704, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:52:15.949425+08:00", "timestamp": 1753847535.949425}}}
{"text": "数据库引擎已关闭\n", "record": {"elapsed": {"repr": "0:00:05.174806", "seconds": 5.174806}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "close_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 90, "message": "数据库引擎已关闭", "module": "connection", "name": "app.database.connection", "process": {"id": 59100, "name": "MainProcess"}, "thread": {"id": 54704, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:52:15.950434+08:00", "timestamp": 1753847535.950434}}}
{"text": "数据库连接已关闭\n", "record": {"elapsed": {"repr": "0:00:05.174806", "seconds": 5.174806}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 37, "message": "数据库连接已关闭", "module": "main", "name": "app.main", "process": {"id": 59100, "name": "MainProcess"}, "thread": {"id": 54704, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:52:15.950434+08:00", "timestamp": 1753847535.950434}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.165365", "seconds": 1.165365}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 32352, "name": "MainProcess"}, "thread": {"id": 57488, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:53:18.838159+08:00", "timestamp": 1753847598.838159}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.165365", "seconds": 1.165365}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 32352, "name": "MainProcess"}, "thread": {"id": 57488, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:53:18.838159+08:00", "timestamp": 1753847598.838159}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.166868", "seconds": 1.166868}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 32352, "name": "MainProcess"}, "thread": {"id": 57488, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:53:18.839662+08:00", "timestamp": 1753847598.839662}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.166868", "seconds": 1.166868}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 28, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 32352, "name": "MainProcess"}, "thread": {"id": 57488, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:53:18.839662+08:00", "timestamp": 1753847598.839662}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.212462", "seconds": 5.212462}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 32352, "name": "MainProcess"}, "thread": {"id": 57488, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:53:22.885256+08:00", "timestamp": 1753847602.885256}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.225812", "seconds": 5.225812}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 32352, "name": "MainProcess"}, "thread": {"id": 57488, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:53:22.898606+08:00", "timestamp": 1753847602.898606}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.225812", "seconds": 5.225812}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 30, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 32352, "name": "MainProcess"}, "thread": {"id": 57488, "name": "MainThread"}, "time": {"repr": "2025-07-30 11:53:22.898606+08:00", "timestamp": 1753847602.898606}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.190097", "seconds": 1.190097}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 63200, "name": "MainProcess"}, "thread": {"id": 63484, "name": "MainThread"}, "time": {"repr": "2025-07-30 12:01:03.026369+08:00", "timestamp": 1753848063.026369}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.190097", "seconds": 1.190097}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 63200, "name": "MainProcess"}, "thread": {"id": 63484, "name": "MainThread"}, "time": {"repr": "2025-07-30 12:01:03.026369+08:00", "timestamp": 1753848063.026369}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.191070", "seconds": 1.19107}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 63200, "name": "MainProcess"}, "thread": {"id": 63484, "name": "MainThread"}, "time": {"repr": "2025-07-30 12:01:03.027342+08:00", "timestamp": 1753848063.027342}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.191070", "seconds": 1.19107}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 63200, "name": "MainProcess"}, "thread": {"id": 63484, "name": "MainThread"}, "time": {"repr": "2025-07-30 12:01:03.027342+08:00", "timestamp": 1753848063.027342}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.203509", "seconds": 5.203509}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 63200, "name": "MainProcess"}, "thread": {"id": 63484, "name": "MainThread"}, "time": {"repr": "2025-07-30 12:01:07.039781+08:00", "timestamp": 1753848067.039781}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.220281", "seconds": 5.220281}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 63200, "name": "MainProcess"}, "thread": {"id": 63484, "name": "MainThread"}, "time": {"repr": "2025-07-30 12:01:07.056553+08:00", "timestamp": 1753848067.056553}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.220281", "seconds": 5.220281}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 63200, "name": "MainProcess"}, "thread": {"id": 63484, "name": "MainThread"}, "time": {"repr": "2025-07-30 12:01:07.056553+08:00", "timestamp": 1753848067.056553}}}
{"text": "已加载 zh-CN 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.179253", "seconds": 1.179253}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-CN 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 41756, "name": "MainProcess"}, "thread": {"id": 43800, "name": "MainThread"}, "time": {"repr": "2025-07-30 12:09:44.945713+08:00", "timestamp": 1753848584.945713}}}
{"text": "已加载 en-US 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.180260", "seconds": 1.18026}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 en-US 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 41756, "name": "MainProcess"}, "thread": {"id": 43800, "name": "MainThread"}, "time": {"repr": "2025-07-30 12:09:44.946720+08:00", "timestamp": 1753848584.94672}}}
{"text": "已加载 zh-TW 翻译文件\n", "record": {"elapsed": {"repr": "0:00:01.180260", "seconds": 1.18026}, "exception": null, "extra": {}, "file": {"name": "i18n.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\i18n.py"}, "function": "_load_translations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 45, "message": "已加载 zh-TW 翻译文件", "module": "i18n", "name": "app.utils.i18n", "process": {"id": 41756, "name": "MainProcess"}, "thread": {"id": 43800, "name": "MainThread"}, "time": {"repr": "2025-07-30 12:09:44.946720+08:00", "timestamp": 1753848584.94672}}}
{"text": "正在初始化数据库连接...\n", "record": {"elapsed": {"repr": "0:00:01.180260", "seconds": 1.18026}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 27, "message": "正在初始化数据库连接...", "module": "main", "name": "app.main", "process": {"id": 41756, "name": "MainProcess"}, "thread": {"id": 43800, "name": "MainThread"}, "time": {"repr": "2025-07-30 12:09:44.946720+08:00", "timestamp": 1753848584.94672}}}
{"text": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.\n", "record": {"elapsed": {"repr": "0:00:05.194815", "seconds": 5.194815}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 66, "message": "Redis连接失败，将在无缓存模式下运行: Error 22 connecting to localhost:6379. 远程计算机拒绝网络连接。.", "module": "connection", "name": "app.database.connection", "process": {"id": 41756, "name": "MainProcess"}, "thread": {"id": 43800, "name": "MainThread"}, "time": {"repr": "2025-07-30 12:09:48.961275+08:00", "timestamp": 1753848588.961275}}}
{"text": "数据库表创建成功\n", "record": {"elapsed": {"repr": "0:00:05.208651", "seconds": 5.208651}, "exception": null, "extra": {}, "file": {"name": "connection.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\database\\connection.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 73, "message": "数据库表创建成功", "module": "connection", "name": "app.database.connection", "process": {"id": 41756, "name": "MainProcess"}, "thread": {"id": 43800, "name": "MainThread"}, "time": {"repr": "2025-07-30 12:09:48.975111+08:00", "timestamp": 1753848588.975111}}}
{"text": "数据库连接初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.208651", "seconds": 5.208651}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 29, "message": "数据库连接初始化完成", "module": "main", "name": "app.main", "process": {"id": 41756, "name": "MainProcess"}, "thread": {"id": 43800, "name": "MainThread"}, "time": {"repr": "2025-07-30 12:09:48.975111+08:00", "timestamp": 1753848588.975111}}}
