import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../services/monitoring_service.dart';
import '../services/language_service.dart';
import '../l10n/app_localizations.dart';
import '../widgets/loading_widget.dart';
import '../widgets/error_widget.dart';

class MonitoringDashboardScreen extends StatefulWidget {
  const MonitoringDashboardScreen({Key? key}) : super(key: key);

  @override
  State<MonitoringDashboardScreen> createState() => _MonitoringDashboardScreenState();
}

class _MonitoringDashboardScreenState extends State<MonitoringDashboardScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final MonitoringService _monitoringService = MonitoringService();
  
  Map<String, dynamic>? _dashboardData;
  Map<String, dynamic>? _healthData;
  List<Map<String, dynamic>> _performanceAlerts = [];
  
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadMonitoringData();

    // 设置定时刷新
    _startAutoRefresh();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _startAutoRefresh() {
    // 每30秒自动刷新数据
    Future.delayed(const Duration(seconds: 30), () {
      if (mounted) {
        _loadMonitoringData();
        _startAutoRefresh();
      }
    });
  }

  Future<void> _loadMonitoringData() async {
    if (!mounted) return;
    
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // 并行加载所有监控数据
      final results = await Future.wait([
        _monitoringService.getDashboardData(),
        _monitoringService.getSystemHealth(),
        _monitoringService.getPerformanceAlerts(),
      ]);

      if (mounted) {
        setState(() {
          _dashboardData = results[0];
          _healthData = results[1];
          _performanceAlerts = List<Map<String, dynamic>>.from(results[2]['alerts'] ?? []);
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.monitoringDashboard),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              icon: const Icon(Icons.dashboard),
              text: l10n.overview,
            ),
            Tab(
              icon: const Icon(Icons.speed),
              text: l10n.performance,
            ),
            Tab(
              icon: const Icon(Icons.storage),
              text: l10n.resources,
            ),
            Tab(
              icon: const Icon(Icons.warning),
              text: l10n.alerts,
            ),
            Tab(
              icon: const Icon(Icons.article),
              text: l10n.logs,
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadMonitoringData,
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : _error != null
              ? ErrorWidget(
                  message: _error!,
                  onRetry: _loadMonitoringData,
                )
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildOverviewTab(context, l10n),
                    _buildPerformanceTab(context, l10n),
                    _buildResourcesTab(context, l10n),
                    _buildAlertsTab(context, l10n),
                    _buildLogsTab(context, l10n),
                  ],
                ),
    );
  }

  Widget _buildOverviewTab(BuildContext context, AppLocalizations l10n) {
    if (_dashboardData == null || _healthData == null) {
      return const Center(child: Text('No monitoring data available'));
    }

    final systemHealth = _dashboardData!['system_health'] as Map<String, dynamic>;
    final status = systemHealth['status'] as String;

    return RefreshIndicator(
      onRefresh: _loadMonitoringData,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSystemStatusCard(context, l10n, systemHealth),
          const SizedBox(height: 16),
          _buildQuickStatsGrid(context, l10n),
          const SizedBox(height: 16),
          _buildRecentAlertsCard(context, l10n),
        ],
      ),
    );
  }

  Widget _buildSystemStatusCard(BuildContext context, AppLocalizations l10n, Map<String, dynamic> health) {
    final status = health['status'] as String;
    final uptime = health['uptime'] as double;
    
    Color statusColor;
    IconData statusIcon;
    
    switch (status) {
      case 'healthy':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case 'warning':
        statusColor = Colors.orange;
        statusIcon = Icons.warning;
        break;
      case 'critical':
        statusColor = Colors.red;
        statusIcon = Icons.error;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
    }
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(statusIcon, color: statusColor, size: 32),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        l10n.systemStatus,
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      Text(
                        status.toUpperCase(),
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              '${l10n.uptime}: ${_formatUptime(uptime)}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(
              '${l10n.lastUpdate}: ${DateTime.now().toString().substring(0, 19)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStatsGrid(BuildContext context, AppLocalizations l10n) {
    final systemHealth = _dashboardData!['system_health'] as Map<String, dynamic>;
    final cacheStats = _dashboardData!['cache_stats'] as Map<String, dynamic>;
    final dbStats = _dashboardData!['database_stats'] as Map<String, dynamic>;
    
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildStatCard(
          context,
          l10n.cpuUsage,
          '${systemHealth['cpu_percent']?.toStringAsFixed(1) ?? '0'}%',
          Icons.memory,
          _getUsageColor(systemHealth['cpu_percent']?.toDouble() ?? 0),
        ),
        _buildStatCard(
          context,
          l10n.memoryUsage,
          '${systemHealth['memory_percent']?.toStringAsFixed(1) ?? '0'}%',
          Icons.storage,
          _getUsageColor(systemHealth['memory_percent']?.toDouble() ?? 0),
        ),
        _buildStatCard(
          context,
          l10n.responseTime,
          '${systemHealth['response_time_avg']?.toStringAsFixed(2) ?? '0'}s',
          Icons.speed,
          _getResponseTimeColor(systemHealth['response_time_avg']?.toDouble() ?? 0),
        ),
        _buildStatCard(
          context,
          l10n.cacheHitRate,
          '${cacheStats['hit_rate']?.toStringAsFixed(1) ?? '0'}%',
          Icons.cached,
          _getCacheHitColor(cacheStats['hit_rate']?.toDouble() ?? 0),
        ),
      ],
    );
  }

  Widget _buildStatCard(BuildContext context, String title, String value, 
                       IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentAlertsCard(BuildContext context, AppLocalizations l10n) {
    final recentAlerts = _performanceAlerts.take(3).toList();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  l10n.recentAlerts,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                TextButton(
                  onPressed: () => _tabController.animateTo(3),
                  child: Text(l10n.viewAll),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (recentAlerts.isEmpty)
              Center(
                child: Text(
                  l10n.noRecentAlerts,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey,
                      ),
                ),
              )
            else
              ...recentAlerts.map((alert) => _buildAlertItem(context, alert, compact: true)),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceTab(BuildContext context, AppLocalizations l10n) {
    if (_dashboardData == null) {
      return const Center(child: Text('No performance data available'));
    }

    final trends = _dashboardData!['performance_trends'] as Map<String, dynamic>? ?? {};
    
    return RefreshIndicator(
      onRefresh: _loadMonitoringData,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildPerformanceChart(context, l10n, trends),
          const SizedBox(height: 16),
          _buildEndpointStatsCard(context, l10n),
        ],
      ),
    );
  }

  Widget _buildPerformanceChart(BuildContext context, AppLocalizations l10n, Map<String, dynamic> trends) {
    final responseTimeTrend = trends['response_time'] as List<dynamic>? ?? [];
    
    if (responseTimeTrend.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Text(
                l10n.responseTimeTrend,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 16),
              const Text('No trend data available'),
            ],
          ),
        ),
      );
    }

    final spots = responseTimeTrend.asMap().entries.map((entry) {
      final index = entry.key.toDouble();
      final data = entry.value as Map<String, dynamic>;
      final value = (data['value'] as num).toDouble();
      return FlSpot(index, value);
    }).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.responseTimeTrend,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            '${value.toStringAsFixed(2)}s',
                            style: const TextStyle(fontSize: 10),
                          );
                        },
                      ),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        getTitlesWidget: (value, meta) {
                          final index = value.toInt();
                          if (index >= 0 && index < responseTimeTrend.length) {
                            final data = responseTimeTrend[index] as Map<String, dynamic>;
                            final timestamp = data['timestamp'] as String;
                            final time = DateTime.parse(timestamp);
                            return Text(
                              '${time.hour}:${time.minute.toString().padLeft(2, '0')}',
                              style: const TextStyle(fontSize: 10),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: spots,
                      isCurved: true,
                      color: Colors.blue,
                      barWidth: 2,
                      dotData: FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: Colors.blue.withOpacity(0.1),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEndpointStatsCard(BuildContext context, AppLocalizations l10n) {
    final endpointStats = _dashboardData!['endpoint_stats'] as Map<String, dynamic>? ?? {};
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.endpointPerformance,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            if (endpointStats.isEmpty)
              const Text('No endpoint data available')
            else
              ...endpointStats.entries.take(5).map((entry) {
                final endpoint = entry.key;
                final stats = entry.value as Map<String, dynamic>;
                return ListTile(
                  title: Text(endpoint),
                  subtitle: Text(
                    'Requests: ${stats['request_count']} | '
                    'Avg: ${stats['avg_response_time']?.toStringAsFixed(3)}s | '
                    'Errors: ${stats['error_count']}',
                  ),
                  trailing: Chip(
                    label: Text('${stats['error_rate']?.toStringAsFixed(1)}%'),
                    backgroundColor: _getErrorRateColor(stats['error_rate']?.toDouble() ?? 0),
                  ),
                );
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildResourcesTab(BuildContext context, AppLocalizations l10n) {
    if (_dashboardData == null) {
      return const Center(child: Text('No resource data available'));
    }

    final systemHealth = _dashboardData!['system_health'] as Map<String, dynamic>;
    final dbStats = _dashboardData!['database_stats'] as Map<String, dynamic>;
    final cacheStats = _dashboardData!['cache_stats'] as Map<String, dynamic>;

    return RefreshIndicator(
      onRefresh: _loadMonitoringData,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildResourceUsageCard(context, l10n, systemHealth),
          const SizedBox(height: 16),
          _buildDatabaseStatsCard(context, l10n, dbStats),
          const SizedBox(height: 16),
          _buildCacheStatsCard(context, l10n, cacheStats),
        ],
      ),
    );
  }

  Widget _buildResourceUsageCard(BuildContext context, AppLocalizations l10n, Map<String, dynamic> health) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.resourceUsage,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            _buildProgressIndicator(
              context,
              l10n.cpuUsage,
              health['cpu_percent']?.toDouble() ?? 0,
              100,
              '%',
              _getUsageColor(health['cpu_percent']?.toDouble() ?? 0),
            ),
            const SizedBox(height: 12),
            _buildProgressIndicator(
              context,
              l10n.memoryUsage,
              health['memory_percent']?.toDouble() ?? 0,
              100,
              '%',
              _getUsageColor(health['memory_percent']?.toDouble() ?? 0),
            ),
            const SizedBox(height: 12),
            _buildProgressIndicator(
              context,
              l10n.diskUsage,
              health['disk_usage']?.toDouble() ?? 0,
              100,
              '%',
              _getUsageColor(health['disk_usage']?.toDouble() ?? 0),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator(BuildContext context, String label, double value, 
                                double max, String unit, Color color) {
    final percentage = (value / max).clamp(0.0, 1.0);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(label),
            Text('${value.toStringAsFixed(1)}$unit'),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: percentage,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      ],
    );
  }

  Widget _buildDatabaseStatsCard(BuildContext context, AppLocalizations l10n, Map<String, dynamic> stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.databaseStats,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  context,
                  l10n.totalUsers,
                  stats['total_users']?.toString() ?? '0',
                  Icons.people,
                ),
                _buildStatItem(
                  context,
                  l10n.totalTransactions,
                  stats['total_transactions']?.toString() ?? '0',
                  Icons.receipt,
                ),
                _buildStatItem(
                  context,
                  l10n.todayTransactions,
                  stats['today_transactions']?.toString() ?? '0',
                  Icons.today,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCacheStatsCard(BuildContext context, AppLocalizations l10n, Map<String, dynamic> stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.cacheStats,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  context,
                  l10n.hits,
                  stats['hits']?.toString() ?? '0',
                  Icons.check_circle,
                  color: Colors.green,
                ),
                _buildStatItem(
                  context,
                  l10n.misses,
                  stats['misses']?.toString() ?? '0',
                  Icons.cancel,
                  color: Colors.red,
                ),
                _buildStatItem(
                  context,
                  l10n.hitRate,
                  '${stats['hit_rate']?.toStringAsFixed(1) ?? '0'}%',
                  Icons.trending_up,
                  color: _getCacheHitColor(stats['hit_rate']?.toDouble() ?? 0),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value, 
                       IconData icon, {Color? color}) {
    return Column(
      children: [
        Icon(icon, color: color ?? Colors.blue, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildAlertsTab(BuildContext context, AppLocalizations l10n) {
    return RefreshIndicator(
      onRefresh: _loadMonitoringData,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          if (_performanceAlerts.isEmpty)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.check_circle,
                    size: 64,
                    color: Colors.green[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    l10n.noPerformanceAlerts,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.grey,
                        ),
                  ),
                ],
              ),
            )
          else
            ..._performanceAlerts.map((alert) => _buildAlertItem(context, alert)),
        ],
      ),
    );
  }

  Widget _buildAlertItem(BuildContext context, Map<String, dynamic> alert, {bool compact = false}) {
    final level = alert['level'] ?? 'info';
    final message = alert['message'] ?? '';
    final type = alert['type'] ?? 'unknown';
    final value = alert['value'];
    final threshold = alert['threshold'];
    
    Color levelColor;
    IconData levelIcon;
    
    switch (level) {
      case 'critical':
        levelColor = Colors.red;
        levelIcon = Icons.dangerous;
        break;
      case 'warning':
        levelColor = Colors.orange;
        levelIcon = Icons.warning;
        break;
      default:
        levelColor = Colors.blue;
        levelIcon = Icons.info;
    }
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(levelIcon, color: levelColor),
        title: Text(message),
        subtitle: compact ? null : Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Type: ${type.toUpperCase()}'),
            if (value != null && threshold != null)
              Text('Value: $value | Threshold: $threshold'),
          ],
        ),
        trailing: Chip(
          label: Text(
            level.toUpperCase(),
            style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: levelColor.withOpacity(0.2),
          side: BorderSide(color: levelColor),
        ),
      ),
    );
  }

  Color _getUsageColor(double percentage) {
    if (percentage > 90) return Colors.red;
    if (percentage > 80) return Colors.orange;
    if (percentage > 60) return Colors.yellow[700]!;
    return Colors.green;
  }

  Color _getResponseTimeColor(double seconds) {
    if (seconds > 2.0) return Colors.red;
    if (seconds > 1.0) return Colors.orange;
    if (seconds > 0.5) return Colors.yellow[700]!;
    return Colors.green;
  }

  Color _getCacheHitColor(double percentage) {
    if (percentage > 80) return Colors.green;
    if (percentage > 60) return Colors.yellow[700]!;
    if (percentage > 40) return Colors.orange;
    return Colors.red;
  }

  Color _getErrorRateColor(double percentage) {
    if (percentage > 10) return Colors.red;
    if (percentage > 5) return Colors.orange;
    if (percentage > 1) return Colors.yellow[700]!;
    return Colors.green;
  }

  Widget _buildLogsTab(BuildContext context, AppLocalizations l10n) {
    return RefreshIndicator(
      onRefresh: _loadMonitoringData,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildLogFiltersCard(context, l10n),
          const SizedBox(height: 16),
          _buildLogStatisticsCard(context, l10n),
          const SizedBox(height: 16),
          _buildRecentLogsCard(context, l10n),
        ],
      ),
    );
  }

  Widget _buildLogFiltersCard(BuildContext context, AppLocalizations l10n) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Log Filters',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Log Level',
                      border: OutlineInputBorder(),
                    ),
                    items: ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
                        .map((level) => DropdownMenuItem(
                              value: level,
                              child: Text(level),
                            ))
                        .toList(),
                    onChanged: (value) {
                      // 实现日志级别过滤
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Log Category',
                      border: OutlineInputBorder(),
                    ),
                    items: ['system', 'api', 'database', 'cache', 'ai', 'security', 'performance', 'user', 'business']
                        .map((category) => DropdownMenuItem(
                              value: category,
                              child: Text(category.toUpperCase()),
                            ))
                        .toList(),
                    onChanged: (value) {
                      // 实现日志分类过滤
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    // 实现日志搜索
                  },
                  icon: const Icon(Icons.search),
                  label: const Text('Search Logs'),
                ),
                const SizedBox(width: 12),
                OutlinedButton.icon(
                  onPressed: () {
                    // 实现日志导出
                  },
                  icon: const Icon(Icons.download),
                  label: const Text('Export Logs'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogStatisticsCard(BuildContext context, AppLocalizations l10n) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Log Statistics',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  context,
                  'Total Logs',
                  '1,234',
                  Icons.article,
                ),
                _buildStatItem(
                  context,
                  'Errors',
                  '12',
                  Icons.error,
                  color: Colors.red,
                ),
                _buildStatItem(
                  context,
                  'Warnings',
                  '45',
                  Icons.warning,
                  color: Colors.orange,
                ),
                _buildStatItem(
                  context,
                  'Info',
                  '1,177',
                  Icons.info,
                  color: Colors.blue,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentLogsCard(BuildContext context, AppLocalizations l10n) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Logs',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                TextButton(
                  onPressed: () {
                    // 跳转到详细日志页面
                  },
                  child: Text(l10n.viewAll),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // 这里应该显示实际的日志数据
            ...List.generate(5, (index) => _buildLogItem(context, {
              'level': ['INFO', 'WARNING', 'ERROR', 'DEBUG', 'INFO'][index],
              'category': ['api', 'system', 'database', 'cache', 'security'][index],
              'message': 'Sample log message ${index + 1}',
              'timestamp': DateTime.now().subtract(Duration(minutes: index * 5)).toIso8601String(),
            })),
          ],
        ),
      ),
    );
  }

  Widget _buildLogItem(BuildContext context, Map<String, dynamic> log) {
    final level = log['level'] ?? 'INFO';
    final category = log['category'] ?? 'system';
    final message = log['message'] ?? '';
    final timestamp = log['timestamp'] ?? '';

    Color levelColor;
    IconData levelIcon;

    switch (level) {
      case 'ERROR':
      case 'CRITICAL':
        levelColor = Colors.red;
        levelIcon = Icons.error;
        break;
      case 'WARNING':
        levelColor = Colors.orange;
        levelIcon = Icons.warning;
        break;
      case 'DEBUG':
        levelColor = Colors.grey;
        levelIcon = Icons.bug_report;
        break;
      default:
        levelColor = Colors.blue;
        levelIcon = Icons.info;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(levelIcon, color: levelColor, size: 20),
        title: Text(
          message,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Row(
          children: [
            Chip(
              label: Text(
                level,
                style: const TextStyle(fontSize: 10),
              ),
              backgroundColor: levelColor.withOpacity(0.2),
              side: BorderSide(color: levelColor),
            ),
            const SizedBox(width: 8),
            Chip(
              label: Text(
                category.toUpperCase(),
                style: const TextStyle(fontSize: 10),
              ),
              backgroundColor: Colors.grey.withOpacity(0.2),
            ),
            const Spacer(),
            Text(
              DateTime.parse(timestamp).toString().substring(11, 19),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey,
                  ),
            ),
          ],
        ),
        onTap: () {
          // 显示日志详情
          _showLogDetails(context, log);
        },
      ),
    );
  }

  void _showLogDetails(BuildContext context, Map<String, dynamic> log) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Log Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildLogDetailItem('Level', log['level'] ?? ''),
              _buildLogDetailItem('Category', log['category'] ?? ''),
              _buildLogDetailItem('Message', log['message'] ?? ''),
              _buildLogDetailItem('Timestamp', log['timestamp'] ?? ''),
              if (log['user_id'] != null)
                _buildLogDetailItem('User ID', log['user_id']),
              if (log['endpoint'] != null)
                _buildLogDetailItem('Endpoint', log['endpoint']),
              if (log['error_message'] != null)
                _buildLogDetailItem('Error', log['error_message']),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildLogDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _formatUptime(double seconds) {
    final duration = Duration(seconds: seconds.toInt());
    final days = duration.inDays;
    final hours = duration.inHours % 24;
    final minutes = duration.inMinutes % 60;

    if (days > 0) {
      return '${days}d ${hours}h ${minutes}m';
    } else if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
}
