"""
API密钥加密工具
提供安全的API密钥加密和解密功能
"""

import os
import base64
from typing import Optional
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from loguru import logger


class APIKeyEncryption:
    """API密钥加密管理器"""
    
    def __init__(self, master_key: Optional[str] = None):
        """
        初始化加密管理器
        
        Args:
            master_key: 主密钥，如果不提供则从环境变量获取
        """
        self.master_key = master_key or os.getenv("ENCRYPTION_MASTER_KEY")
        if not self.master_key:
            # 如果没有主密钥，生成一个新的（仅用于开发环境）
            self.master_key = self._generate_master_key()
            logger.warning("未找到ENCRYPTION_MASTER_KEY环境变量，已生成临时密钥")
        
        self.cipher = self._create_cipher()
    
    def _generate_master_key(self) -> str:
        """生成新的主密钥"""
        key = Fernet.generate_key()
        return base64.urlsafe_b64encode(key).decode()
    
    def _create_cipher(self) -> Fernet:
        """创建加密器"""
        try:
            # 使用PBKDF2从主密钥派生加密密钥
            salt = b'ai_accounting_salt'  # 在生产环境中应该使用随机盐
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(self.master_key.encode()))
            return Fernet(key)
        except Exception as e:
            logger.error(f"创建加密器失败: {e}")
            raise
    
    def encrypt_api_key(self, api_key: str) -> str:
        """
        加密API密钥
        
        Args:
            api_key: 原始API密钥
            
        Returns:
            加密后的API密钥（Base64编码）
        """
        try:
            if not api_key:
                return ""
            
            encrypted_bytes = self.cipher.encrypt(api_key.encode('utf-8'))
            encrypted_key = base64.urlsafe_b64encode(encrypted_bytes).decode('utf-8')
            
            logger.debug(f"API密钥加密成功，长度: {len(encrypted_key)}")
            return encrypted_key
            
        except Exception as e:
            logger.error(f"API密钥加密失败: {e}")
            raise
    
    def decrypt_api_key(self, encrypted_api_key: str) -> str:
        """
        解密API密钥
        
        Args:
            encrypted_api_key: 加密的API密钥
            
        Returns:
            原始API密钥
        """
        try:
            if not encrypted_api_key:
                return ""
            
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_api_key.encode('utf-8'))
            decrypted_bytes = self.cipher.decrypt(encrypted_bytes)
            api_key = decrypted_bytes.decode('utf-8')
            
            logger.debug("API密钥解密成功")
            return api_key
            
        except Exception as e:
            logger.error(f"API密钥解密失败: {e}")
            raise
    
    def mask_api_key(self, api_key: str, show_chars: int = 4) -> str:
        """
        脱敏显示API密钥
        
        Args:
            api_key: 原始API密钥
            show_chars: 显示的字符数
            
        Returns:
            脱敏后的API密钥
        """
        if not api_key or len(api_key) <= show_chars:
            return "****"
        
        return api_key[:show_chars] + "*" * (len(api_key) - show_chars)
    
    def validate_api_key_format(self, api_key: str, provider: str) -> bool:
        """
        验证API密钥格式
        
        Args:
            api_key: API密钥
            provider: AI提供商
            
        Returns:
            是否格式正确
        """
        if not api_key:
            return False
        
        # 不同提供商的密钥格式验证
        format_rules = {
            "openai": lambda k: k.startswith("sk-") and len(k) > 20,
            "anthropic": lambda k: k.startswith("sk-ant-") and len(k) > 30,
            "azure_openai": lambda k: len(k) == 32,  # Azure密钥通常是32位
            "google_gemini": lambda k: k.startswith("AIza") and len(k) == 39,
            "baidu_qianfan": lambda k: len(k) >= 16,
            "custom": lambda k: len(k) >= 8,  # 自定义提供商最小长度要求
        }
        
        validator = format_rules.get(provider, lambda k: len(k) >= 8)
        return validator(api_key)
    
    def get_key_strength(self, api_key: str) -> str:
        """
        评估API密钥强度
        
        Args:
            api_key: API密钥
            
        Returns:
            密钥强度等级 (weak/medium/strong)
        """
        if not api_key:
            return "weak"
        
        length = len(api_key)
        has_upper = any(c.isupper() for c in api_key)
        has_lower = any(c.islower() for c in api_key)
        has_digit = any(c.isdigit() for c in api_key)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in api_key)
        
        score = 0
        if length >= 20:
            score += 2
        elif length >= 12:
            score += 1
        
        if has_upper:
            score += 1
        if has_lower:
            score += 1
        if has_digit:
            score += 1
        if has_special:
            score += 1
        
        if score >= 5:
            return "strong"
        elif score >= 3:
            return "medium"
        else:
            return "weak"


# 全局加密管理器实例
_encryption_manager: Optional[APIKeyEncryption] = None


def get_encryption_manager() -> APIKeyEncryption:
    """获取全局加密管理器实例"""
    global _encryption_manager
    if _encryption_manager is None:
        _encryption_manager = APIKeyEncryption()
    return _encryption_manager


def encrypt_api_key(api_key: str) -> str:
    """便捷函数：加密API密钥"""
    return get_encryption_manager().encrypt_api_key(api_key)


def decrypt_api_key(encrypted_api_key: str) -> str:
    """便捷函数：解密API密钥"""
    return get_encryption_manager().decrypt_api_key(encrypted_api_key)


def mask_api_key(api_key: str, show_chars: int = 4) -> str:
    """便捷函数：脱敏显示API密钥"""
    return get_encryption_manager().mask_api_key(api_key, show_chars)


def validate_api_key_format(api_key: str, provider: str) -> bool:
    """便捷函数：验证API密钥格式"""
    return get_encryption_manager().validate_api_key_format(api_key, provider)


# 测试函数
def test_encryption():
    """测试加密功能"""
    try:
        print("🔐 开始测试API密钥加密功能...")
        manager = APIKeyEncryption()

        # 测试不同类型的API密钥
        test_keys = {
            "openai": "sk-1234567890abcdef1234567890abcdef",
            "anthropic": "sk-ant-1234567890abcdef1234567890abcdef",
            "azure": "1234567890abcdef1234567890abcdef",
        }

        for provider, key in test_keys.items():
            print(f"\n📝 测试 {provider} 密钥:")
            print(f"   原始密钥: {key}")

            # 加密
            encrypted = manager.encrypt_api_key(key)
            print(f"   加密后: {encrypted[:20]}...")

            # 解密
            decrypted = manager.decrypt_api_key(encrypted)
            print(f"   解密后: {decrypted}")

            # 脱敏显示
            masked = manager.mask_api_key(key)
            print(f"   脱敏显示: {masked}")

            # 验证格式
            is_valid = manager.validate_api_key_format(key, provider.split("_")[0])
            print(f"   格式验证: {'✅' if is_valid else '❌'}")

            # 强度评估
            strength = manager.get_key_strength(key)
            print(f"   密钥强度: {strength}")

            # 验证加解密一致性
            assert decrypted == key, f"{provider} 密钥加解密不一致!"
            print(f"   加解密一致性: ✅")

        print("\n🎉 所有加密测试通过!")
        return True

    except Exception as e:
        print(f"\n❌ 加密测试失败: {e}")
        return False


if __name__ == "__main__":
    test_encryption()
