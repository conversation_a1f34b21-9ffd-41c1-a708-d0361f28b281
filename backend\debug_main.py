#!/usr/bin/env python3
"""
调试版本的主应用
"""

from fastapi import FastAPI
import uvicorn
from loguru import logger
from contextlib import asynccontextmanager

# 尝试导入数据库连接
try:
    from app.database.connection import init_db, close_db
    print("✅ 数据库模块导入成功")
except Exception as e:
    print(f"❌ 数据库模块导入失败: {e}")
    init_db = None
    close_db = None

# 尝试导入中间件
try:
    from app.middleware.rate_limit import RateLimitMiddleware
    print("✅ 限流中间件导入成功")
except Exception as e:
    print(f"❌ 限流中间件导入失败: {e}")
    RateLimitMiddleware = None

try:
    from app.middleware.i18n import I18nMiddleware
    print("✅ 国际化中间件导入成功")
except Exception as e:
    print(f"❌ 国际化中间件导入失败: {e}")
    I18nMiddleware = None

# 尝试导入路由
try:
    from app.api import auth
    print("✅ 认证路由导入成功")
except Exception as e:
    print(f"❌ 认证路由导入失败: {e}")
    auth = None

try:
    from app.api import batch
    print("✅ 批处理路由导入成功")
except Exception as e:
    print(f"❌ 批处理路由导入失败: {e}")
    batch = None

# 尝试导入性能中间件
try:
    from app.middleware.performance import (
        PerformanceMiddleware, SystemMetricsMiddleware, DatabaseMetricsMiddleware,
        CacheMetricsMiddleware, AIMetricsMiddleware, ErrorTrackingMiddleware, HealthCheckMiddleware
    )
    print("✅ 性能中间件导入成功")
except Exception as e:
    print(f"❌ 性能中间件导入失败: {e}")
    PerformanceMiddleware = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    print("🚀 应用启动中...")
    if init_db:
        try:
            await init_db()
            print("✅ 数据库初始化成功")
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
    yield
    print("🛑 应用关闭中...")
    if close_db:
        try:
            await close_db()
            print("✅ 数据库关闭成功")
        except Exception as e:
            print(f"❌ 数据库关闭失败: {e}")

# 创建应用
app = FastAPI(title="记账应用调试版", version="1.0.0", lifespan=lifespan)

# 添加中间件
if I18nMiddleware:
    try:
        app.add_middleware(I18nMiddleware)
        print("✅ 国际化中间件添加成功")
    except Exception as e:
        print(f"❌ 国际化中间件添加失败: {e}")

if RateLimitMiddleware:
    try:
        app.add_middleware(RateLimitMiddleware)
        print("✅ 限流中间件添加成功")
    except Exception as e:
        print(f"❌ 限流中间件添加失败: {e}")

# 添加路由
if auth:
    try:
        app.include_router(auth.router, prefix="/api/v1/auth", tags=["认证"])
        print("✅ 认证路由添加成功")
    except Exception as e:
        print(f"❌ 认证路由添加失败: {e}")

if batch:
    try:
        app.include_router(batch.router, prefix="/api/v1/batch", tags=["批处理"])
        print("✅ 批处理路由添加成功")
    except Exception as e:
        print(f"❌ 批处理路由添加失败: {e}")

# 添加性能中间件
if PerformanceMiddleware:
    try:
        app.add_middleware(PerformanceMiddleware)
        app.add_middleware(SystemMetricsMiddleware)
        app.add_middleware(DatabaseMetricsMiddleware)
        app.add_middleware(CacheMetricsMiddleware)
        app.add_middleware(AIMetricsMiddleware)
        app.add_middleware(ErrorTrackingMiddleware)
        app.add_middleware(HealthCheckMiddleware)
        print("✅ 性能中间件添加成功")
    except Exception as e:
        print(f"❌ 性能中间件添加失败: {e}")

@app.get("/")
async def root():
    return {"message": "Hello from debug app"}

@app.get("/health")
async def health():
    return {"status": "ok", "message": "服务器运行正常"}

# 添加一个简单的异常处理器来调试
@app.exception_handler(Exception)
async def debug_exception_handler(request, exc):
    import traceback
    error_traceback = traceback.format_exc()
    logger.error(f"异常: {str(exc)}")
    logger.error(f"堆栈: {error_traceback}")
    print(f"ERROR: {str(exc)}")
    print(f"TRACEBACK: {error_traceback}")
    
    from fastapi.responses import JSONResponse
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": f"调试错误: {str(exc)}",
            "traceback": error_traceback
        }
    )

if __name__ == "__main__":
    print("启动调试服务器...")
    uvicorn.run(app, host="0.0.0.0", port=8005, log_level="debug")
