"""
限流中间件
为FastAPI应用提供自动限流功能
"""

import time
from typing import Callable, Optional, Dict, Any
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger
import ipaddress

from ..utils.rate_limiter import get_rate_limiter, RateLimit, RateLimitResult
from ..auth.dependencies import get_user_from_token


class RateLimitMiddleware(BaseHTTPMiddleware):
    """限流中间件"""
    
    def __init__(
        self,
        app,
        enable_ip_limiting: bool = True,
        enable_user_limiting: bool = True,
        enable_global_limiting: bool = True,
        custom_limits: Optional[Dict[str, RateLimit]] = None,
        excluded_paths: Optional[list[str]] = None
    ):
        super().__init__(app)
        self.enable_ip_limiting = enable_ip_limiting
        self.enable_user_limiting = enable_user_limiting
        self.enable_global_limiting = enable_global_limiting
        self.custom_limits = custom_limits or {}
        self.excluded_paths = excluded_paths or [
            "/docs", "/redoc", "/openapi.json", "/health", "/metrics"
        ]
        
        # 路径特定的限流配置
        self.path_limits = {
            "/api/v1/ai/categorize": ["user_ai_requests", "ip_requests"],
            "/api/v1/auth/login": ["user_login", "ip_requests"],
            "/api/v1/feedback/submit": ["feedback_submit", "user_api_calls"],
            "/api/v1/ai-config": ["config_update", "user_api_calls"],
            "/api/v1/feedback/batch-feedback": ["batch_operations", "user_api_calls"],
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求的限流检查"""
        
        # 检查是否为排除路径
        if self._is_excluded_path(request.url.path):
            return await call_next(request)
        
        # 获取客户端标识
        client_ip = self._get_client_ip(request)
        user_id = await self._get_user_id(request)
        
        # 执行限流检查
        rate_limit_result = await self._check_rate_limits(
            request, client_ip, user_id
        )
        
        if not rate_limit_result.allowed:
            return self._create_rate_limit_response(rate_limit_result)
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 处理请求
        response = await call_next(request)
        
        # 记录请求处理时间
        process_time = time.time() - start_time
        
        # 添加限流头信息
        self._add_rate_limit_headers(response, rate_limit_result)
        
        # 记录请求日志
        await self._log_request(request, response, process_time, client_ip, user_id)
        
        return response
    
    def _is_excluded_path(self, path: str) -> bool:
        """检查是否为排除路径"""
        for excluded in self.excluded_paths:
            if path.startswith(excluded):
                return True
        return False
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # 取第一个IP（原始客户端IP）
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 使用连接IP
        if hasattr(request.client, "host"):
            return request.client.host
        
        return "unknown"
    
    async def _get_user_id(self, request: Request) -> Optional[str]:
        """从请求中获取用户ID"""
        try:
            # 从Authorization头获取token
            auth_header = request.headers.get("Authorization")
            if not auth_header or not auth_header.startswith("Bearer "):
                return None
            
            token = auth_header.split(" ")[1]
            user = await get_user_from_token(token)
            return str(user.id) if user else None
            
        except Exception:
            return None
    
    async def _check_rate_limits(
        self,
        request: Request,
        client_ip: str,
        user_id: Optional[str]
    ) -> RateLimitResult:
        """执行限流检查"""
        
        limiter = await get_rate_limiter()
        path = request.url.path
        method = request.method
        
        # 获取适用的限流类型
        limit_types = self._get_applicable_limits(path, method, user_id)
        
        # 检查所有适用的限流规则
        for limit_type in limit_types:
            identifier = self._get_identifier(limit_type, client_ip, user_id)
            if not identifier:
                continue
            
            custom_limit = self.custom_limits.get(limit_type)
            result = await limiter.check_rate_limit(identifier, limit_type, custom_limit)
            
            if not result.allowed:
                logger.warning(
                    f"限流触发: {limit_type}, 标识符: {identifier}, "
                    f"路径: {path}, 剩余: {result.remaining}"
                )
                return result
        
        # 如果所有检查都通过，返回最严格的限制信息
        if limit_types:
            identifier = self._get_identifier(limit_types[0], client_ip, user_id)
            return await limiter.check_rate_limit(identifier, limit_types[0])
        
        # 默认允许
        from datetime import datetime, timedelta
        return RateLimitResult(
            allowed=True,
            remaining=999,
            reset_time=datetime.now() + timedelta(hours=1)
        )
    
    def _get_applicable_limits(
        self,
        path: str,
        method: str,
        user_id: Optional[str]
    ) -> list[str]:
        """获取适用的限流类型"""
        
        limits = []
        
        # 路径特定限制
        for pattern, path_limits in self.path_limits.items():
            if path.startswith(pattern):
                limits.extend(path_limits)
                break
        
        # 通用限制
        if self.enable_ip_limiting:
            limits.append("ip_requests")
            if method == "POST":
                limits.append("ip_burst")
        
        if self.enable_user_limiting and user_id:
            limits.append("user_api_calls")
        
        if self.enable_global_limiting:
            limits.append("global_ai_requests")
        
        return list(set(limits))  # 去重
    
    def _get_identifier(
        self,
        limit_type: str,
        client_ip: str,
        user_id: Optional[str]
    ) -> Optional[str]:
        """根据限流类型获取标识符"""
        
        if limit_type.startswith("user_") or limit_type in ["feedback_submit", "config_update", "batch_operations"]:
            return user_id
        elif limit_type.startswith("ip_"):
            return client_ip
        elif limit_type.startswith("global_"):
            return "global"
        else:
            # 默认使用用户ID，如果没有则使用IP
            return user_id or client_ip
    
    def _create_rate_limit_response(self, result: RateLimitResult) -> JSONResponse:
        """创建限流响应"""
        
        headers = {
            "X-RateLimit-Limit": "0",
            "X-RateLimit-Remaining": str(result.remaining),
            "X-RateLimit-Reset": str(int(result.reset_time.timestamp())),
        }
        
        if result.retry_after:
            headers["Retry-After"] = str(result.retry_after)
        
        content = {
            "error": True,
            "message": "请求过于频繁，请稍后再试",
            "details": {
                "limit_type": result.limit_type,
                "remaining": result.remaining,
                "reset_time": result.reset_time.isoformat(),
                "retry_after": result.retry_after
            }
        }
        
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content=content,
            headers=headers
        )
    
    def _add_rate_limit_headers(self, response: Response, result: RateLimitResult):
        """添加限流头信息"""
        
        response.headers["X-RateLimit-Remaining"] = str(result.remaining)
        response.headers["X-RateLimit-Reset"] = str(int(result.reset_time.timestamp()))
        
        if result.limit_type:
            response.headers["X-RateLimit-Type"] = result.limit_type
    
    async def _log_request(
        self,
        request: Request,
        response: Response,
        process_time: float,
        client_ip: str,
        user_id: Optional[str]
    ):
        """记录请求日志"""
        
        log_data = {
            "method": request.method,
            "path": request.url.path,
            "status_code": response.status_code,
            "process_time": round(process_time, 3),
            "client_ip": client_ip,
            "user_id": user_id,
            "user_agent": request.headers.get("User-Agent", ""),
        }
        
        # 记录慢请求
        if process_time > 2.0:
            logger.warning(f"慢请求: {log_data}")
        elif response.status_code >= 400:
            logger.warning(f"错误请求: {log_data}")
        else:
            logger.info(f"请求: {request.method} {request.url.path} - {response.status_code} - {process_time:.3f}s")


def create_rate_limit_decorator(limit_type: str, custom_limit: Optional[RateLimit] = None):
    """创建限流装饰器"""
    
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取request对象
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                # 如果没有找到request对象，直接执行函数
                return await func(*args, **kwargs)
            
            # 获取限流器
            limiter = await get_rate_limiter()
            
            # 获取标识符
            client_ip = request.client.host if hasattr(request.client, "host") else "unknown"
            user_id = await get_user_from_token(
                request.headers.get("Authorization", "").replace("Bearer ", "")
            )
            
            identifier = str(user_id.id) if user_id else client_ip
            
            # 检查限流
            result = await limiter.check_rate_limit(identifier, limit_type, custom_limit)
            
            if not result.allowed:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail={
                        "message": "请求过于频繁，请稍后再试",
                        "retry_after": result.retry_after,
                        "reset_time": result.reset_time.isoformat()
                    }
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


# 常用装饰器
ai_request_limit = create_rate_limit_decorator("user_ai_requests")
api_call_limit = create_rate_limit_decorator("user_api_calls")
feedback_limit = create_rate_limit_decorator("feedback_submit")
config_update_limit = create_rate_limit_decorator("config_update")
