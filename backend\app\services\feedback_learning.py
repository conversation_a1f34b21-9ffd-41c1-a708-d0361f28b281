"""
用户反馈学习服务
基于用户反馈改进AI分类准确性
"""

import re
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, or_, func

from ..models.user_feedback import (
    UserFeedback, UserLearningPattern, CategoryAccuracy, AIModelPerformance
)
from ..utils.cache import get_cache_manager


class FeedbackLearningService:
    """反馈学习服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def record_feedback(
        self,
        user_id: str,
        transaction_description: str,
        transaction_amount: float,
        transaction_type: str,
        ai_suggested_category: str,
        ai_confidence: float,
        ai_reasoning: str,
        user_accepted: bool,
        user_corrected_category: str = None,
        user_comment: str = None
    ) -> UserFeedback:
        """记录用户反馈"""
        
        feedback = UserFeedback(
            user_id=user_id,
            transaction_description=transaction_description,
            transaction_amount=transaction_amount,
            transaction_type=transaction_type,
            ai_suggested_category=ai_suggested_category,
            ai_confidence=ai_confidence,
            ai_reasoning=ai_reasoning,
            user_accepted=user_accepted,
            user_corrected_category=user_corrected_category,
            user_comment=user_comment,
            feedback_type="correction" if not user_accepted else "confirmation"
        )
        
        self.db.add(feedback)
        await self.db.commit()
        await self.db.refresh(feedback)
        
        # 异步处理学习
        await self._process_feedback_async(feedback)
        
        logger.info(f"用户反馈已记录: {user_id}, 接受={user_accepted}, 分类={ai_suggested_category}")
        return feedback
    
    async def _process_feedback_async(self, feedback: UserFeedback):
        """异步处理反馈学习"""
        try:
            # 更新分类准确性统计
            await self._update_category_accuracy(feedback)
            
            # 提取和更新学习模式
            await self._extract_learning_patterns(feedback)
            
            # 更新缓存
            await self._update_user_cache(feedback.user_id)
            
            # 标记为已处理
            feedback.is_processed = True
            feedback.processed_at = datetime.now()
            await self.db.commit()
            
        except Exception as e:
            logger.error(f"处理反馈学习失败: {e}")
    
    async def _update_category_accuracy(self, feedback: UserFeedback):
        """更新分类准确性统计"""
        
        # 查找或创建准确性记录
        result = await self.db.execute(
            select(CategoryAccuracy).where(
                and_(
                    CategoryAccuracy.user_id == feedback.user_id,
                    CategoryAccuracy.category_name == feedback.ai_suggested_category
                )
            )
        )
        accuracy = result.scalar_one_or_none()
        
        if not accuracy:
            accuracy = CategoryAccuracy(
                user_id=feedback.user_id,
                category_name=feedback.ai_suggested_category
            )
            self.db.add(accuracy)
        
        # 更新统计
        accuracy.update_accuracy(feedback.user_accepted, feedback.ai_confidence)
        await self.db.commit()
    
    async def _extract_learning_patterns(self, feedback: UserFeedback):
        """提取学习模式"""
        
        if not feedback.user_accepted and feedback.user_corrected_category:
            # 用户修正了分类，提取模式
            
            # 1. 关键词模式
            await self._extract_keyword_patterns(feedback)
            
            # 2. 金额范围模式
            await self._extract_amount_patterns(feedback)
            
            # 3. 商户模式
            await self._extract_merchant_patterns(feedback)
    
    async def _extract_keyword_patterns(self, feedback: UserFeedback):
        """提取关键词模式"""
        
        description = feedback.transaction_description.lower()
        
        # 提取关键词（简单的词汇提取）
        keywords = re.findall(r'\b\w{2,}\b', description)
        
        for keyword in keywords:
            if len(keyword) >= 2:  # 过滤太短的词
                await self._update_or_create_pattern(
                    user_id=feedback.user_id,
                    pattern_type="keyword",
                    pattern_key=keyword,
                    pattern_data={"keyword": keyword, "context": description[:100]},
                    preferred_category=feedback.user_corrected_category
                )
    
    async def _extract_amount_patterns(self, feedback: UserFeedback):
        """提取金额范围模式"""
        
        amount = feedback.transaction_amount
        
        # 定义金额范围
        ranges = [
            (0, 50, "small"),
            (50, 200, "medium"),
            (200, 1000, "large"),
            (1000, float('inf'), "very_large")
        ]
        
        for min_amount, max_amount, range_name in ranges:
            if min_amount <= amount < max_amount:
                pattern_key = f"{feedback.transaction_type}_{range_name}"
                await self._update_or_create_pattern(
                    user_id=feedback.user_id,
                    pattern_type="amount_range",
                    pattern_key=pattern_key,
                    pattern_data={
                        "min_amount": min_amount,
                        "max_amount": max_amount,
                        "transaction_type": feedback.transaction_type,
                        "range_name": range_name
                    },
                    preferred_category=feedback.user_corrected_category
                )
                break
    
    async def _extract_merchant_patterns(self, feedback: UserFeedback):
        """提取商户模式"""
        
        description = feedback.transaction_description
        
        # 简单的商户名提取（可以改进）
        # 查找可能的商户名模式
        merchant_patterns = [
            r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',  # 首字母大写的词组
            r'(\w+店|\w+超市|\w+商场|\w+餐厅)',  # 中文商户后缀
        ]
        
        for pattern in merchant_patterns:
            matches = re.findall(pattern, description)
            for match in matches:
                if len(match) >= 2:
                    await self._update_or_create_pattern(
                        user_id=feedback.user_id,
                        pattern_type="merchant",
                        pattern_key=match.lower(),
                        pattern_data={"merchant_name": match, "full_description": description},
                        preferred_category=feedback.user_corrected_category
                    )
    
    async def _update_or_create_pattern(
        self,
        user_id: str,
        pattern_type: str,
        pattern_key: str,
        pattern_data: Dict[str, Any],
        preferred_category: str
    ):
        """更新或创建学习模式"""
        
        # 查找现有模式
        result = await self.db.execute(
            select(UserLearningPattern).where(
                and_(
                    UserLearningPattern.user_id == user_id,
                    UserLearningPattern.pattern_type == pattern_type,
                    UserLearningPattern.pattern_key == pattern_key
                )
            )
        )
        pattern = result.scalar_one_or_none()
        
        if pattern:
            # 更新现有模式
            pattern.usage_count += 1
            pattern.last_used = datetime.now()
            
            # 如果用户选择了不同的分类，降低置信度提升
            if pattern.preferred_category != preferred_category:
                pattern.confidence_boost = max(0.05, pattern.confidence_boost - 0.02)
            else:
                # 相同分类，增加置信度提升
                pattern.confidence_boost = min(0.3, pattern.confidence_boost + 0.01)
        else:
            # 创建新模式
            pattern = UserLearningPattern(
                user_id=user_id,
                pattern_type=pattern_type,
                pattern_key=pattern_key,
                preferred_category=preferred_category,
                confidence_boost=0.1,
                usage_count=1,
                last_used=datetime.now()
            )
            pattern.update_pattern_data(pattern_data)
            self.db.add(pattern)
        
        await self.db.commit()
    
    async def _update_user_cache(self, user_id: str):
        """更新用户缓存"""
        cache = await get_cache_manager()
        
        # 清除用户相关缓存
        await cache.invalidate_user_cache(user_id)
        
        # 重新缓存用户模式
        patterns = await self.get_user_patterns(user_id)
        await cache.cache_user_patterns(user_id, patterns)
    
    async def get_user_patterns(self, user_id: str) -> Dict[str, Any]:
        """获取用户学习模式"""
        
        result = await self.db.execute(
            select(UserLearningPattern).where(
                UserLearningPattern.user_id == user_id
            ).order_by(UserLearningPattern.usage_count.desc())
        )
        patterns = result.scalars().all()
        
        # 按类型组织模式
        organized_patterns = {
            "keyword": [],
            "amount_range": [],
            "merchant": [],
            "time_based": []
        }
        
        for pattern in patterns:
            if pattern.pattern_type in organized_patterns:
                organized_patterns[pattern.pattern_type].append({
                    "key": pattern.pattern_key,
                    "category": pattern.preferred_category,
                    "confidence_boost": pattern.confidence_boost,
                    "usage_count": pattern.usage_count,
                    "success_rate": pattern.success_rate,
                    "data": pattern.get_pattern_data()
                })
        
        return organized_patterns
    
    async def apply_learning_patterns(
        self,
        user_id: str,
        transaction_description: str,
        transaction_amount: float,
        transaction_type: str,
        base_categories: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """应用学习模式调整分类建议"""
        
        # 获取用户模式
        cache = await get_cache_manager()
        patterns = await cache.get_user_patterns(user_id)
        
        if not patterns:
            patterns = await self.get_user_patterns(user_id)
            await cache.cache_user_patterns(user_id, patterns)
        
        # 创建分类权重映射
        category_boosts = defaultdict(float)
        
        # 应用关键词模式
        description_lower = transaction_description.lower()
        for keyword_pattern in patterns.get("keyword", []):
            if keyword_pattern["key"] in description_lower:
                category_boosts[keyword_pattern["category"]] += keyword_pattern["confidence_boost"]
        
        # 应用金额范围模式
        for amount_pattern in patterns.get("amount_range", []):
            pattern_data = amount_pattern["data"]
            if (pattern_data.get("transaction_type") == transaction_type and
                pattern_data.get("min_amount", 0) <= transaction_amount < pattern_data.get("max_amount", float('inf'))):
                category_boosts[amount_pattern["category"]] += amount_pattern["confidence_boost"]
        
        # 应用商户模式
        for merchant_pattern in patterns.get("merchant", []):
            if merchant_pattern["key"] in description_lower:
                category_boosts[merchant_pattern["category"]] += merchant_pattern["confidence_boost"]
        
        # 调整分类建议
        adjusted_categories = []
        for category in base_categories:
            boost = category_boosts.get(category["name"], 0)
            adjusted_category = category.copy()
            adjusted_category["confidence_boost"] = boost
            adjusted_category["learning_applied"] = boost > 0
            adjusted_categories.append(adjusted_category)
        
        # 按调整后的权重排序
        adjusted_categories.sort(key=lambda x: x.get("confidence_boost", 0), reverse=True)
        
        return adjusted_categories
    
    async def get_user_feedback_stats(self, user_id: str) -> Dict[str, Any]:
        """获取用户反馈统计"""
        
        # 总反馈数
        total_result = await self.db.execute(
            select(func.count(UserFeedback.id)).where(UserFeedback.user_id == user_id)
        )
        total_feedbacks = total_result.scalar() or 0
        
        # 接受率
        accepted_result = await self.db.execute(
            select(func.count(UserFeedback.id)).where(
                and_(UserFeedback.user_id == user_id, UserFeedback.user_accepted == True)
            )
        )
        accepted_feedbacks = accepted_result.scalar() or 0
        
        # 分类准确性
        accuracy_result = await self.db.execute(
            select(CategoryAccuracy).where(CategoryAccuracy.user_id == user_id)
        )
        accuracies = accuracy_result.scalars().all()
        
        # 学习模式数量
        patterns_result = await self.db.execute(
            select(func.count(UserLearningPattern.id)).where(UserLearningPattern.user_id == user_id)
        )
        patterns_count = patterns_result.scalar() or 0
        
        return {
            "total_feedbacks": total_feedbacks,
            "accepted_feedbacks": accepted_feedbacks,
            "acceptance_rate": accepted_feedbacks / max(1, total_feedbacks),
            "category_accuracies": [acc.to_dict() for acc in accuracies],
            "learning_patterns_count": patterns_count,
            "overall_accuracy": sum(acc.accuracy_rate for acc in accuracies) / max(1, len(accuracies))
        }
