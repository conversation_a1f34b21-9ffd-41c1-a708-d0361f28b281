"""
国际化API
提供语言设置和翻译管理功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, Header, Query
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any, List
from pydantic import BaseModel

from ..database.connection import get_db
from ..models.user import User
from ..auth.dependencies import get_current_user
from ..utils.i18n import get_i18n_manager, SupportedLanguage, t
from ..middleware.rate_limit import api_call_limit
from loguru import logger

router = APIRouter(prefix="/i18n", tags=["国际化"])


class LanguagePreference(BaseModel):
    """语言偏好设置"""
    language: SupportedLanguage
    timezone: Optional[str] = "Asia/Shanghai"
    date_format: Optional[str] = "YYYY-MM-DD"
    currency: Optional[str] = "CNY"


class TranslationRequest(BaseModel):
    """翻译请求"""
    keys: List[str]
    language: Optional[SupportedLanguage] = None


class AddTranslationRequest(BaseModel):
    """添加翻译请求"""
    language: SupportedLanguage
    key: str
    value: str


@router.get("/languages")
async def get_supported_languages():
    """获取支持的语言列表"""
    try:
        i18n_manager = get_i18n_manager()
        languages = i18n_manager.get_supported_languages()
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "languages": [
                    {
                        "code": code,
                        "name": name,
                        "native_name": name
                    }
                    for code, name in languages.items()
                ],
                "default_language": i18n_manager.default_language.value,
                "current_language": i18n_manager.current_language.value
            },
            "message": t("common.success")
        })
        
    except Exception as e:
        logger.error(f"获取支持语言列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=t("errors.server_error")
        )


@router.post("/set-language")
@api_call_limit
async def set_language(
    preference: LanguagePreference,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """设置用户语言偏好"""
    try:
        i18n_manager = get_i18n_manager()
        
        # 设置语言
        i18n_manager.set_language(preference.language)
        
        # 这里可以将用户的语言偏好保存到数据库
        # 为了简化，我们暂时只在内存中设置
        
        logger.info(f"用户 {current_user.username} 设置语言为: {preference.language.value}")
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "language": preference.language.value,
                "timezone": preference.timezone,
                "date_format": preference.date_format,
                "currency": preference.currency
            },
            "message": t("common.success")
        })
        
    except Exception as e:
        logger.error(f"设置语言失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=t("errors.server_error")
        )


@router.post("/translate")
@api_call_limit
async def translate_texts(
    request: TranslationRequest,
    accept_language: Optional[str] = Header(None),
    current_user: User = Depends(get_current_user)
):
    """批量翻译文本"""
    try:
        i18n_manager = get_i18n_manager()
        
        # 确定使用的语言
        target_language = request.language
        if not target_language and accept_language:
            # 从HTTP头解析语言
            lang_code = accept_language.split(',')[0].strip()
            try:
                target_language = SupportedLanguage(lang_code)
            except ValueError:
                target_language = None
        
        # 翻译所有请求的键
        translations = {}
        for key in request.keys:
            translations[key] = i18n_manager.get_text(key, target_language)
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "translations": translations,
                "language": target_language.value if target_language else i18n_manager.current_language.value,
                "total_keys": len(request.keys)
            },
            "message": t("common.success")
        })
        
    except Exception as e:
        logger.error(f"批量翻译失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=t("errors.server_error")
        )


@router.get("/translate/{key}")
@api_call_limit
async def translate_single_text(
    key: str,
    language: Optional[SupportedLanguage] = Query(None),
    accept_language: Optional[str] = Header(None),
    current_user: User = Depends(get_current_user)
):
    """翻译单个文本"""
    try:
        i18n_manager = get_i18n_manager()
        
        # 确定使用的语言
        target_language = language
        if not target_language and accept_language:
            lang_code = accept_language.split(',')[0].strip()
            try:
                target_language = SupportedLanguage(lang_code)
            except ValueError:
                target_language = None
        
        translation = i18n_manager.get_text(key, target_language)
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "key": key,
                "translation": translation,
                "language": target_language.value if target_language else i18n_manager.current_language.value
            },
            "message": t("common.success")
        })
        
    except Exception as e:
        logger.error(f"翻译单个文本失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=t("errors.server_error")
        )


@router.get("/current-language")
async def get_current_language(
    current_user: User = Depends(get_current_user)
):
    """获取当前语言设置"""
    try:
        i18n_manager = get_i18n_manager()
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "current_language": i18n_manager.current_language.value,
                "default_language": i18n_manager.default_language.value,
                "supported_languages": list(i18n_manager.get_supported_languages().keys())
            },
            "message": t("common.success")
        })
        
    except Exception as e:
        logger.error(f"获取当前语言失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=t("errors.server_error")
        )


@router.post("/add-translation")
@api_call_limit
async def add_translation(
    request: AddTranslationRequest,
    current_user: User = Depends(get_current_user)
):
    """添加自定义翻译（管理员功能）"""
    try:
        # 这里可以添加管理员权限检查
        # if not current_user.is_admin:
        #     raise HTTPException(status_code=403, detail="权限不足")
        
        i18n_manager = get_i18n_manager()
        i18n_manager.add_translation(request.language, request.key, request.value)
        
        logger.info(f"用户 {current_user.username} 添加翻译: {request.language.value}.{request.key}")
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "language": request.language.value,
                "key": request.key,
                "value": request.value
            },
            "message": t("common.success")
        })
        
    except Exception as e:
        logger.error(f"添加翻译失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=t("errors.server_error")
        )


@router.get("/localization-data")
@api_call_limit
async def get_localization_data(
    language: Optional[SupportedLanguage] = Query(None),
    sections: Optional[str] = Query(None, description="指定要获取的部分，逗号分隔"),
    current_user: User = Depends(get_current_user)
):
    """获取本地化数据"""
    try:
        i18n_manager = get_i18n_manager()
        
        target_language = language or i18n_manager.current_language
        translations = i18n_manager.translations.get(target_language.value, {})
        
        # 如果指定了特定部分，只返回这些部分
        if sections:
            section_list = [s.strip() for s in sections.split(',')]
            filtered_translations = {}
            for section in section_list:
                if section in translations:
                    filtered_translations[section] = translations[section]
            translations = filtered_translations
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "language": target_language.value,
                "translations": translations,
                "sections": list(translations.keys()) if translations else [],
                "total_keys": sum(
                    len(section) if isinstance(section, dict) else 1 
                    for section in translations.values()
                )
            },
            "message": t("common.success")
        })
        
    except Exception as e:
        logger.error(f"获取本地化数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=t("errors.server_error")
        )


@router.get("/health")
async def i18n_health_check():
    """国际化服务健康检查"""
    try:
        i18n_manager = get_i18n_manager()
        
        # 检查翻译文件是否正常加载
        loaded_languages = list(i18n_manager.translations.keys())
        
        # 测试翻译功能
        test_key = "common.success"
        test_translations = {}
        for lang_code in loaded_languages:
            try:
                lang = SupportedLanguage(lang_code)
                test_translations[lang_code] = i18n_manager.get_text(test_key, lang)
            except ValueError:
                continue
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "status": "healthy",
                "loaded_languages": loaded_languages,
                "supported_languages": len(SupportedLanguage),
                "test_translations": test_translations,
                "current_language": i18n_manager.current_language.value,
                "translations_dir": str(i18n_manager.translations_dir)
            },
            "message": "国际化服务运行正常"
        })
        
    except Exception as e:
        logger.error(f"国际化健康检查失败: {e}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "success": False,
                "data": {
                    "status": "unhealthy",
                    "error": str(e)
                },
                "message": "国际化服务异常"
            }
        )
