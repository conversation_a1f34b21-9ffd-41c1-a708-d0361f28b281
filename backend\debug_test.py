#!/usr/bin/env python3
"""
调试测试脚本
"""

import asyncio
import httpx
import traceback

async def test_endpoints():
    """测试各个端点"""
    
    async with httpx.AsyncClient() as client:
        # 测试根端点
        print("=== 测试根端点 ===")
        try:
            response = await client.get("http://localhost:8002/")
            print(f"GET / - Status: {response.status_code}")
            print(f"Response: {response.text[:200]}")
        except Exception as e:
            print(f"GET / - Error: {e}")

        # 测试健康检查
        print("\n=== 测试健康检查 ===")
        try:
            response = await client.get("http://localhost:8002/health")
            print(f"GET /health - Status: {response.status_code}")
            print(f"Response: {response.text}")
        except Exception as e:
            print(f"GET /health - Error: {e}")

        # 测试API文档
        print("\n=== 测试API文档 ===")
        try:
            response = await client.get("http://localhost:8002/docs")
            print(f"GET /docs - Status: {response.status_code}")
            print(f"Response length: {len(response.text)}")
        except Exception as e:
            print(f"GET /docs - Error: {e}")

        # 测试注册端点
        print("\n=== 测试注册端点 ===")
        try:
            response = await client.post(
                "http://localhost:8002/api/v1/auth/register",
                json={
                    "email": "<EMAIL>",
                    "password": "testpassword123"
                }
            )
            print(f"POST /api/v1/auth/register - Status: {response.status_code}")
            print(f"Response: {response.text}")
        except Exception as e:
            print(f"POST /api/v1/auth/register - Error: {e}")
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_endpoints())
