"""
支出预测引擎
使用机器学习算法预测用户未来支出趋势和模式
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import asyncio
import json

from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from sqlalchemy.orm import selectinload

from ..models.transaction import Transaction
from ..models.category import Category
from ..models.user import User
from ..utils.cache import get_cache_manager
from ..utils.structured_logger import structured_logger, LogCategory


@dataclass
class SpendingPrediction:
    """支出预测结果"""
    category_name: str
    predicted_amount: float
    confidence: float
    prediction_period: str  # 'daily', 'weekly', 'monthly'
    trend: str  # 'increasing', 'decreasing', 'stable'
    factors: List[str]
    historical_average: float
    seasonal_adjustment: float


@dataclass
class SpendingTrend:
    """支出趋势"""
    period: str
    total_spending: float
    category_breakdown: Dict[str, float]
    growth_rate: float
    volatility: float
    anomalies: List[Dict[str, Any]]


class SpendingPredictionEngine:
    """支出预测引擎"""
    
    def __init__(self):
        self.cache_ttl = 1800  # 30分钟缓存
        self.min_data_points = 10  # 最少需要的数据点
        
        # 季节性因子（简化实现）
        self.seasonal_factors = {
            1: 1.1,   # 1月 - 新年消费增加
            2: 0.9,   # 2月 - 春节后消费减少
            3: 1.0,   # 3月 - 正常
            4: 1.0,   # 4月 - 正常
            5: 1.1,   # 5月 - 五一假期
            6: 1.0,   # 6月 - 正常
            7: 1.1,   # 7月 - 暑假
            8: 1.1,   # 8月 - 暑假
            9: 1.0,   # 9月 - 正常
            10: 1.1,  # 10月 - 国庆假期
            11: 1.2,  # 11月 - 双十一购物
            12: 1.3,  # 12月 - 年末消费增加
        }
        
        # 周期性因子
        self.weekly_factors = {
            0: 1.0,   # 周一
            1: 1.0,   # 周二
            2: 1.0,   # 周三
            3: 1.0,   # 周四
            4: 1.2,   # 周五 - 周末前消费增加
            5: 1.3,   # 周六 - 周末消费
            6: 1.1,   # 周日 - 周末消费
        }
    
    async def predict_future_spending(
        self,
        user_id: str,
        prediction_days: int,
        db: AsyncSession,
        categories: Optional[List[str]] = None
    ) -> List[SpendingPrediction]:
        """预测未来支出"""
        
        # 检查缓存
        cache = await get_cache_manager()
        cache_key = f"spending_prediction:{user_id}:{prediction_days}:{hash(str(categories))}"
        
        cached_result = await cache.get(cache_key)
        if cached_result:
            logger.info(f"使用缓存的支出预测结果: {user_id}")
            return [SpendingPrediction(**pred) for pred in cached_result]
        
        try:
            # 获取历史数据
            historical_data = await self._get_historical_data(user_id, db, days=365)
            
            if len(historical_data) < self.min_data_points:
                logger.warning(f"用户 {user_id} 历史数据不足，无法进行准确预测")
                return []
            
            # 数据预处理
            df = pd.DataFrame(historical_data)
            df['transaction_date'] = pd.to_datetime(df['transaction_date'])
            df = df.sort_values('transaction_date')
            
            predictions = []
            
            # 如果指定了分类，只预测这些分类
            if categories:
                target_categories = categories
            else:
                # 获取用户最常用的分类
                target_categories = await self._get_top_categories(user_id, db, limit=10)
            
            for category in target_categories:
                category_data = df[df['category_name'] == category]
                
                if len(category_data) >= self.min_data_points:
                    prediction = await self._predict_category_spending(
                        category, category_data, prediction_days, user_id
                    )
                    if prediction:
                        predictions.append(prediction)
            
            # 缓存结果
            cache_data = [pred.__dict__ for pred in predictions]
            await cache.set(cache_key, cache_data, self.cache_ttl)
            
            logger.info(f"完成用户 {user_id} 的支出预测，预测了 {len(predictions)} 个分类")
            return predictions
            
        except Exception as e:
            logger.error(f"支出预测失败: {e}")
            return []
    
    async def _get_historical_data(
        self, user_id: str, db: AsyncSession, days: int = 365
    ) -> List[Dict[str, Any]]:
        """获取历史数据"""
        
        cutoff_date = datetime.now() - timedelta(days=days)
        
        query = select(Transaction).where(
            and_(
                Transaction.user_id == user_id,
                Transaction.transaction_date >= cutoff_date,
                Transaction.transaction_type == 'expense'  # 只考虑支出
            )
        ).options(selectinload(Transaction.category)).order_by(Transaction.transaction_date)
        
        result = await db.execute(query)
        transactions = result.scalars().all()
        
        historical_data = []
        for trans in transactions:
            historical_data.append({
                'transaction_date': trans.transaction_date,
                'amount': float(trans.amount),
                'category_name': trans.category.name if trans.category else '未分类',
                'description': trans.description,
                'day_of_week': trans.transaction_date.weekday(),
                'month': trans.transaction_date.month,
                'day_of_month': trans.transaction_date.day
            })
        
        return historical_data
    
    async def _get_top_categories(
        self, user_id: str, db: AsyncSession, limit: int = 10
    ) -> List[str]:
        """获取用户最常用的分类"""
        
        query = select(
            Category.name,
            func.count(Transaction.id).label('count'),
            func.sum(Transaction.amount).label('total_amount')
        ).join(Transaction).where(
            and_(
                Transaction.user_id == user_id,
                Transaction.transaction_type == 'expense'
            )
        ).group_by(Category.name).order_by(desc('total_amount')).limit(limit)
        
        result = await db.execute(query)
        categories = [row[0] for row in result.all()]
        
        return categories
    
    async def _predict_category_spending(
        self,
        category: str,
        category_data: pd.DataFrame,
        prediction_days: int,
        user_id: str
    ) -> Optional[SpendingPrediction]:
        """预测特定分类的支出"""
        
        try:
            # 计算基础统计信息
            daily_amounts = category_data.groupby(
                category_data['transaction_date'].dt.date
            )['amount'].sum()
            
            if len(daily_amounts) < 7:  # 至少需要一周的数据
                return None
            
            # 计算历史平均值
            historical_average = daily_amounts.mean()
            
            # 计算趋势
            trend = self._calculate_trend(daily_amounts)
            
            # 应用季节性调整
            current_month = datetime.now().month
            seasonal_factor = self.seasonal_factors.get(current_month, 1.0)
            
            # 应用周期性调整
            current_day_of_week = datetime.now().weekday()
            weekly_factor = self.weekly_factors.get(current_day_of_week, 1.0)
            
            # 预测未来支出
            base_prediction = historical_average * trend['slope'] if trend['slope'] > 0 else historical_average
            seasonal_adjustment = base_prediction * (seasonal_factor - 1.0)
            weekly_adjustment = base_prediction * (weekly_factor - 1.0)
            
            predicted_amount = base_prediction + seasonal_adjustment + weekly_adjustment
            predicted_amount = max(predicted_amount, 0)  # 确保非负
            
            # 计算置信度
            confidence = self._calculate_confidence(daily_amounts, trend)
            
            # 确定预测周期
            prediction_period = 'daily' if prediction_days <= 7 else 'weekly' if prediction_days <= 30 else 'monthly'
            
            # 分析影响因素
            factors = self._analyze_prediction_factors(
                category_data, trend, seasonal_factor, weekly_factor
            )
            
            return SpendingPrediction(
                category_name=category,
                predicted_amount=predicted_amount,
                confidence=confidence,
                prediction_period=prediction_period,
                trend=trend['direction'],
                factors=factors,
                historical_average=historical_average,
                seasonal_adjustment=seasonal_adjustment
            )
            
        except Exception as e:
            logger.error(f"预测分类 {category} 支出失败: {e}")
            return None
    
    def _calculate_trend(self, daily_amounts: pd.Series) -> Dict[str, Any]:
        """计算趋势"""
        
        if len(daily_amounts) < 3:
            return {'direction': 'stable', 'slope': 0, 'r_squared': 0}
        
        # 使用线性回归计算趋势
        x = np.arange(len(daily_amounts))
        y = daily_amounts.values
        
        # 计算线性回归系数
        n = len(x)
        sum_x = np.sum(x)
        sum_y = np.sum(y)
        sum_xy = np.sum(x * y)
        sum_x2 = np.sum(x * x)
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        intercept = (sum_y - slope * sum_x) / n
        
        # 计算R²
        y_pred = slope * x + intercept
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - np.mean(y)) ** 2)
        r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        # 确定趋势方向
        if abs(slope) < 0.1:
            direction = 'stable'
        elif slope > 0:
            direction = 'increasing'
        else:
            direction = 'decreasing'
        
        return {
            'direction': direction,
            'slope': slope,
            'r_squared': r_squared
        }
    
    def _calculate_confidence(self, daily_amounts: pd.Series, trend: Dict[str, Any]) -> float:
        """计算预测置信度"""
        
        # 基础置信度基于数据量
        data_confidence = min(len(daily_amounts) / 30, 1.0)  # 30天数据为满分
        
        # 趋势置信度基于R²
        trend_confidence = trend['r_squared']
        
        # 稳定性置信度基于变异系数
        cv = daily_amounts.std() / daily_amounts.mean() if daily_amounts.mean() > 0 else 1
        stability_confidence = max(0, 1 - cv)
        
        # 综合置信度
        confidence = (data_confidence * 0.4 + trend_confidence * 0.3 + stability_confidence * 0.3)
        
        return min(max(confidence, 0.1), 0.95)  # 限制在0.1-0.95之间
    
    def _analyze_prediction_factors(
        self,
        category_data: pd.DataFrame,
        trend: Dict[str, Any],
        seasonal_factor: float,
        weekly_factor: float
    ) -> List[str]:
        """分析预测影响因素"""
        
        factors = []
        
        # 趋势因素
        if trend['direction'] == 'increasing':
            factors.append(f"支出呈上升趋势 (斜率: {trend['slope']:.2f})")
        elif trend['direction'] == 'decreasing':
            factors.append(f"支出呈下降趋势 (斜率: {trend['slope']:.2f})")
        else:
            factors.append("支出相对稳定")
        
        # 季节性因素
        if seasonal_factor > 1.1:
            factors.append(f"当前月份消费通常较高 (季节因子: {seasonal_factor:.1f})")
        elif seasonal_factor < 0.9:
            factors.append(f"当前月份消费通常较低 (季节因子: {seasonal_factor:.1f})")
        
        # 周期性因素
        if weekly_factor > 1.1:
            factors.append(f"当前星期消费通常较高 (周期因子: {weekly_factor:.1f})")
        
        # 数据质量因素
        if trend['r_squared'] > 0.7:
            factors.append("历史数据趋势明显，预测可靠性较高")
        elif trend['r_squared'] < 0.3:
            factors.append("历史数据波动较大，预测不确定性较高")
        
        return factors

    async def analyze_spending_trends(
        self,
        user_id: str,
        db: AsyncSession,
        period: str = 'monthly'  # 'daily', 'weekly', 'monthly'
    ) -> List[SpendingTrend]:
        """分析支出趋势"""

        try:
            # 获取历史数据
            days = {'daily': 30, 'weekly': 84, 'monthly': 365}[period]
            historical_data = await self._get_historical_data(user_id, db, days)

            if not historical_data:
                return []

            df = pd.DataFrame(historical_data)
            df['transaction_date'] = pd.to_datetime(df['transaction_date'])

            trends = []

            if period == 'daily':
                # 按天分组
                grouped = df.groupby(df['transaction_date'].dt.date)
            elif period == 'weekly':
                # 按周分组
                df['week'] = df['transaction_date'].dt.isocalendar().week
                df['year'] = df['transaction_date'].dt.year
                grouped = df.groupby(['year', 'week'])
            else:  # monthly
                # 按月分组
                df['month'] = df['transaction_date'].dt.to_period('M')
                grouped = df.groupby('month')

            for period_key, group in grouped:
                total_spending = group['amount'].sum()
                category_breakdown = group.groupby('category_name')['amount'].sum().to_dict()

                # 计算增长率（与前一期比较）
                growth_rate = 0.0  # 简化实现

                # 计算波动性
                volatility = group['amount'].std() / group['amount'].mean() if group['amount'].mean() > 0 else 0

                # 检测异常
                anomalies = self._detect_anomalies(group)

                trend = SpendingTrend(
                    period=str(period_key),
                    total_spending=total_spending,
                    category_breakdown=category_breakdown,
                    growth_rate=growth_rate,
                    volatility=volatility,
                    anomalies=anomalies
                )

                trends.append(trend)

            return trends

        except Exception as e:
            logger.error(f"分析支出趋势失败: {e}")
            return []

    def _detect_anomalies(self, group_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """检测异常支出"""

        anomalies = []
        amounts = group_data['amount']

        if len(amounts) < 3:
            return anomalies

        # 使用IQR方法检测异常值
        Q1 = amounts.quantile(0.25)
        Q3 = amounts.quantile(0.75)
        IQR = Q3 - Q1

        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR

        # 找出异常值
        anomaly_data = group_data[
            (group_data['amount'] < lower_bound) |
            (group_data['amount'] > upper_bound)
        ]

        for _, row in anomaly_data.iterrows():
            anomaly_type = 'unusually_high' if row['amount'] > upper_bound else 'unusually_low'
            anomalies.append({
                'type': anomaly_type,
                'amount': row['amount'],
                'description': row['description'],
                'category': row['category_name'],
                'date': row['transaction_date'].isoformat()
            })

        return anomalies

    async def predict_monthly_budget_needs(
        self,
        user_id: str,
        db: AsyncSession,
        target_month: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """预测月度预算需求"""

        if not target_month:
            target_month = datetime.now().replace(day=1) + timedelta(days=32)
            target_month = target_month.replace(day=1)  # 下个月第一天

        try:
            # 获取各分类的预测
            predictions = await self.predict_future_spending(user_id, 30, db)

            budget_recommendations = {}
            total_predicted = 0

            for pred in predictions:
                # 将日预测转换为月预测
                monthly_prediction = pred.predicted_amount * 30
                total_predicted += monthly_prediction

                budget_recommendations[pred.category_name] = {
                    'predicted_amount': monthly_prediction,
                    'confidence': pred.confidence,
                    'trend': pred.trend,
                    'recommended_budget': monthly_prediction * 1.1,  # 增加10%缓冲
                    'factors': pred.factors
                }

            # 获取历史月度平均
            historical_monthly = await self._get_historical_monthly_average(user_id, db)

            return {
                'target_month': target_month.strftime('%Y-%m'),
                'total_predicted': total_predicted,
                'total_recommended_budget': total_predicted * 1.1,
                'historical_monthly_average': historical_monthly,
                'category_recommendations': budget_recommendations,
                'confidence_score': np.mean([pred.confidence for pred in predictions]) if predictions else 0,
                'risk_assessment': self._assess_budget_risk(total_predicted, historical_monthly)
            }

        except Exception as e:
            logger.error(f"预测月度预算需求失败: {e}")
            return {}

    async def _get_historical_monthly_average(
        self, user_id: str, db: AsyncSession, months: int = 6
    ) -> float:
        """获取历史月度平均支出"""

        cutoff_date = datetime.now() - timedelta(days=months * 30)

        query = select(func.sum(Transaction.amount)).where(
            and_(
                Transaction.user_id == user_id,
                Transaction.transaction_type == 'expense',
                Transaction.transaction_date >= cutoff_date
            )
        )

        result = await db.execute(query)
        total_amount = result.scalar() or 0

        return float(total_amount) / months

    def _assess_budget_risk(self, predicted_amount: float, historical_average: float) -> Dict[str, Any]:
        """评估预算风险"""

        if historical_average == 0:
            return {'level': 'unknown', 'description': '缺乏历史数据'}

        ratio = predicted_amount / historical_average

        if ratio > 1.3:
            return {
                'level': 'high',
                'description': f'预测支出比历史平均高{(ratio-1)*100:.1f}%，建议谨慎规划'
            }
        elif ratio > 1.1:
            return {
                'level': 'medium',
                'description': f'预测支出比历史平均高{(ratio-1)*100:.1f}%，需要适当控制'
            }
        elif ratio < 0.8:
            return {
                'level': 'low',
                'description': f'预测支出比历史平均低{(1-ratio)*100:.1f}%，可以适当增加预算'
            }
        else:
            return {
                'level': 'normal',
                'description': '预测支出与历史水平相当，预算合理'
            }

    async def get_spending_insights(
        self,
        user_id: str,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """获取支出洞察"""

        try:
            # 获取预测数据
            predictions = await self.predict_future_spending(user_id, 30, db)

            # 获取趋势数据
            monthly_trends = await self.analyze_spending_trends(user_id, db, 'monthly')

            insights = {
                'summary': {
                    'total_categories_analyzed': len(predictions),
                    'average_prediction_confidence': np.mean([p.confidence for p in predictions]) if predictions else 0,
                    'dominant_trend': self._get_dominant_trend(predictions)
                },
                'category_insights': [],
                'trend_insights': [],
                'recommendations': []
            }

            # 分类洞察
            for pred in predictions:
                insights['category_insights'].append({
                    'category': pred.category_name,
                    'predicted_daily_spending': pred.predicted_amount,
                    'trend': pred.trend,
                    'confidence': pred.confidence,
                    'key_factors': pred.factors[:3]  # 前3个关键因素
                })

            # 趋势洞察
            if monthly_trends:
                latest_trend = monthly_trends[-1]
                insights['trend_insights'] = {
                    'latest_period': latest_trend.period,
                    'total_spending': latest_trend.total_spending,
                    'volatility': latest_trend.volatility,
                    'top_categories': sorted(
                        latest_trend.category_breakdown.items(),
                        key=lambda x: x[1],
                        reverse=True
                    )[:5],
                    'anomalies_detected': len(latest_trend.anomalies)
                }

            # 生成建议
            insights['recommendations'] = self._generate_spending_recommendations(predictions, monthly_trends)

            return insights

        except Exception as e:
            logger.error(f"获取支出洞察失败: {e}")
            return {}

    def _get_dominant_trend(self, predictions: List[SpendingPrediction]) -> str:
        """获取主导趋势"""

        if not predictions:
            return 'unknown'

        trend_counts = {'increasing': 0, 'decreasing': 0, 'stable': 0}

        for pred in predictions:
            trend_counts[pred.trend] += 1

        return max(trend_counts, key=trend_counts.get)

    def _generate_spending_recommendations(
        self,
        predictions: List[SpendingPrediction],
        trends: List[SpendingTrend]
    ) -> List[Dict[str, Any]]:
        """生成支出建议"""

        recommendations = []

        # 基于预测的建议
        for pred in predictions:
            if pred.trend == 'increasing' and pred.confidence > 0.7:
                recommendations.append({
                    'type': 'warning',
                    'category': pred.category_name,
                    'message': f'{pred.category_name}支出呈上升趋势，建议关注和控制',
                    'priority': 'high' if pred.predicted_amount > pred.historical_average * 1.5 else 'medium'
                })

        # 基于趋势的建议
        if trends:
            latest_trend = trends[-1]
            if latest_trend.volatility > 0.5:
                recommendations.append({
                    'type': 'advice',
                    'category': 'general',
                    'message': '支出波动较大，建议制定更详细的预算计划',
                    'priority': 'medium'
                })

            if latest_trend.anomalies:
                recommendations.append({
                    'type': 'alert',
                    'category': 'general',
                    'message': f'检测到{len(latest_trend.anomalies)}笔异常支出，请检查是否合理',
                    'priority': 'high'
                })

        return recommendations
