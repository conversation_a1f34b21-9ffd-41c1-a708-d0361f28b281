# AI记账报销软件 - 后端服务启动脚本 (PowerShell版本)

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   AI记账报销软件 - 后端服务启动脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "📍 当前目录: $(Get-Location)" -ForegroundColor Yellow
Write-Host ""

# 检查虚拟环境是否存在
if (-not (Test-Path "venv\Scripts\activate.ps1")) {
    Write-Host "❌ 虚拟环境不存在！" -ForegroundColor Red
    Write-Host "请先创建虚拟环境：python -m venv venv" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "🔧 激活虚拟环境..." -ForegroundColor Green
try {
    & "venv\Scripts\Activate.ps1"
    Write-Host "✅ 虚拟环境已激活" -ForegroundColor Green
} catch {
    Write-Host "❌ 虚拟环境激活失败！" -ForegroundColor Red
    Write-Host "错误信息: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""
Write-Host "🚀 启动后端服务..." -ForegroundColor Green
Write-Host "📖 API文档地址: http://localhost:8000/docs" -ForegroundColor Cyan
Write-Host "🔧 ReDoc文档地址: http://localhost:8000/redoc" -ForegroundColor Cyan
Write-Host "💡 按 Ctrl+C 停止服务" -ForegroundColor Yellow
Write-Host ""

try {
    python -m app.main
} catch {
    Write-Host ""
    Write-Host "❌ 服务启动失败！" -ForegroundColor Red
    Write-Host "错误信息: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    Write-Host ""
    Write-Host "👋 服务已停止" -ForegroundColor Yellow
    Read-Host "按任意键退出"
}
