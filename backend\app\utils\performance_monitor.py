"""
性能监控系统
提供API性能监控、资源使用监控和性能指标收集功能
"""

import time
import psutil
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import json
from loguru import logger
from .structured_logger import structured_logger, LogCategory
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, select, func
from ..database.connection import get_db
from ..models.user import User
from ..models.transaction import Transaction


class MetricType(Enum):
    """指标类型"""
    RESPONSE_TIME = "response_time"
    REQUEST_COUNT = "request_count"
    ERROR_RATE = "error_rate"
    CPU_USAGE = "cpu_usage"
    MEMORY_USAGE = "memory_usage"
    DATABASE_CONNECTIONS = "database_connections"
    CACHE_HIT_RATE = "cache_hit_rate"
    ACTIVE_USERS = "active_users"


@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    metric_type: MetricType
    value: float
    timestamp: datetime
    endpoint: Optional[str] = None
    user_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class SystemHealth:
    """系统健康状态"""
    cpu_percent: float
    memory_percent: float
    disk_usage: float
    active_connections: int
    response_time_avg: float
    error_rate: float
    uptime: float
    status: str  # healthy, warning, critical


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics: List[PerformanceMetric] = []
        self.start_time = datetime.now()
        self.request_times: Dict[str, List[float]] = {}
        self.request_counts: Dict[str, int] = {}
        self.error_counts: Dict[str, int] = {}
        self.cache_stats = {"hits": 0, "misses": 0}
        self.active_users: set = set()
        
    async def record_request(self, endpoint: str, response_time: float, 
                           status_code: int, user_id: Optional[str] = None):
        """记录请求性能数据"""
        try:
            # 记录响应时间
            if endpoint not in self.request_times:
                self.request_times[endpoint] = []
            self.request_times[endpoint].append(response_time)
            
            # 记录请求计数
            self.request_counts[endpoint] = self.request_counts.get(endpoint, 0) + 1
            
            # 记录错误计数
            if status_code >= 400:
                self.error_counts[endpoint] = self.error_counts.get(endpoint, 0) + 1
            
            # 记录活跃用户
            if user_id:
                self.active_users.add(user_id)
            
            # 创建性能指标
            metric = PerformanceMetric(
                metric_type=MetricType.RESPONSE_TIME,
                value=response_time,
                timestamp=datetime.now(),
                endpoint=endpoint,
                user_id=user_id,
                metadata={
                    "status_code": status_code,
                    "method": "GET"  # 可以从请求中获取
                }
            )
            
            self.metrics.append(metric)
            
            # 保持最近1000条记录
            if len(self.metrics) > 1000:
                self.metrics = self.metrics[-1000:]

            # 使用结构化日志记录
            structured_logger.log_performance_metric(
                'api_response_time',
                response_time,
                'seconds',
                endpoint=endpoint,
                status_code=status_code,
                user_id=user_id
            )

        except Exception as e:
            structured_logger.error(
                f"记录请求性能数据失败: {e}",
                LogCategory.PERFORMANCE,
                error=e,
                endpoint=endpoint
            )
            logger.error(f"记录请求性能数据失败: {e}")
    
    async def record_cache_hit(self, hit: bool):
        """记录缓存命中情况"""
        if hit:
            self.cache_stats["hits"] += 1
        else:
            self.cache_stats["misses"] += 1

        # 使用结构化日志记录
        structured_logger.log_cache_operation(
            'get',
            'unknown_key',  # 可以传入具体的key
            hit=hit
        )
    
    async def get_system_health(self) -> SystemHealth:
        """获取系统健康状态"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_usage = (disk.used / disk.total) * 100
            
            # 网络连接数
            connections = len(psutil.net_connections())
            
            # 平均响应时间
            all_times = []
            for times in self.request_times.values():
                all_times.extend(times[-100:])  # 最近100次请求
            response_time_avg = sum(all_times) / len(all_times) if all_times else 0
            
            # 错误率
            total_requests = sum(self.request_counts.values())
            total_errors = sum(self.error_counts.values())
            error_rate = (total_errors / total_requests * 100) if total_requests > 0 else 0
            
            # 运行时间
            uptime = (datetime.now() - self.start_time).total_seconds()
            
            # 确定系统状态
            status = "healthy"
            if cpu_percent > 80 or memory_percent > 80 or error_rate > 5:
                status = "warning"
            if cpu_percent > 95 or memory_percent > 95 or error_rate > 10:
                status = "critical"
            
            return SystemHealth(
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_usage=disk_usage,
                active_connections=connections,
                response_time_avg=response_time_avg,
                error_rate=error_rate,
                uptime=uptime,
                status=status
            )
            
        except Exception as e:
            logger.error(f"获取系统健康状态失败: {e}")
            return SystemHealth(
                cpu_percent=0,
                memory_percent=0,
                disk_usage=0,
                active_connections=0,
                response_time_avg=0,
                error_rate=0,
                uptime=0,
                status="unknown"
            )
    
    async def get_endpoint_stats(self, endpoint: Optional[str] = None) -> Dict[str, Any]:
        """获取端点统计信息"""
        try:
            if endpoint:
                # 特定端点统计
                times = self.request_times.get(endpoint, [])
                count = self.request_counts.get(endpoint, 0)
                errors = self.error_counts.get(endpoint, 0)
                
                return {
                    "endpoint": endpoint,
                    "request_count": count,
                    "error_count": errors,
                    "error_rate": (errors / count * 100) if count > 0 else 0,
                    "avg_response_time": sum(times) / len(times) if times else 0,
                    "min_response_time": min(times) if times else 0,
                    "max_response_time": max(times) if times else 0,
                    "recent_response_times": times[-10:]  # 最近10次
                }
            else:
                # 所有端点统计
                stats = {}
                for ep in self.request_times.keys():
                    stats[ep] = await self.get_endpoint_stats(ep)
                return stats
                
        except Exception as e:
            logger.error(f"获取端点统计信息失败: {e}")
            return {}
    
    async def get_performance_trends(self, hours: int = 24) -> Dict[str, List[Dict[str, Any]]]:
        """获取性能趋势数据"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_metrics = [m for m in self.metrics if m.timestamp >= cutoff_time]
            
            trends = {}
            for metric_type in MetricType:
                type_metrics = [m for m in recent_metrics if m.metric_type == metric_type]
                
                # 按小时分组
                hourly_data = {}
                for metric in type_metrics:
                    hour_key = metric.timestamp.strftime("%Y-%m-%d %H:00")
                    if hour_key not in hourly_data:
                        hourly_data[hour_key] = []
                    hourly_data[hour_key].append(metric.value)
                
                # 计算每小时平均值
                trend_data = []
                for hour, values in sorted(hourly_data.items()):
                    trend_data.append({
                        "timestamp": hour,
                        "value": sum(values) / len(values),
                        "count": len(values)
                    })
                
                trends[metric_type.value] = trend_data
            
            return trends
            
        except Exception as e:
            logger.error(f"获取性能趋势数据失败: {e}")
            return {}
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total = self.cache_stats["hits"] + self.cache_stats["misses"]
        hit_rate = (self.cache_stats["hits"] / total * 100) if total > 0 else 0
        
        return {
            "hits": self.cache_stats["hits"],
            "misses": self.cache_stats["misses"],
            "total": total,
            "hit_rate": hit_rate
        }
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            async for db in get_db():
                # 获取用户数量
                user_count = await db.scalar(select(func.count(User.id)))
                
                # 获取交易数量
                transaction_count = await db.scalar(select(func.count(Transaction.id)))
                
                # 获取今日交易数量
                today = datetime.now().date()
                today_transactions = await db.scalar(
                    select(func.count(Transaction.id)).where(
                        func.date(Transaction.created_at) == today
                    )
                )
                
                return {
                    "total_users": user_count or 0,
                    "total_transactions": transaction_count or 0,
                    "today_transactions": today_transactions or 0,
                    "connection_pool_size": 10,  # 从配置获取
                    "active_connections": 1  # 简化实现
                }
                
        except Exception as e:
            logger.error(f"获取数据库统计信息失败: {e}")
            return {
                "total_users": 0,
                "total_transactions": 0,
                "today_transactions": 0,
                "connection_pool_size": 0,
                "active_connections": 0
            }
    
    async def get_active_users_count(self) -> int:
        """获取活跃用户数量"""
        # 清理超过1小时的活跃用户记录
        # 这里简化实现，实际应该基于时间戳
        return len(self.active_users)
    
    async def export_metrics(self, start_time: Optional[datetime] = None, 
                           end_time: Optional[datetime] = None) -> Dict[str, Any]:
        """导出性能指标数据"""
        try:
            if not start_time:
                start_time = datetime.now() - timedelta(hours=24)
            if not end_time:
                end_time = datetime.now()
            
            filtered_metrics = [
                m for m in self.metrics 
                if start_time <= m.timestamp <= end_time
            ]
            
            # 转换为可序列化的格式
            export_data = {
                "export_time": datetime.now().isoformat(),
                "period": {
                    "start": start_time.isoformat(),
                    "end": end_time.isoformat()
                },
                "metrics": [
                    {
                        "type": m.metric_type.value,
                        "value": m.value,
                        "timestamp": m.timestamp.isoformat(),
                        "endpoint": m.endpoint,
                        "user_id": m.user_id,
                        "metadata": m.metadata
                    }
                    for m in filtered_metrics
                ],
                "summary": {
                    "total_metrics": len(filtered_metrics),
                    "unique_endpoints": len(set(m.endpoint for m in filtered_metrics if m.endpoint)),
                    "unique_users": len(set(m.user_id for m in filtered_metrics if m.user_id))
                }
            }
            
            return export_data
            
        except Exception as e:
            logger.error(f"导出性能指标数据失败: {e}")
            return {}
    
    async def clear_old_metrics(self, days: int = 7):
        """清理旧的性能指标数据"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            self.metrics = [m for m in self.metrics if m.timestamp >= cutoff_time]
            
            logger.info(f"清理了 {days} 天前的性能指标数据")
            
        except Exception as e:
            logger.error(f"清理旧性能指标数据失败: {e}")


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()
