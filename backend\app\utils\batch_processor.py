"""
批处理系统
提供高效的批量数据处理能力，包括批量交易处理、导入导出、分类等功能
"""

import asyncio
import json
import csv
import io
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union, Callable, AsyncGenerator
from enum import Enum
from dataclasses import dataclass, asdict
from pathlib import Path
import uuid

from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, update, delete, func
from sqlalchemy.orm import selectinload

from ..models.transaction import Transaction
from ..models.category import Category
from ..models.account import Account
from ..models.user import User
from ..database.connection import get_db
from ..utils.cache import get_cache_manager_sync
from ..utils.structured_logger import structured_logger, LogCategory


class BatchOperationType(Enum):
    """批处理操作类型"""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    CATEGORIZE = "categorize"
    IMPORT = "import"
    EXPORT = "export"


class BatchStatus(Enum):
    """批处理状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class BatchResult:
    """批处理结果"""
    success_count: int = 0
    error_count: int = 0
    total_count: int = 0
    errors: List[Dict[str, Any]] = None
    results: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.results is None:
            self.results = []
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return (self.success_count / self.total_count * 100) if self.total_count > 0 else 0
    
    def add_success(self, result: Dict[str, Any]):
        """添加成功结果"""
        self.success_count += 1
        self.results.append(result)
    
    def add_error(self, error: Dict[str, Any]):
        """添加错误"""
        self.error_count += 1
        self.errors.append(error)


@dataclass
class BatchJob:
    """批处理任务"""
    id: str
    user_id: str
    operation_type: BatchOperationType
    status: BatchStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    total_items: int = 0
    processed_items: int = 0
    result: Optional[BatchResult] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.result is None:
            self.result = BatchResult()
    
    @property
    def progress(self) -> float:
        """进度百分比"""
        return (self.processed_items / self.total_items * 100) if self.total_items > 0 else 0
    
    @property
    def duration(self) -> Optional[float]:
        """执行时长（秒）"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        elif self.started_at:
            return (datetime.now() - self.started_at).total_seconds()
        return None


class BatchProcessor:
    """批处理器"""
    
    def __init__(self):
        self.cache_manager = get_cache_manager_sync()
        self.jobs: Dict[str, BatchJob] = {}
        self.max_concurrent_jobs = 5
        self.batch_size = 100
        self._processing_semaphore = asyncio.Semaphore(self.max_concurrent_jobs)
    
    async def create_batch_job(
        self,
        user_id: str,
        operation_type: BatchOperationType,
        total_items: int,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """创建批处理任务"""
        job_id = str(uuid.uuid4())
        
        job = BatchJob(
            id=job_id,
            user_id=user_id,
            operation_type=operation_type,
            status=BatchStatus.PENDING,
            created_at=datetime.now(),
            total_items=total_items,
            metadata=metadata or {}
        )
        
        self.jobs[job_id] = job
        
        # 缓存任务信息
        await self.cache_manager.set(
            f"batch_job:{job_id}",
            asdict(job),
            ttl=86400  # 24小时
        )
        
        structured_logger.log_business_event(
            "batch_job_created",
            user_id=user_id,
            job_id=job_id,
            operation_type=operation_type.value,
            total_items=total_items
        )
        
        return job_id
    
    async def get_batch_job(self, job_id: str) -> Optional[BatchJob]:
        """获取批处理任务"""
        if job_id in self.jobs:
            return self.jobs[job_id]
        
        # 从缓存获取
        cached_job = await self.cache_manager.get(f"batch_job:{job_id}")
        if cached_job:
            job = BatchJob(**cached_job)
            self.jobs[job_id] = job
            return job
        
        return None
    
    async def update_job_progress(self, job_id: str, processed_items: int):
        """更新任务进度"""
        job = await self.get_batch_job(job_id)
        if job:
            job.processed_items = processed_items
            
            # 更新缓存
            await self.cache_manager.set(
                f"batch_job:{job_id}",
                asdict(job),
                ttl=86400
            )
    
    async def complete_job(self, job_id: str, result: BatchResult):
        """完成任务"""
        job = await self.get_batch_job(job_id)
        if job:
            job.status = BatchStatus.COMPLETED
            job.completed_at = datetime.now()
            job.result = result
            
            # 更新缓存
            await self.cache_manager.set(
                f"batch_job:{job_id}",
                asdict(job),
                ttl=86400
            )
            
            structured_logger.log_business_event(
                "batch_job_completed",
                user_id=job.user_id,
                job_id=job_id,
                success_count=result.success_count,
                error_count=result.error_count,
                duration=job.duration
            )
    
    async def fail_job(self, job_id: str, error: str):
        """标记任务失败"""
        job = await self.get_batch_job(job_id)
        if job:
            job.status = BatchStatus.FAILED
            job.completed_at = datetime.now()
            job.result.add_error({"error": error, "timestamp": datetime.now().isoformat()})
            
            # 更新缓存
            await self.cache_manager.set(
                f"batch_job:{job_id}",
                asdict(job),
                ttl=86400
            )
            
            structured_logger.log_business_event(
                "batch_job_failed",
                user_id=job.user_id,
                job_id=job_id,
                error=error
            )


    async def process_batch_transactions(
        self,
        job_id: str,
        transactions_data: List[Dict[str, Any]],
        operation_type: BatchOperationType
    ) -> BatchResult:
        """批量处理交易"""
        async with self._processing_semaphore:
            job = await self.get_batch_job(job_id)
            if not job:
                raise ValueError(f"批处理任务不存在: {job_id}")

            job.status = BatchStatus.PROCESSING
            job.started_at = datetime.now()

            result = BatchResult()
            result.total_count = len(transactions_data)

            try:
                async for db in get_db():
                    # 分批处理
                    for i in range(0, len(transactions_data), self.batch_size):
                        batch = transactions_data[i:i + self.batch_size]

                        if operation_type == BatchOperationType.CREATE:
                            await self._batch_create_transactions(db, batch, result, job.user_id)
                        elif operation_type == BatchOperationType.UPDATE:
                            await self._batch_update_transactions(db, batch, result, job.user_id)
                        elif operation_type == BatchOperationType.DELETE:
                            await self._batch_delete_transactions(db, batch, result, job.user_id)

                        # 更新进度
                        await self.update_job_progress(job_id, min(i + self.batch_size, len(transactions_data)))

                        # 提交批次
                        await db.commit()

                await self.complete_job(job_id, result)
                return result

            except Exception as e:
                await self.fail_job(job_id, str(e))
                raise

    async def _batch_create_transactions(
        self,
        db: AsyncSession,
        transactions_data: List[Dict[str, Any]],
        result: BatchResult,
        user_id: str
    ):
        """批量创建交易"""
        for data in transactions_data:
            try:
                # 验证数据
                if not self._validate_transaction_data(data):
                    result.add_error({
                        "data": data,
                        "error": "数据验证失败",
                        "timestamp": datetime.now().isoformat()
                    })
                    continue

                # 创建交易
                transaction = Transaction(
                    user_id=user_id,
                    account_id=data.get("account_id"),
                    category_id=data.get("category_id"),
                    amount=float(data.get("amount", 0)),
                    description=data.get("description", ""),
                    transaction_date=datetime.fromisoformat(data.get("transaction_date", datetime.now().isoformat())),
                    transaction_type=data.get("transaction_type", "expense"),
                    tags=data.get("tags", []),
                    location=data.get("location"),
                    receipt_url=data.get("receipt_url"),
                    notes=data.get("notes")
                )

                db.add(transaction)
                result.add_success({
                    "action": "created",
                    "transaction_id": transaction.id,
                    "amount": transaction.amount
                })

            except Exception as e:
                result.add_error({
                    "data": data,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })

    async def _batch_update_transactions(
        self,
        db: AsyncSession,
        transactions_data: List[Dict[str, Any]],
        result: BatchResult,
        user_id: str
    ):
        """批量更新交易"""
        for data in transactions_data:
            try:
                transaction_id = data.get("id")
                if not transaction_id:
                    result.add_error({
                        "data": data,
                        "error": "缺少交易ID",
                        "timestamp": datetime.now().isoformat()
                    })
                    continue

                # 查找交易
                transaction = await db.scalar(
                    select(Transaction).where(
                        and_(Transaction.id == transaction_id, Transaction.user_id == user_id)
                    )
                )

                if not transaction:
                    result.add_error({
                        "data": data,
                        "error": "交易不存在",
                        "timestamp": datetime.now().isoformat()
                    })
                    continue

                # 更新字段
                for field, value in data.items():
                    if field != "id" and hasattr(transaction, field):
                        if field == "transaction_date" and isinstance(value, str):
                            value = datetime.fromisoformat(value)
                        setattr(transaction, field, value)

                transaction.updated_at = datetime.now()

                result.add_success({
                    "action": "updated",
                    "transaction_id": transaction.id,
                    "amount": transaction.amount
                })

            except Exception as e:
                result.add_error({
                    "data": data,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })


    async def _batch_delete_transactions(
        self,
        db: AsyncSession,
        transactions_data: List[Dict[str, Any]],
        result: BatchResult,
        user_id: str
    ):
        """批量删除交易"""
        for data in transactions_data:
            try:
                transaction_id = data.get("id")
                if not transaction_id:
                    result.add_error({
                        "data": data,
                        "error": "缺少交易ID",
                        "timestamp": datetime.now().isoformat()
                    })
                    continue

                # 查找并删除交易
                transaction = await db.scalar(
                    select(Transaction).where(
                        and_(Transaction.id == transaction_id, Transaction.user_id == user_id)
                    )
                )

                if not transaction:
                    result.add_error({
                        "data": data,
                        "error": "交易不存在",
                        "timestamp": datetime.now().isoformat()
                    })
                    continue

                await db.delete(transaction)

                result.add_success({
                    "action": "deleted",
                    "transaction_id": transaction_id,
                    "amount": transaction.amount
                })

            except Exception as e:
                result.add_error({
                    "data": data,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })

    def _validate_transaction_data(self, data: Dict[str, Any]) -> bool:
        """验证交易数据"""
        required_fields = ["amount", "transaction_type"]

        for field in required_fields:
            if field not in data or data[field] is None:
                return False

        # 验证金额
        try:
            amount = float(data["amount"])
            if amount <= 0:
                return False
        except (ValueError, TypeError):
            return False

        # 验证交易类型
        if data["transaction_type"] not in ["income", "expense", "transfer"]:
            return False

        # 验证日期格式
        if "transaction_date" in data:
            try:
                datetime.fromisoformat(data["transaction_date"])
            except (ValueError, TypeError):
                return False

        return True

    async def batch_categorize_transactions(
        self,
        job_id: str,
        transaction_ids: List[str],
        category_id: str,
        user_id: str
    ) -> BatchResult:
        """批量分类交易"""
        async with self._processing_semaphore:
            job = await self.get_batch_job(job_id)
            if not job:
                raise ValueError(f"批处理任务不存在: {job_id}")

            job.status = BatchStatus.PROCESSING
            job.started_at = datetime.now()

            result = BatchResult()
            result.total_count = len(transaction_ids)

            try:
                async for db in get_db():
                    # 验证分类是否存在
                    category = await db.scalar(
                        select(Category).where(
                            and_(Category.id == category_id, Category.user_id == user_id)
                        )
                    )

                    if not category:
                        await self.fail_job(job_id, f"分类不存在: {category_id}")
                        return result

                    # 分批处理
                    for i in range(0, len(transaction_ids), self.batch_size):
                        batch_ids = transaction_ids[i:i + self.batch_size]

                        # 批量更新
                        update_result = await db.execute(
                            update(Transaction)
                            .where(
                                and_(
                                    Transaction.id.in_(batch_ids),
                                    Transaction.user_id == user_id
                                )
                            )
                            .values(
                                category_id=category_id,
                                updated_at=datetime.now()
                            )
                        )

                        updated_count = update_result.rowcount

                        for j, tid in enumerate(batch_ids):
                            if j < updated_count:
                                result.add_success({
                                    "action": "categorized",
                                    "transaction_id": tid,
                                    "category_id": category_id
                                })
                            else:
                                result.add_error({
                                    "transaction_id": tid,
                                    "error": "交易不存在或无权限",
                                    "timestamp": datetime.now().isoformat()
                                })

                        # 更新进度
                        await self.update_job_progress(job_id, min(i + self.batch_size, len(transaction_ids)))

                        # 提交批次
                        await db.commit()

                await self.complete_job(job_id, result)
                return result

            except Exception as e:
                await self.fail_job(job_id, str(e))
                raise


# 全局批处理器实例
batch_processor = BatchProcessor()
