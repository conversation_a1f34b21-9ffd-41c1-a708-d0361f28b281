"""
性能监控中间件
自动收集HTTP请求的性能指标
"""

import time
import asyncio
from typing import Callable, Optional
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from loguru import logger
import psutil
from ..utils.performance_monitor import performance_monitor
from ..utils.metrics_collector import metrics_collector



class PerformanceMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""
    
    def __init__(self, app: ASGIApp, excluded_paths: Optional[list] = None):
        super().__init__(app)
        self.excluded_paths = excluded_paths or [
            "/docs", "/redoc", "/openapi.json", "/favicon.ico", "/health", "/metrics"
        ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 检查是否需要跳过监控
        if any(request.url.path.startswith(path) for path in self.excluded_paths):
            return await call_next(request)
        
        # 记录开始时间
        start_time = time.time()
        
        # 获取用户信息（如果有）
        user_id = None
        try:
            # 从请求头中提取token
            auth_header = request.headers.get("Authorization")
            if auth_header and auth_header.startswith("Bearer "):
                from ..auth.jwt_handler import jwt_handler
                token = auth_header.split(" ")[1]
                payload = jwt_handler.verify_token(token)
                if payload:
                    user_id = payload.get("sub")
        except:
            pass
        
        # 执行请求
        response = await call_next(request)
        
        # 计算响应时间
        response_time = time.time() - start_time
        
        # 记录性能数据
        await self._record_performance_data(
            request, response, response_time, user_id
        )
        
        return response
    
    async def _record_performance_data(self, request: Request, response: Response, 
                                     response_time: float, user_id: Optional[str]):
        """记录性能数据"""
        try:
            endpoint = f"{request.method} {request.url.path}"
            status_code = response.status_code
            
            # 记录到性能监控器
            await performance_monitor.record_request(
                endpoint=endpoint,
                response_time=response_time,
                status_code=status_code,
                user_id=user_id
            )
            
            # 记录到指标收集器
            metrics_collector.http_requests_total.inc(
                method=request.method,
                endpoint=request.url.path,
                status=str(status_code)
            )
            
            metrics_collector.http_request_duration.observe(
                response_time,
                method=request.method,
                endpoint=request.url.path
            )
            
        except Exception as e:
            logger.error(f"记录性能数据失败: {e}")


class SystemMetricsMiddleware(BaseHTTPMiddleware):
    """系统指标监控中间件"""
    
    def __init__(self, app: ASGIApp, update_interval: int = 30):
        super().__init__(app)
        self.update_interval = update_interval
        self.last_update = 0
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 定期更新系统指标
        current_time = time.time()
        if current_time - self.last_update > self.update_interval:
            await self._update_system_metrics()
            self.last_update = current_time
        
        return await call_next(request)
    
    async def _update_system_metrics(self):
        """更新系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=None)
            metrics_collector.cpu_usage.set(cpu_percent)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            metrics_collector.memory_usage.set(memory.percent)
            
            # 活跃连接数
            connections = len(psutil.net_connections())
            metrics_collector.active_connections.set(connections)
            
        except Exception as e:
            logger.error(f"更新系统指标失败: {e}")


class DatabaseMetricsMiddleware(BaseHTTPMiddleware):
    """数据库指标监控中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.active_queries = 0
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 检查是否是数据库相关的请求
        if self._is_database_request(request):
            start_time = time.time()
            self.active_queries += 1
            
            try:
                response = await call_next(request)
                
                # 记录查询时间
                query_time = time.time() - start_time
                operation = self._get_operation_type(request)
                
                metrics_collector.database_query_duration.observe(
                    query_time,
                    operation=operation
                )
                
                return response
            finally:
                self.active_queries -= 1
                metrics_collector.database_connections.set(self.active_queries)
        else:
            return await call_next(request)
    
    def _is_database_request(self, request: Request) -> bool:
        """判断是否是数据库相关请求"""
        db_endpoints = [
            "/api/v1/transactions",
            "/api/v1/accounts",
            "/api/v1/budgets",
            "/api/v1/reports"
        ]
        return any(request.url.path.startswith(endpoint) for endpoint in db_endpoints)
    
    def _get_operation_type(self, request: Request) -> str:
        """获取操作类型"""
        if request.method == "GET":
            return "select"
        elif request.method == "POST":
            return "insert"
        elif request.method == "PUT" or request.method == "PATCH":
            return "update"
        elif request.method == "DELETE":
            return "delete"
        else:
            return "unknown"


class CacheMetricsMiddleware(BaseHTTPMiddleware):
    """缓存指标监控中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # 检查响应头中的缓存信息
        cache_status = response.headers.get("X-Cache-Status")
        if cache_status:
            operation = "get"
            result = "hit" if cache_status == "HIT" else "miss"
            
            metrics_collector.cache_operations_total.inc(
                operation=operation,
                result=result
            )
            
            # 同时记录到性能监控器
            await performance_monitor.record_cache_hit(result == "hit")
        
        return response


class AIMetricsMiddleware(BaseHTTPMiddleware):
    """AI指标监控中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 检查是否是AI相关请求
        if self._is_ai_request(request):
            start_time = time.time()
            
            response = await call_next(request)
            
            # 记录AI请求指标
            ai_time = time.time() - start_time
            provider, model = self._extract_ai_info(request)
            status = "success" if response.status_code < 400 else "error"
            
            metrics_collector.ai_requests_total.inc(
                provider=provider,
                model=model,
                status=status
            )
            
            metrics_collector.ai_request_duration.observe(
                ai_time,
                provider=provider,
                model=model
            )
            
            return response
        else:
            return await call_next(request)
    
    def _is_ai_request(self, request: Request) -> bool:
        """判断是否是AI相关请求"""
        ai_endpoints = [
            "/api/v1/ai",
            "/api/v1/ai-categorization"
        ]
        return any(request.url.path.startswith(endpoint) for endpoint in ai_endpoints)
    
    def _extract_ai_info(self, request: Request) -> tuple:
        """提取AI提供商和模型信息"""
        # 从请求中提取AI信息，这里简化实现
        provider = "unknown"
        model = "unknown"
        
        # 可以从请求体或查询参数中提取
        if hasattr(request, 'json'):
            try:
                body = request.json()
                provider = body.get('provider', 'unknown')
                model = body.get('model', 'unknown')
            except:
                pass
        
        return provider, model


class ErrorTrackingMiddleware(BaseHTTPMiddleware):
    """错误跟踪中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            response = await call_next(request)
            
            # 记录错误状态码
            if response.status_code >= 400:
                await self._record_error(request, response)
            
            return response
            
        except Exception as e:
            # 记录未捕获的异常
            await self._record_exception(request, e)
            
            # 返回500错误响应
            return JSONResponse(
                status_code=500,
                content={
                    "error": True,
                    "message": "Internal server error",
                    "status_code": 500
                }
            )
    
    async def _record_error(self, request: Request, response: Response):
        """记录错误"""
        try:
            endpoint = f"{request.method} {request.url.path}"
            logger.warning(f"HTTP错误: {response.status_code} - {endpoint}")
            
            # 可以在这里添加更多错误跟踪逻辑
            # 比如发送到错误监控服务
            
        except Exception as e:
            logger.error(f"记录错误失败: {e}")
    
    async def _record_exception(self, request: Request, exception: Exception):
        """记录异常"""
        try:
            endpoint = f"{request.method} {request.url.path}"
            logger.error(f"未捕获异常: {endpoint} - {str(exception)}")
            
            # 可以在这里添加更多异常跟踪逻辑
            
        except Exception as e:
            logger.error(f"记录异常失败: {e}")


class HealthCheckMiddleware(BaseHTTPMiddleware):
    """健康检查中间件"""
    
    def __init__(self, app: ASGIApp, health_check_path: str = "/health"):
        super().__init__(app)
        self.health_check_path = health_check_path
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        if request.url.path == self.health_check_path:
            # 执行健康检查
            health_status = await self._perform_health_check()
            
            status_code = 200 if health_status["status"] == "healthy" else 503
            
            return JSONResponse(
                status_code=status_code,
                content=health_status
            )
        
        return await call_next(request)
    
    async def _perform_health_check(self) -> dict:
        """执行健康检查"""
        try:
            system_health = await performance_monitor.get_system_health()
            
            return {
                "status": system_health.status,
                "timestamp": time.time(),
                "checks": {
                    "cpu": {
                        "status": "ok" if system_health.cpu_percent < 80 else "warning",
                        "value": system_health.cpu_percent
                    },
                    "memory": {
                        "status": "ok" if system_health.memory_percent < 80 else "warning",
                        "value": system_health.memory_percent
                    },
                    "response_time": {
                        "status": "ok" if system_health.response_time_avg < 1.0 else "warning",
                        "value": system_health.response_time_avg
                    },
                    "error_rate": {
                        "status": "ok" if system_health.error_rate < 5.0 else "warning",
                        "value": system_health.error_rate
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                "status": "unhealthy",
                "timestamp": time.time(),
                "error": str(e)
            }
