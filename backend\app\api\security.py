"""
安全管理API
提供密钥轮换、审计日志、安全监控等功能的API接口
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional
from fastapi import APIRouter, HTTPException, Depends, status, Request, Query
from pydantic import BaseModel, Field

from ..auth.dependencies import get_current_user
from ..models.user import User
from ..utils.key_rotation import get_key_rotation_manager, RotationReason
from ..utils.audit_logger import get_audit_logger, AuditEventType, AuditLevel
from ..utils.security_monitor import get_security_monitor
from ..utils.access_control import get_access_control_manager, Permission, Role
from ..middleware.i18n import t_request

router = APIRouter(prefix="/security", tags=["安全管理"])

# Pydantic模型
class KeyRotationRequest(BaseModel):
    """密钥轮换请求"""
    provider: str = Field(..., description="AI服务提供商")
    reason: str = Field(default="manual", description="轮换原因")
    delay_hours: int = Field(default=0, description="延迟小时数")
    metadata: Optional[Dict] = Field(default=None, description="附加元数据")


class SecurityAlertResponse(BaseModel):
    """安全警报响应"""
    id: str
    timestamp: str
    event_type: str
    threat_level: str
    source_ip: Optional[str]
    user_id: Optional[str]
    description: str
    details: Dict
    actions_taken: List[str]
    resolved: bool


class AuditSearchRequest(BaseModel):
    """审计日志搜索请求"""
    start_time: Optional[datetime] = Field(default=None, description="开始时间")
    end_time: Optional[datetime] = Field(default=None, description="结束时间")
    event_type: Optional[str] = Field(default=None, description="事件类型")
    user_id: Optional[str] = Field(default=None, description="用户ID")
    level: Optional[str] = Field(default=None, description="审计级别")
    limit: int = Field(default=100, description="返回数量限制")


class RoleAssignmentRequest(BaseModel):
    """角色分配请求"""
    user_id: str = Field(..., description="目标用户ID")
    role: str = Field(..., description="角色名称")
    expires_at: Optional[datetime] = Field(default=None, description="过期时间")


# 密钥轮换相关API
@router.post("/key-rotation/schedule")
async def schedule_key_rotation(
    request: Request,
    rotation_request: KeyRotationRequest,
    current_user: User = Depends(get_current_user)
):
    """安排密钥轮换"""
    try:
        # 检查权限
        access_control = get_access_control_manager()
        if not await access_control.has_permission(current_user.id, Permission.API_KEY_ROTATE):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=t_request(request, "errors.permission_denied")
            )
        
        # 验证轮换原因
        try:
            reason = RotationReason(rotation_request.reason)
        except ValueError:
            reason = RotationReason.MANUAL
        
        # 安排轮换
        key_rotation_manager = get_key_rotation_manager()
        rotation_id = await key_rotation_manager.schedule_rotation(
            user_id=current_user.id,
            provider=rotation_request.provider,
            reason=reason,
            delay_hours=rotation_request.delay_hours,
            metadata=rotation_request.metadata
        )
        
        return {
            "success": True,
            "message": t_request(request, "security.key_rotation_scheduled"),
            "data": {
                "rotation_id": rotation_id,
                "provider": rotation_request.provider,
                "scheduled_time": (
                    datetime.now() + timedelta(hours=rotation_request.delay_hours)
                ).isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"{t_request(request, 'security.key_rotation_failed')}: {str(e)}"
        )


@router.get("/key-rotation/{rotation_id}/status")
async def get_rotation_status(
    request: Request,
    rotation_id: str,
    current_user: User = Depends(get_current_user)
):
    """获取密钥轮换状态"""
    try:
        # 检查权限
        access_control = get_access_control_manager()
        if not await access_control.has_permission(current_user.id, Permission.API_KEY_READ):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=t_request(request, "errors.permission_denied")
            )
        
        key_rotation_manager = get_key_rotation_manager()
        rotation_record = await key_rotation_manager.get_rotation_status(rotation_id)
        
        if not rotation_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=t_request(request, "errors.not_found")
            )
        
        # 检查是否为用户自己的轮换记录
        if rotation_record.user_id != current_user.id:
            # 检查是否有管理员权限
            if not await access_control.has_permission(current_user.id, Permission.SYSTEM_ADMIN):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=t_request(request, "errors.permission_denied")
                )
        
        return {
            "success": True,
            "data": rotation_record.to_dict()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.delete("/key-rotation/{rotation_id}")
async def cancel_key_rotation(
    request: Request,
    rotation_id: str,
    current_user: User = Depends(get_current_user)
):
    """取消密钥轮换"""
    try:
        # 检查权限
        access_control = get_access_control_manager()
        if not await access_control.has_permission(current_user.id, Permission.API_KEY_ROTATE):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=t_request(request, "errors.permission_denied")
            )
        
        key_rotation_manager = get_key_rotation_manager()
        success = await key_rotation_manager.cancel_rotation(rotation_id, current_user.id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=t_request(request, "security.key_rotation_cancel_failed")
            )
        
        return {
            "success": True,
            "message": t_request(request, "security.key_rotation_cancelled")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/key-rotation/history")
async def get_rotation_history(
    request: Request,
    provider: Optional[str] = Query(None, description="服务提供商"),
    limit: int = Query(50, description="返回数量限制"),
    current_user: User = Depends(get_current_user)
):
    """获取密钥轮换历史"""
    try:
        # 检查权限
        access_control = get_access_control_manager()
        if not await access_control.has_permission(current_user.id, Permission.API_KEY_READ):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=t_request(request, "errors.permission_denied")
            )
        
        key_rotation_manager = get_key_rotation_manager()
        history = await key_rotation_manager.get_rotation_history(
            user_id=current_user.id,
            provider=provider,
            limit=limit
        )
        
        return {
            "success": True,
            "data": [record.to_dict() for record in history]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


# 审计日志相关API
@router.get("/audit/recent")
async def get_recent_audit_events(
    request: Request,
    event_type: Optional[str] = Query(None, description="事件类型"),
    limit: int = Query(100, description="返回数量限制"),
    current_user: User = Depends(get_current_user)
):
    """获取最近的审计事件"""
    try:
        # 检查权限
        access_control = get_access_control_manager()
        if not await access_control.has_permission(current_user.id, Permission.SYSTEM_AUDIT):
            # 普通用户只能查看自己的审计事件
            user_id = current_user.id
        else:
            # 管理员可以查看所有事件
            user_id = None
        
        audit_logger = get_audit_logger()
        
        # 转换事件类型
        audit_event_type = None
        if event_type:
            try:
                audit_event_type = AuditEventType(event_type)
            except ValueError:
                pass
        
        events = await audit_logger.get_recent_events(
            user_id=user_id,
            event_type=audit_event_type,
            limit=limit
        )
        
        return {
            "success": True,
            "data": events
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.post("/audit/search")
async def search_audit_events(
    request: Request,
    search_request: AuditSearchRequest,
    current_user: User = Depends(get_current_user)
):
    """搜索审计事件"""
    try:
        # 检查权限
        access_control = get_access_control_manager()
        if not await access_control.has_permission(current_user.id, Permission.SYSTEM_AUDIT):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=t_request(request, "errors.permission_denied")
            )
        
        audit_logger = get_audit_logger()
        
        # 转换参数
        event_type = None
        if search_request.event_type:
            try:
                event_type = AuditEventType(search_request.event_type)
            except ValueError:
                pass
        
        level = None
        if search_request.level:
            try:
                level = AuditLevel(search_request.level)
            except ValueError:
                pass
        
        events = await audit_logger.search_events(
            start_time=search_request.start_time,
            end_time=search_request.end_time,
            user_id=search_request.user_id,
            event_type=event_type,
            level=level,
            limit=search_request.limit
        )
        
        return {
            "success": True,
            "data": events
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


# 安全监控相关API
@router.get("/monitor/status")
async def get_security_status(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """获取安全监控状态"""
    try:
        # 检查权限
        access_control = get_access_control_manager()
        if not await access_control.has_permission(current_user.id, Permission.SYSTEM_AUDIT):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=t_request(request, "errors.permission_denied")
            )
        
        security_monitor = get_security_monitor()
        status_info = await security_monitor.get_security_status()
        
        return {
            "success": True,
            "data": status_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/monitor/alerts")
async def get_security_alerts(
    request: Request,
    resolved: Optional[bool] = Query(None, description="是否已解决"),
    threat_level: Optional[str] = Query(None, description="威胁级别"),
    limit: int = Query(100, description="返回数量限制"),
    current_user: User = Depends(get_current_user)
):
    """获取安全警报"""
    try:
        # 检查权限
        access_control = get_access_control_manager()
        if not await access_control.has_permission(current_user.id, Permission.SYSTEM_AUDIT):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=t_request(request, "errors.permission_denied")
            )
        
        security_monitor = get_security_monitor()
        
        # 获取所有警报
        all_alerts = list(security_monitor.active_alerts.values())
        
        # 过滤警报
        filtered_alerts = []
        for alert in all_alerts:
            if resolved is not None and alert.resolved != resolved:
                continue
            if threat_level and alert.threat_level.value != threat_level:
                continue
            filtered_alerts.append(alert)
        
        # 按时间倒序排列
        filtered_alerts.sort(key=lambda x: x.timestamp, reverse=True)
        
        # 限制数量
        filtered_alerts = filtered_alerts[:limit]
        
        return {
            "success": True,
            "data": [alert.to_dict() for alert in filtered_alerts]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


# 访问控制相关API
@router.post("/access/assign-role")
async def assign_user_role(
    request: Request,
    assignment_request: RoleAssignmentRequest,
    current_user: User = Depends(get_current_user)
):
    """分配用户角色"""
    try:
        # 检查权限
        access_control = get_access_control_manager()
        if not await access_control.has_permission(current_user.id, Permission.USER_WRITE):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=t_request(request, "errors.permission_denied")
            )
        
        # 验证角色
        try:
            role = Role(assignment_request.role)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=t_request(request, "errors.invalid_role")
            )
        
        # 分配角色
        success = await access_control.assign_role(
            user_id=assignment_request.user_id,
            role=role,
            granted_by=current_user.id,
            expires_at=assignment_request.expires_at
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=t_request(request, "security.role_assignment_failed")
            )
        
        return {
            "success": True,
            "message": t_request(request, "security.role_assigned_successfully")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/access/permissions/{user_id}")
async def get_user_permissions(
    request: Request,
    user_id: str,
    current_user: User = Depends(get_current_user)
):
    """获取用户权限"""
    try:
        # 检查权限（只能查看自己的权限或管理员权限）
        access_control = get_access_control_manager()
        if (user_id != current_user.id and 
            not await access_control.has_permission(current_user.id, Permission.SYSTEM_AUDIT)):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=t_request(request, "errors.permission_denied")
            )
        
        # 获取权限摘要
        summary = await access_control.get_access_summary(user_id)
        
        return {
            "success": True,
            "data": summary
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/health")
async def security_health_check(request: Request):
    """安全系统健康检查"""
    try:
        # 检查各个安全组件的状态
        key_rotation_manager = get_key_rotation_manager()
        audit_logger = get_audit_logger()
        security_monitor = get_security_monitor()
        access_control = get_access_control_manager()
        
        health_status = {
            "key_rotation": "healthy",
            "audit_logging": "healthy",
            "security_monitoring": "healthy",
            "access_control": "healthy",
            "overall_status": "healthy",
            "timestamp": datetime.now().isoformat()
        }
        
        return {
            "success": True,
            "data": health_status
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"安全系统健康检查失败: {str(e)}",
            "data": {
                "overall_status": "unhealthy",
                "timestamp": datetime.now().isoformat()
            }
        }
