"""
指标收集器
收集和聚合各种系统指标，支持Prometheus格式导出
"""

import time
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass
from enum import Enum
import json
from loguru import logger
import threading
from concurrent.futures import ThreadPoolExecutor


class MetricUnit(Enum):
    """指标单位"""
    SECONDS = "seconds"
    MILLISECONDS = "milliseconds"
    BYTES = "bytes"
    PERCENT = "percent"
    COUNT = "count"
    RATE = "rate"


@dataclass
class MetricSample:
    """指标样本"""
    name: str
    value: float
    timestamp: float
    labels: Dict[str, str]
    unit: MetricUnit


class Counter:
    """计数器指标"""
    
    def __init__(self, name: str, description: str, labels: Optional[List[str]] = None):
        self.name = name
        self.description = description
        self.labels = labels or []
        self._values: Dict[Tuple[str, ...], float] = defaultdict(float)
        self._lock = threading.Lock()
    
    def inc(self, amount: float = 1.0, **label_values):
        """增加计数"""
        label_key = tuple(label_values.get(label, "") for label in self.labels)
        with self._lock:
            self._values[label_key] += amount
    
    def get_samples(self) -> List[MetricSample]:
        """获取样本数据"""
        samples = []
        with self._lock:
            for label_key, value in self._values.items():
                labels = dict(zip(self.labels, label_key))
                samples.append(MetricSample(
                    name=self.name,
                    value=value,
                    timestamp=time.time(),
                    labels=labels,
                    unit=MetricUnit.COUNT
                ))
        return samples


class Gauge:
    """仪表盘指标"""
    
    def __init__(self, name: str, description: str, labels: Optional[List[str]] = None):
        self.name = name
        self.description = description
        self.labels = labels or []
        self._values: Dict[Tuple[str, ...], float] = {}
        self._lock = threading.Lock()
    
    def set(self, value: float, **label_values):
        """设置值"""
        label_key = tuple(label_values.get(label, "") for label in self.labels)
        with self._lock:
            self._values[label_key] = value
    
    def inc(self, amount: float = 1.0, **label_values):
        """增加值"""
        label_key = tuple(label_values.get(label, "") for label in self.labels)
        with self._lock:
            self._values[label_key] = self._values.get(label_key, 0) + amount
    
    def dec(self, amount: float = 1.0, **label_values):
        """减少值"""
        self.inc(-amount, **label_values)
    
    def get_samples(self) -> List[MetricSample]:
        """获取样本数据"""
        samples = []
        with self._lock:
            for label_key, value in self._values.items():
                labels = dict(zip(self.labels, label_key))
                samples.append(MetricSample(
                    name=self.name,
                    value=value,
                    timestamp=time.time(),
                    labels=labels,
                    unit=MetricUnit.COUNT
                ))
        return samples


class Histogram:
    """直方图指标"""
    
    def __init__(self, name: str, description: str, buckets: Optional[List[float]] = None,
                 labels: Optional[List[str]] = None):
        self.name = name
        self.description = description
        self.labels = labels or []
        self.buckets = buckets or [0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1.0, 2.5, 5.0, 7.5, 10.0]
        self._buckets: Dict[Tuple[str, ...], Dict[float, int]] = defaultdict(lambda: defaultdict(int))
        self._counts: Dict[Tuple[str, ...], int] = defaultdict(int)
        self._sums: Dict[Tuple[str, ...], float] = defaultdict(float)
        self._lock = threading.Lock()
    
    def observe(self, value: float, **label_values):
        """观察值"""
        label_key = tuple(label_values.get(label, "") for label in self.labels)
        with self._lock:
            self._counts[label_key] += 1
            self._sums[label_key] += value
            
            for bucket in self.buckets:
                if value <= bucket:
                    self._buckets[label_key][bucket] += 1
    
    def get_samples(self) -> List[MetricSample]:
        """获取样本数据"""
        samples = []
        with self._lock:
            for label_key in self._counts:
                labels = dict(zip(self.labels, label_key))
                
                # 添加桶样本
                for bucket in self.buckets:
                    bucket_labels = labels.copy()
                    bucket_labels['le'] = str(bucket)
                    samples.append(MetricSample(
                        name=f"{self.name}_bucket",
                        value=self._buckets[label_key][bucket],
                        timestamp=time.time(),
                        labels=bucket_labels,
                        unit=MetricUnit.COUNT
                    ))
                
                # 添加计数样本
                samples.append(MetricSample(
                    name=f"{self.name}_count",
                    value=self._counts[label_key],
                    timestamp=time.time(),
                    labels=labels,
                    unit=MetricUnit.COUNT
                ))
                
                # 添加总和样本
                samples.append(MetricSample(
                    name=f"{self.name}_sum",
                    value=self._sums[label_key],
                    timestamp=time.time(),
                    labels=labels,
                    unit=MetricUnit.SECONDS
                ))
        
        return samples


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self.metrics: Dict[str, Any] = {}
        self._lock = threading.Lock()
        
        # 初始化默认指标
        self._init_default_metrics()
    
    def _init_default_metrics(self):
        """初始化默认指标"""
        # HTTP请求指标
        self.http_requests_total = Counter(
            "http_requests_total",
            "Total HTTP requests",
            ["method", "endpoint", "status"]
        )
        
        self.http_request_duration = Histogram(
            "http_request_duration_seconds",
            "HTTP request duration in seconds",
            labels=["method", "endpoint"]
        )
        
        # 系统指标
        self.cpu_usage = Gauge(
            "cpu_usage_percent",
            "CPU usage percentage"
        )
        
        self.memory_usage = Gauge(
            "memory_usage_percent",
            "Memory usage percentage"
        )
        
        self.active_connections = Gauge(
            "active_connections",
            "Number of active connections"
        )
        
        # 数据库指标
        self.database_connections = Gauge(
            "database_connections",
            "Number of database connections"
        )
        
        self.database_query_duration = Histogram(
            "database_query_duration_seconds",
            "Database query duration in seconds",
            labels=["operation"]
        )
        
        # 缓存指标
        self.cache_operations_total = Counter(
            "cache_operations_total",
            "Total cache operations",
            ["operation", "result"]
        )
        
        # AI指标
        self.ai_requests_total = Counter(
            "ai_requests_total",
            "Total AI requests",
            ["provider", "model", "status"]
        )
        
        self.ai_request_duration = Histogram(
            "ai_request_duration_seconds",
            "AI request duration in seconds",
            labels=["provider", "model"]
        )
        
        # 注册所有指标
        self.register_metric("http_requests_total", self.http_requests_total)
        self.register_metric("http_request_duration", self.http_request_duration)
        self.register_metric("cpu_usage", self.cpu_usage)
        self.register_metric("memory_usage", self.memory_usage)
        self.register_metric("active_connections", self.active_connections)
        self.register_metric("database_connections", self.database_connections)
        self.register_metric("database_query_duration", self.database_query_duration)
        self.register_metric("cache_operations_total", self.cache_operations_total)
        self.register_metric("ai_requests_total", self.ai_requests_total)
        self.register_metric("ai_request_duration", self.ai_request_duration)
    
    def register_metric(self, name: str, metric: Any):
        """注册指标"""
        with self._lock:
            self.metrics[name] = metric
    
    def get_metric(self, name: str) -> Optional[Any]:
        """获取指标"""
        with self._lock:
            return self.metrics.get(name)
    
    def collect_all_samples(self) -> List[MetricSample]:
        """收集所有样本"""
        all_samples = []
        with self._lock:
            for metric in self.metrics.values():
                if hasattr(metric, 'get_samples'):
                    all_samples.extend(metric.get_samples())
        return all_samples
    
    def export_prometheus_format(self) -> str:
        """导出Prometheus格式"""
        lines = []
        samples = self.collect_all_samples()
        
        # 按指标名称分组
        metrics_by_name = defaultdict(list)
        for sample in samples:
            metrics_by_name[sample.name].append(sample)
        
        for metric_name, metric_samples in metrics_by_name.items():
            # 添加HELP和TYPE注释
            lines.append(f"# HELP {metric_name} {metric_name}")
            
            # 确定指标类型
            if metric_name.endswith('_total'):
                lines.append(f"# TYPE {metric_name} counter")
            elif metric_name.endswith('_bucket') or metric_name.endswith('_count') or metric_name.endswith('_sum'):
                base_name = metric_name.rsplit('_', 1)[0]
                lines.append(f"# TYPE {base_name} histogram")
            else:
                lines.append(f"# TYPE {metric_name} gauge")
            
            # 添加样本数据
            for sample in metric_samples:
                labels_str = ""
                if sample.labels:
                    label_pairs = [f'{k}="{v}"' for k, v in sample.labels.items()]
                    labels_str = "{" + ",".join(label_pairs) + "}"
                
                lines.append(f"{sample.name}{labels_str} {sample.value} {int(sample.timestamp * 1000)}")
            
            lines.append("")  # 空行分隔
        
        return "\n".join(lines)
    
    def export_json_format(self) -> Dict[str, Any]:
        """导出JSON格式"""
        samples = self.collect_all_samples()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "metrics": [
                {
                    "name": sample.name,
                    "value": sample.value,
                    "timestamp": sample.timestamp,
                    "labels": sample.labels,
                    "unit": sample.unit.value
                }
                for sample in samples
            ]
        }
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取汇总统计"""
        samples = self.collect_all_samples()
        
        stats = {
            "total_metrics": len(self.metrics),
            "total_samples": len(samples),
            "metrics_by_type": defaultdict(int),
            "last_update": datetime.now().isoformat()
        }
        
        for sample in samples:
            if sample.name.endswith('_total'):
                stats["metrics_by_type"]["counter"] += 1
            elif sample.name.endswith('_bucket') or sample.name.endswith('_count') or sample.name.endswith('_sum'):
                stats["metrics_by_type"]["histogram"] += 1
            else:
                stats["metrics_by_type"]["gauge"] += 1
        
        return dict(stats)


# 全局指标收集器实例
metrics_collector = MetricsCollector()
