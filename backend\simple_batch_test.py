"""
简单的批处理系统测试
"""

import asyncio
import httpx
import json
from loguru import logger

async def main():
    """主测试函数"""
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            # 测试用户数据
            user_data = {
                'email': '<EMAIL>', 
                'password': 'testpassword123'
            }
            
            logger.info("开始测试批处理系统...")
            
            # 1. 尝试注册用户（可能已存在）
            logger.info("1. 注册用户...")
            register_response = await client.post(
                'http://localhost:8003/api/v1/auth/register', 
                json=user_data
            )
            logger.info(f"注册响应: {register_response.status_code}")
            
            # 2. 登录获取token
            logger.info("2. 用户登录...")
            login_response = await client.post(
                'http://localhost:8003/api/v1/auth/login', 
                json=user_data
            )
            logger.info(f"登录响应: {login_response.status_code}")
            
            if login_response.status_code != 200:
                logger.error(f"登录失败: {login_response.text}")
                return
                
            # 获取token
            token_data = login_response.json()
            token = token_data.get('access_token')
            if not token:
                logger.error("未获取到访问令牌")
                return
                
            headers = {'Authorization': f'Bearer {token}'}
            logger.info("登录成功，获取到访问令牌")
            
            # 3. 测试批处理交易创建
            logger.info("3. 测试批处理交易创建...")
            batch_data = {
                'transactions': [
                    {
                        'amount': 100.0,
                        'transaction_type': 'expense',
                        'description': '测试交易1',
                        'category_name': '餐饮',
                        'account_name': '现金'
                    },
                    {
                        'amount': 50.0,
                        'transaction_type': 'income',
                        'description': '测试收入1',
                        'category_name': '工资',
                        'account_name': '银行卡'
                    }
                ],
                'operation_type': 'CREATE'
            }
            
            batch_response = await client.post(
                'http://localhost:8003/api/v1/batch/transactions',
                json=batch_data,
                headers=headers
            )
            
            logger.info(f"批处理响应状态: {batch_response.status_code}")
            logger.info(f"批处理响应内容: {batch_response.text}")
            
            if batch_response.status_code == 200:
                result = batch_response.json()
                job_id = result.get('job_id')
                
                if job_id:
                    logger.info(f"批处理任务已创建，任务ID: {job_id}")
                    
                    # 4. 查询任务状态
                    logger.info("4. 查询任务状态...")
                    for i in range(5):  # 最多查询5次
                        await asyncio.sleep(2)  # 等待2秒
                        
                        status_response = await client.get(
                            f'http://localhost:8003/api/v1/batch/jobs/{job_id}',
                            headers=headers
                        )
                        
                        if status_response.status_code == 200:
                            status_data = status_response.json()
                            logger.info(f"任务状态: {status_data.get('status')}")
                            logger.info(f"进度: {status_data.get('progress', 0)}%")
                            
                            if status_data.get('status') in ['completed', 'failed']:
                                logger.info(f"任务完成: {status_data}")
                                break
                        else:
                            logger.error(f"查询状态失败: {status_response.text}")
                            break
                    
                    logger.info("批处理系统测试完成！")
                else:
                    logger.error("未获取到任务ID")
            else:
                logger.error(f"批处理请求失败: {batch_response.text}")
                
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
