{"common": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "loading": "Loading...", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "email": "Email", "password": "Password", "username": "Username", "login_success": "Login successful", "login_failed": "<PERSON><PERSON> failed", "register_success": "Registration successful", "register_failed": "Registration failed", "invalid_credentials": "Invalid username or password", "email_already_exists": "Email already exists", "username_already_exists": "Username already exists"}, "transaction": {"transaction": "Transaction", "transactions": "Transactions", "amount": "Amount", "description": "Description", "category": "Category", "date": "Date", "type": "Type", "income": "Income", "expense": "Expense", "add_transaction": "Add Transaction", "edit_transaction": "Edit Transaction", "delete_transaction": "Delete Transaction", "transaction_added": "Transaction added successfully", "transaction_updated": "Transaction updated successfully", "transaction_deleted": "Transaction deleted successfully"}, "category": {"category": "Category", "categories": "Categories", "category_name": "Category Name", "category_color": "Category Color", "category_type": "Category Type", "add_category": "Add Category", "edit_category": "Edit Category", "delete_category": "Delete Category", "category_added": "Category added successfully", "category_updated": "Category updated successfully", "category_deleted": "Category deleted successfully"}, "budget": {"budget": "Budget", "budgets": "Budgets", "budget_amount": "Budget Amount", "spent_amount": "Spent Amount", "remaining_amount": "Remaining Amount", "budget_period": "Budget Period", "monthly": "Monthly", "yearly": "Yearly", "add_budget": "Add Budget", "edit_budget": "Edit Budget", "delete_budget": "Delete Budget", "budget_exceeded": "Budget Exceeded", "budget_warning": "Budget Warning"}, "ai": {"ai_categorization": "AI Categorization", "ai_config": "AI Configuration", "api_key": "API Key", "model_name": "Model Name", "provider": "Provider", "categorize": "Categorize", "categorizing": "Categorizing...", "categorization_success": "Categorization successful", "categorization_failed": "Categorization failed", "invalid_api_key": "Invalid API key", "ai_service_unavailable": "AI service unavailable"}, "analytics": {"analytics": "Analytics", "spending_patterns": "Spending Patterns", "budget_recommendations": "Budget Recommendations", "financial_health": "Financial Health", "trends": "Trends", "insights": "Insights", "predictions": "Predictions", "comparison": "Comparison", "health_score": "Health Score", "excellent": "Excellent", "good": "Good", "average": "Average", "needs_improvement": "Needs Improvement"}, "errors": {"network_error": "Network Error", "server_error": "Server Error", "validation_error": "Validation Error", "permission_denied": "Permission Denied", "not_found": "Not Found", "rate_limit_exceeded": "Rate Limit Exceeded", "insufficient_data": "Insufficient Data", "operation_failed": "Operation Failed"}, "test": {"greeting": "Hello, {name}! Welcome to {app_name}."}}