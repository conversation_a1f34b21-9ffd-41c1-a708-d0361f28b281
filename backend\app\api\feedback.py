"""
用户反馈API
处理用户对AI分类结果的反馈
"""

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
import uuid
from datetime import datetime

from ..database.connection import get_db
from ..models.user import User
from ..models.user_feedback import UserFeedback, UserLearningPattern, CategoryAccuracy
from ..auth.dependencies import get_current_user
from ..services.feedback_learning import FeedbackLearningService
from ..middleware.rate_limit import feedback_limit, api_call_limit

router = APIRouter(prefix="/feedback", tags=["用户反馈"])


class FeedbackCreate(BaseModel):
    """创建反馈请求模型"""
    transaction_description: str = Field(..., description="交易描述")
    transaction_amount: float = Field(..., description="交易金额")
    transaction_type: str = Field(..., pattern="^(income|expense|transfer)$", description="交易类型")
    ai_suggested_category: str = Field(..., description="AI建议的分类")
    ai_confidence: float = Field(..., ge=0.0, le=1.0, description="AI置信度")
    ai_reasoning: Optional[str] = Field(None, description="AI分类理由")
    user_accepted: bool = Field(..., description="用户是否接受AI建议")
    user_corrected_category: Optional[str] = Field(None, description="用户修正的分类")
    user_comment: Optional[str] = Field(None, description="用户评论")


class FeedbackResponse(BaseModel):
    """反馈响应模型"""
    id: str
    message: str
    learning_applied: bool


@router.post("/submit", response_model=FeedbackResponse)
@feedback_limit
async def submit_feedback(
    feedback_data: FeedbackCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """提交用户反馈"""
    try:
        learning_service = FeedbackLearningService(db)
        
        # 记录反馈
        feedback = await learning_service.record_feedback(
            user_id=str(current_user.id),
            transaction_description=feedback_data.transaction_description,
            transaction_amount=feedback_data.transaction_amount,
            transaction_type=feedback_data.transaction_type,
            ai_suggested_category=feedback_data.ai_suggested_category,
            ai_confidence=feedback_data.ai_confidence,
            ai_reasoning=feedback_data.ai_reasoning,
            user_accepted=feedback_data.user_accepted,
            user_corrected_category=feedback_data.user_corrected_category,
            user_comment=feedback_data.user_comment
        )
        
        return FeedbackResponse(
            id=str(feedback.id),
            message="反馈提交成功，系统将学习您的偏好",
            learning_applied=not feedback_data.user_accepted
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"提交反馈失败: {str(e)}"
        )


@router.get("/history")
async def get_feedback_history(
    limit: int = 50,
    offset: int = 0,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户反馈历史"""
    try:
        result = await db.execute(
            select(UserFeedback)
            .where(UserFeedback.user_id == str(current_user.id))
            .order_by(UserFeedback.created_at.desc())
            .limit(limit)
            .offset(offset)
        )
        feedbacks = result.scalars().all()
        
        return JSONResponse(content={
            "feedbacks": [feedback.to_dict() for feedback in feedbacks],
            "total": len(feedbacks),
            "limit": limit,
            "offset": offset
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取反馈历史失败: {str(e)}"
        )


@router.get("/stats")
async def get_feedback_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户反馈统计"""
    try:
        learning_service = FeedbackLearningService(db)
        stats = await learning_service.get_user_feedback_stats(str(current_user.id))
        
        return JSONResponse(content=stats)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取反馈统计失败: {str(e)}"
        )


@router.get("/patterns")
async def get_learning_patterns(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户学习模式"""
    try:
        learning_service = FeedbackLearningService(db)
        patterns = await learning_service.get_user_patterns(str(current_user.id))
        
        return JSONResponse(content={
            "patterns": patterns,
            "total_patterns": sum(len(patterns[key]) for key in patterns)
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取学习模式失败: {str(e)}"
        )


@router.delete("/patterns/{pattern_id}")
async def delete_learning_pattern(
    pattern_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除学习模式"""
    try:
        # 查找模式
        result = await db.execute(
            select(UserLearningPattern).where(
                and_(
                    UserLearningPattern.id == pattern_id,
                    UserLearningPattern.user_id == str(current_user.id)
                )
            )
        )
        pattern = result.scalar_one_or_none()
        
        if not pattern:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="学习模式不存在"
            )
        
        await db.delete(pattern)
        await db.commit()
        
        return JSONResponse(content={
            "message": "学习模式删除成功",
            "pattern_id": pattern_id
        })
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除学习模式失败: {str(e)}"
        )


@router.get("/accuracy")
async def get_category_accuracy(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取分类准确性统计"""
    try:
        result = await db.execute(
            select(CategoryAccuracy)
            .where(CategoryAccuracy.user_id == str(current_user.id))
            .order_by(CategoryAccuracy.accuracy_rate.desc())
        )
        accuracies = result.scalars().all()
        
        return JSONResponse(content={
            "accuracies": [accuracy.to_dict() for accuracy in accuracies],
            "total_categories": len(accuracies),
            "overall_accuracy": sum(acc.accuracy_rate for acc in accuracies) / max(1, len(accuracies))
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取分类准确性失败: {str(e)}"
        )


@router.post("/batch-feedback")
async def submit_batch_feedback(
    feedbacks: List[FeedbackCreate],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """批量提交反馈"""
    try:
        learning_service = FeedbackLearningService(db)
        results = []
        
        for feedback_data in feedbacks:
            try:
                feedback = await learning_service.record_feedback(
                    user_id=str(current_user.id),
                    transaction_description=feedback_data.transaction_description,
                    transaction_amount=feedback_data.transaction_amount,
                    transaction_type=feedback_data.transaction_type,
                    ai_suggested_category=feedback_data.ai_suggested_category,
                    ai_confidence=feedback_data.ai_confidence,
                    ai_reasoning=feedback_data.ai_reasoning,
                    user_accepted=feedback_data.user_accepted,
                    user_corrected_category=feedback_data.user_corrected_category,
                    user_comment=feedback_data.user_comment
                )
                
                results.append({
                    "success": True,
                    "feedback_id": str(feedback.id),
                    "description": feedback_data.transaction_description[:50]
                })
                
            except Exception as e:
                results.append({
                    "success": False,
                    "error": str(e),
                    "description": feedback_data.transaction_description[:50]
                })
        
        successful_count = sum(1 for r in results if r["success"])
        
        return JSONResponse(content={
            "message": f"批量反馈处理完成，成功: {successful_count}/{len(feedbacks)}",
            "results": results,
            "successful_count": successful_count,
            "total_count": len(feedbacks)
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量提交反馈失败: {str(e)}"
        )


@router.post("/reset-learning")
async def reset_user_learning(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """重置用户学习数据"""
    try:
        # 删除学习模式
        await db.execute(
            select(UserLearningPattern).where(UserLearningPattern.user_id == str(current_user.id))
        )
        patterns_result = await db.execute(
            select(UserLearningPattern).where(UserLearningPattern.user_id == str(current_user.id))
        )
        patterns = patterns_result.scalars().all()
        
        for pattern in patterns:
            await db.delete(pattern)
        
        # 删除准确性统计
        accuracy_result = await db.execute(
            select(CategoryAccuracy).where(CategoryAccuracy.user_id == str(current_user.id))
        )
        accuracies = accuracy_result.scalars().all()
        
        for accuracy in accuracies:
            await db.delete(accuracy)
        
        await db.commit()
        
        # 清除缓存
        from ..utils.cache import get_cache_manager
        cache = await get_cache_manager()
        await cache.invalidate_user_cache(str(current_user.id))
        
        return JSONResponse(content={
            "message": "用户学习数据重置成功",
            "deleted_patterns": len(patterns),
            "deleted_accuracies": len(accuracies)
        })
        
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重置学习数据失败: {str(e)}"
        )
