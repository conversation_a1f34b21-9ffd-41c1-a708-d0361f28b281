"""
智能预算建议系统
基于历史数据和支出模式提供个性化预算建议
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import asyncio

from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from sqlalchemy.orm import selectinload

from ..models.transaction import Transaction
from ..models.category import Category
from ..models.budget import Budget
from ..models.user import User
from .spending_prediction_engine import SpendingPredictionEngine
from ..utils.cache import get_cache_manager
from ..utils.structured_logger import structured_logger, LogCategory


class BudgetStrategy(Enum):
    """预算策略"""
    CONSERVATIVE = "conservative"  # 保守型
    BALANCED = "balanced"         # 平衡型
    AGGRESSIVE = "aggressive"     # 激进型


@dataclass
class BudgetRecommendation:
    """预算建议"""
    category_name: str
    current_budget: float
    recommended_budget: float
    adjustment_percentage: float
    strategy: BudgetStrategy
    reasoning: List[str]
    confidence: float
    priority: str  # 'high', 'medium', 'low'
    expected_savings: float


@dataclass
class BudgetOptimization:
    """预算优化方案"""
    total_current_budget: float
    total_recommended_budget: float
    total_potential_savings: float
    optimization_score: float
    recommendations: List[BudgetRecommendation]
    risk_assessment: Dict[str, Any]
    implementation_plan: List[Dict[str, Any]]


class SmartBudgetAdvisor:
    """智能预算建议系统"""
    
    def __init__(self):
        self.prediction_engine = SpendingPredictionEngine()
        self.cache_ttl = 1800  # 30分钟缓存
        
        # 预算策略参数
        self.strategy_multipliers = {
            BudgetStrategy.CONSERVATIVE: {
                'safety_margin': 0.2,    # 20%安全边际
                'volatility_factor': 1.5, # 波动性调整因子
                'trend_sensitivity': 0.8   # 趋势敏感度
            },
            BudgetStrategy.BALANCED: {
                'safety_margin': 0.1,     # 10%安全边际
                'volatility_factor': 1.2,
                'trend_sensitivity': 1.0
            },
            BudgetStrategy.AGGRESSIVE: {
                'safety_margin': 0.05,    # 5%安全边际
                'volatility_factor': 1.0,
                'trend_sensitivity': 1.2
            }
        }
        
        # 分类重要性权重
        self.category_importance = {
            '住房': 0.9,     # 必需品
            '餐饮': 0.8,     # 基本需求
            '交通': 0.8,     # 基本需求
            '医疗': 0.9,     # 必需品
            '教育': 0.8,     # 重要投资
            '购物': 0.5,     # 可调整
            '娱乐': 0.3,     # 可调整
            '其他': 0.4      # 可调整
        }
    
    async def generate_budget_recommendations(
        self,
        user_id: str,
        db: AsyncSession,
        strategy: BudgetStrategy = BudgetStrategy.BALANCED,
        target_savings_rate: Optional[float] = None
    ) -> BudgetOptimization:
        """生成预算建议"""
        
        # 检查缓存
        cache = await get_cache_manager()
        cache_key = f"budget_recommendations:{user_id}:{strategy.value}:{target_savings_rate}"
        
        cached_result = await cache.get(cache_key)
        if cached_result:
            logger.info(f"使用缓存的预算建议: {user_id}")
            return BudgetOptimization(**cached_result)
        
        try:
            # 获取当前预算
            current_budgets = await self._get_current_budgets(user_id, db)
            
            # 获取支出预测
            predictions = await self.prediction_engine.predict_future_spending(
                user_id, 30, db
            )
            
            # 获取历史支出分析
            historical_analysis = await self._analyze_historical_spending(user_id, db)
            
            # 生成各分类的预算建议
            recommendations = []
            total_current = 0
            total_recommended = 0
            total_savings = 0
            
            for category_name, current_budget in current_budgets.items():
                recommendation = await self._generate_category_recommendation(
                    category_name,
                    current_budget,
                    predictions,
                    historical_analysis,
                    strategy,
                    target_savings_rate
                )
                
                if recommendation:
                    recommendations.append(recommendation)
                    total_current += current_budget
                    total_recommended += recommendation.recommended_budget
                    total_savings += recommendation.expected_savings
            
            # 处理没有预算的分类
            for pred in predictions:
                if pred.category_name not in current_budgets:
                    recommendation = await self._generate_new_category_recommendation(
                        pred, strategy
                    )
                    if recommendation:
                        recommendations.append(recommendation)
                        total_recommended += recommendation.recommended_budget
            
            # 计算优化分数
            optimization_score = self._calculate_optimization_score(
                recommendations, historical_analysis
            )
            
            # 风险评估
            risk_assessment = await self._assess_budget_risks(
                recommendations, historical_analysis, strategy
            )
            
            # 实施计划
            implementation_plan = self._create_implementation_plan(recommendations)
            
            optimization = BudgetOptimization(
                total_current_budget=total_current,
                total_recommended_budget=total_recommended,
                total_potential_savings=total_savings,
                optimization_score=optimization_score,
                recommendations=recommendations,
                risk_assessment=risk_assessment,
                implementation_plan=implementation_plan
            )
            
            # 缓存结果
            await cache.set(cache_key, optimization.__dict__, self.cache_ttl)
            
            logger.info(f"完成用户 {user_id} 的预算建议生成")
            return optimization
            
        except Exception as e:
            logger.error(f"生成预算建议失败: {e}")
            return BudgetOptimization(
                total_current_budget=0,
                total_recommended_budget=0,
                total_potential_savings=0,
                optimization_score=0,
                recommendations=[],
                risk_assessment={},
                implementation_plan=[]
            )
    
    async def _get_current_budgets(
        self, user_id: str, db: AsyncSession
    ) -> Dict[str, float]:
        """获取当前预算"""
        
        current_month = datetime.now().replace(day=1)
        
        query = select(Budget).where(
            and_(
                Budget.user_id == user_id,
                Budget.period_start <= current_month,
                Budget.period_end >= current_month
            )
        ).options(selectinload(Budget.category))
        
        result = await db.execute(query)
        budgets = result.scalars().all()
        
        current_budgets = {}
        for budget in budgets:
            category_name = budget.category.name if budget.category else '未分类'
            current_budgets[category_name] = float(budget.amount)
        
        return current_budgets
    
    async def _analyze_historical_spending(
        self, user_id: str, db: AsyncSession, months: int = 6
    ) -> Dict[str, Any]:
        """分析历史支出"""
        
        cutoff_date = datetime.now() - timedelta(days=months * 30)
        
        query = select(Transaction).where(
            and_(
                Transaction.user_id == user_id,
                Transaction.transaction_type == 'expense',
                Transaction.transaction_date >= cutoff_date
            )
        ).options(selectinload(Transaction.category))
        
        result = await db.execute(query)
        transactions = result.scalars().all()
        
        if not transactions:
            return {}
        
        # 转换为DataFrame进行分析
        data = []
        for trans in transactions:
            data.append({
                'amount': float(trans.amount),
                'category_name': trans.category.name if trans.category else '未分类',
                'date': trans.transaction_date,
                'month': trans.transaction_date.strftime('%Y-%m')
            })
        
        df = pd.DataFrame(data)
        
        # 按分类和月份分析
        monthly_by_category = df.groupby(['category_name', 'month'])['amount'].sum().unstack(fill_value=0)
        
        analysis = {}
        for category in monthly_by_category.index:
            monthly_amounts = monthly_by_category.loc[category]
            
            analysis[category] = {
                'average_monthly': monthly_amounts.mean(),
                'median_monthly': monthly_amounts.median(),
                'std_monthly': monthly_amounts.std(),
                'min_monthly': monthly_amounts.min(),
                'max_monthly': monthly_amounts.max(),
                'volatility': monthly_amounts.std() / monthly_amounts.mean() if monthly_amounts.mean() > 0 else 0,
                'trend': self._calculate_spending_trend(monthly_amounts),
                'consistency': self._calculate_consistency_score(monthly_amounts)
            }
        
        return analysis
    
    def _calculate_spending_trend(self, monthly_amounts: pd.Series) -> Dict[str, Any]:
        """计算支出趋势"""
        
        if len(monthly_amounts) < 3:
            return {'direction': 'stable', 'strength': 0}
        
        # 线性回归计算趋势
        x = np.arange(len(monthly_amounts))
        y = monthly_amounts.values
        
        slope = np.polyfit(x, y, 1)[0]
        
        # 计算趋势强度
        correlation = np.corrcoef(x, y)[0, 1] if len(x) > 1 else 0
        
        if abs(slope) < monthly_amounts.mean() * 0.05:  # 变化小于5%
            direction = 'stable'
        elif slope > 0:
            direction = 'increasing'
        else:
            direction = 'decreasing'
        
        return {
            'direction': direction,
            'strength': abs(correlation),
            'slope': slope
        }
    
    def _calculate_consistency_score(self, monthly_amounts: pd.Series) -> float:
        """计算一致性分数"""
        
        if len(monthly_amounts) < 2:
            return 0.5
        
        # 使用变异系数的倒数作为一致性分数
        cv = monthly_amounts.std() / monthly_amounts.mean() if monthly_amounts.mean() > 0 else 1
        consistency = 1 / (1 + cv)  # 转换为0-1分数
        
        return consistency
    
    async def _generate_category_recommendation(
        self,
        category_name: str,
        current_budget: float,
        predictions: List,
        historical_analysis: Dict[str, Any],
        strategy: BudgetStrategy,
        target_savings_rate: Optional[float]
    ) -> Optional[BudgetRecommendation]:
        """生成分类预算建议"""
        
        try:
            # 找到对应的预测
            prediction = None
            for pred in predictions:
                if pred.category_name == category_name:
                    prediction = pred
                    break
            
            if not prediction:
                return None
            
            # 获取历史分析数据
            historical = historical_analysis.get(category_name, {})
            
            # 获取策略参数
            strategy_params = self.strategy_multipliers[strategy]
            
            # 计算基础推荐预算
            predicted_monthly = prediction.predicted_amount * 30
            historical_average = historical.get('average_monthly', predicted_monthly)
            
            # 基于策略调整
            base_recommendation = (predicted_monthly + historical_average) / 2
            
            # 应用安全边际
            safety_margin = strategy_params['safety_margin']
            recommended_budget = base_recommendation * (1 + safety_margin)
            
            # 波动性调整
            volatility = historical.get('volatility', 0.2)
            volatility_adjustment = volatility * strategy_params['volatility_factor']
            recommended_budget *= (1 + volatility_adjustment)
            
            # 趋势调整
            trend = historical.get('trend', {})
            if trend.get('direction') == 'increasing':
                trend_adjustment = trend.get('strength', 0) * strategy_params['trend_sensitivity'] * 0.1
                recommended_budget *= (1 + trend_adjustment)
            elif trend.get('direction') == 'decreasing':
                trend_adjustment = trend.get('strength', 0) * strategy_params['trend_sensitivity'] * 0.1
                recommended_budget *= (1 - trend_adjustment)
            
            # 分类重要性调整
            importance = self.category_importance.get(category_name, 0.5)
            if importance < 0.5:  # 可调整分类
                recommended_budget *= 0.9  # 适当减少
            
            # 计算调整百分比
            adjustment_percentage = (recommended_budget - current_budget) / current_budget * 100 if current_budget > 0 else 0
            
            # 计算预期节省
            expected_savings = max(0, current_budget - recommended_budget)
            
            # 生成推理
            reasoning = self._generate_reasoning(
                category_name, current_budget, recommended_budget,
                prediction, historical, strategy
            )
            
            # 计算置信度
            confidence = self._calculate_recommendation_confidence(
                prediction, historical, strategy
            )
            
            # 确定优先级
            priority = self._determine_priority(
                adjustment_percentage, importance, confidence
            )
            
            return BudgetRecommendation(
                category_name=category_name,
                current_budget=current_budget,
                recommended_budget=recommended_budget,
                adjustment_percentage=adjustment_percentage,
                strategy=strategy,
                reasoning=reasoning,
                confidence=confidence,
                priority=priority,
                expected_savings=expected_savings
            )
            
        except Exception as e:
            logger.error(f"生成分类 {category_name} 预算建议失败: {e}")
            return None

    async def _generate_new_category_recommendation(
        self, prediction, strategy: BudgetStrategy
    ) -> Optional[BudgetRecommendation]:
        """为没有预算的分类生成建议"""

        try:
            # 基于预测生成新预算
            predicted_monthly = prediction.predicted_amount * 30
            strategy_params = self.strategy_multipliers[strategy]

            # 应用安全边际
            recommended_budget = predicted_monthly * (1 + strategy_params['safety_margin'])

            # 分类重要性调整
            importance = self.category_importance.get(prediction.category_name, 0.5)
            if importance > 0.7:  # 重要分类
                recommended_budget *= 1.1

            reasoning = [
                f"基于预测的新分类预算建议",
                f"预测月支出: {predicted_monthly:.2f}元",
                f"应用{strategy.value}策略安全边际"
            ]

            return BudgetRecommendation(
                category_name=prediction.category_name,
                current_budget=0,
                recommended_budget=recommended_budget,
                adjustment_percentage=100,  # 新增预算
                strategy=strategy,
                reasoning=reasoning,
                confidence=prediction.confidence,
                priority='medium',
                expected_savings=0
            )

        except Exception as e:
            logger.error(f"生成新分类预算建议失败: {e}")
            return None

    def _generate_reasoning(
        self,
        category_name: str,
        current_budget: float,
        recommended_budget: float,
        prediction,
        historical: Dict[str, Any],
        strategy: BudgetStrategy
    ) -> List[str]:
        """生成推理说明"""

        reasoning = []

        # 预测信息
        predicted_monthly = prediction.predicted_amount * 30
        reasoning.append(f"AI预测月支出: {predicted_monthly:.2f}元")

        # 历史信息
        if 'average_monthly' in historical:
            avg_monthly = historical['average_monthly']
            reasoning.append(f"历史月均支出: {avg_monthly:.2f}元")

        # 趋势信息
        trend = historical.get('trend', {})
        if trend.get('direction') != 'stable':
            reasoning.append(f"支出趋势: {trend['direction']}")

        # 策略说明
        if strategy == BudgetStrategy.CONSERVATIVE:
            reasoning.append("采用保守策略，增加较大安全边际")
        elif strategy == BudgetStrategy.AGGRESSIVE:
            reasoning.append("采用激进策略，追求更高效率")
        else:
            reasoning.append("采用平衡策略，兼顾安全与效率")

        # 调整说明
        if recommended_budget > current_budget:
            increase = (recommended_budget - current_budget) / current_budget * 100
            reasoning.append(f"建议增加预算 {increase:.1f}%")
        elif recommended_budget < current_budget:
            decrease = (current_budget - recommended_budget) / current_budget * 100
            reasoning.append(f"建议减少预算 {decrease:.1f}%，可节省 {current_budget - recommended_budget:.2f}元")

        return reasoning

    def _calculate_recommendation_confidence(
        self, prediction, historical: Dict[str, Any], strategy: BudgetStrategy
    ) -> float:
        """计算建议置信度"""

        # 基础置信度来自预测
        base_confidence = prediction.confidence

        # 历史数据质量调整
        consistency = historical.get('consistency', 0.5)
        data_quality_boost = consistency * 0.2

        # 策略调整
        strategy_confidence = {
            BudgetStrategy.CONSERVATIVE: 0.1,  # 保守策略增加置信度
            BudgetStrategy.BALANCED: 0.0,
            BudgetStrategy.AGGRESSIVE: -0.1   # 激进策略降低置信度
        }

        final_confidence = base_confidence + data_quality_boost + strategy_confidence[strategy]

        return min(max(final_confidence, 0.1), 0.95)

    def _determine_priority(
        self, adjustment_percentage: float, importance: float, confidence: float
    ) -> str:
        """确定优先级"""

        # 综合评分
        score = abs(adjustment_percentage) * 0.4 + importance * 0.3 + confidence * 0.3

        if score > 70:
            return 'high'
        elif score > 40:
            return 'medium'
        else:
            return 'low'

    def _calculate_optimization_score(
        self, recommendations: List[BudgetRecommendation], historical_analysis: Dict[str, Any]
    ) -> float:
        """计算优化分数"""

        if not recommendations:
            return 0

        # 基于节省金额和置信度计算分数
        total_savings = sum(rec.expected_savings for rec in recommendations)
        total_budget = sum(rec.current_budget for rec in recommendations)

        savings_ratio = total_savings / total_budget if total_budget > 0 else 0
        avg_confidence = np.mean([rec.confidence for rec in recommendations])

        # 综合分数
        optimization_score = (savings_ratio * 50 + avg_confidence * 50)

        return min(max(optimization_score, 0), 100)

    async def _assess_budget_risks(
        self,
        recommendations: List[BudgetRecommendation],
        historical_analysis: Dict[str, Any],
        strategy: BudgetStrategy
    ) -> Dict[str, Any]:
        """评估预算风险"""

        risks = {
            'overall_risk_level': 'low',
            'risk_factors': [],
            'mitigation_suggestions': []
        }

        # 分析大幅调整的风险
        large_adjustments = [
            rec for rec in recommendations
            if abs(rec.adjustment_percentage) > 30
        ]

        if large_adjustments:
            risks['risk_factors'].append({
                'type': 'large_adjustment',
                'description': f'{len(large_adjustments)}个分类需要大幅调整预算',
                'severity': 'medium'
            })
            risks['mitigation_suggestions'].append(
                '建议分阶段实施大幅预算调整，避免一次性改变过多'
            )

        # 分析低置信度建议的风险
        low_confidence = [
            rec for rec in recommendations
            if rec.confidence < 0.6
        ]

        if low_confidence:
            risks['risk_factors'].append({
                'type': 'low_confidence',
                'description': f'{len(low_confidence)}个建议置信度较低',
                'severity': 'low'
            })
            risks['mitigation_suggestions'].append(
                '对于低置信度的建议，建议先小幅调整并观察效果'
            )

        # 分析策略风险
        if strategy == BudgetStrategy.AGGRESSIVE:
            risks['risk_factors'].append({
                'type': 'strategy_risk',
                'description': '激进策略可能导致预算不足',
                'severity': 'medium'
            })
            risks['mitigation_suggestions'].append(
                '激进策略下建议密切监控实际支出，及时调整'
            )

        # 确定整体风险等级
        high_risk_count = sum(1 for factor in risks['risk_factors'] if factor['severity'] == 'high')
        medium_risk_count = sum(1 for factor in risks['risk_factors'] if factor['severity'] == 'medium')

        if high_risk_count > 0:
            risks['overall_risk_level'] = 'high'
        elif medium_risk_count > 1:
            risks['overall_risk_level'] = 'medium'

        return risks

    def _create_implementation_plan(
        self, recommendations: List[BudgetRecommendation]
    ) -> List[Dict[str, Any]]:
        """创建实施计划"""

        # 按优先级排序
        high_priority = [rec for rec in recommendations if rec.priority == 'high']
        medium_priority = [rec for rec in recommendations if rec.priority == 'medium']
        low_priority = [rec for rec in recommendations if rec.priority == 'low']

        plan = []

        # 第一阶段：高优先级
        if high_priority:
            plan.append({
                'phase': 1,
                'title': '立即实施（高优先级）',
                'duration': '本月内',
                'actions': [
                    f"调整{rec.category_name}预算至{rec.recommended_budget:.2f}元"
                    for rec in high_priority
                ],
                'expected_impact': sum(rec.expected_savings for rec in high_priority)
            })

        # 第二阶段：中优先级
        if medium_priority:
            plan.append({
                'phase': 2,
                'title': '逐步实施（中优先级）',
                'duration': '1-2个月内',
                'actions': [
                    f"调整{rec.category_name}预算至{rec.recommended_budget:.2f}元"
                    for rec in medium_priority
                ],
                'expected_impact': sum(rec.expected_savings for rec in medium_priority)
            })

        # 第三阶段：低优先级
        if low_priority:
            plan.append({
                'phase': 3,
                'title': '可选实施（低优先级）',
                'duration': '2-3个月内',
                'actions': [
                    f"考虑调整{rec.category_name}预算至{rec.recommended_budget:.2f}元"
                    for rec in low_priority
                ],
                'expected_impact': sum(rec.expected_savings for rec in low_priority)
            })

        return plan

    async def _generate_new_category_recommendation(
        self, prediction, strategy: BudgetStrategy
    ) -> Optional[BudgetRecommendation]:
        """为没有预算的分类生成建议"""

        try:
            # 基于预测生成新预算
            predicted_monthly = prediction.predicted_amount * 30
            strategy_params = self.strategy_multipliers[strategy]

            # 应用安全边际
            recommended_budget = predicted_monthly * (1 + strategy_params['safety_margin'])

            # 分类重要性调整
            importance = self.category_importance.get(prediction.category_name, 0.5)
            if importance > 0.7:  # 重要分类
                recommended_budget *= 1.1

            reasoning = [
                f"基于预测的新分类预算建议",
                f"预测月支出: {predicted_monthly:.2f}元",
                f"应用{strategy.value}策略安全边际"
            ]

            return BudgetRecommendation(
                category_name=prediction.category_name,
                current_budget=0,
                recommended_budget=recommended_budget,
                adjustment_percentage=100,  # 新增预算
                strategy=strategy,
                reasoning=reasoning,
                confidence=prediction.confidence,
                priority='medium',
                expected_savings=0
            )

        except Exception as e:
            logger.error(f"生成新分类预算建议失败: {e}")
            return None

    def _generate_reasoning(
        self,
        category_name: str,
        current_budget: float,
        recommended_budget: float,
        prediction,
        historical: Dict[str, Any],
        strategy: BudgetStrategy
    ) -> List[str]:
        """生成推理说明"""

        reasoning = []

        # 预测信息
        predicted_monthly = prediction.predicted_amount * 30
        reasoning.append(f"AI预测月支出: {predicted_monthly:.2f}元")

        # 历史信息
        if 'average_monthly' in historical:
            avg_monthly = historical['average_monthly']
            reasoning.append(f"历史月均支出: {avg_monthly:.2f}元")

        # 趋势信息
        trend = historical.get('trend', {})
        if trend.get('direction') != 'stable':
            reasoning.append(f"支出趋势: {trend['direction']}")

        # 策略说明
        if strategy == BudgetStrategy.CONSERVATIVE:
            reasoning.append("采用保守策略，增加较大安全边际")
        elif strategy == BudgetStrategy.AGGRESSIVE:
            reasoning.append("采用激进策略，追求更高效率")
        else:
            reasoning.append("采用平衡策略，兼顾安全与效率")

        # 调整说明
        if recommended_budget > current_budget:
            increase = (recommended_budget - current_budget) / current_budget * 100
            reasoning.append(f"建议增加预算 {increase:.1f}%")
        elif recommended_budget < current_budget:
            decrease = (current_budget - recommended_budget) / current_budget * 100
            reasoning.append(f"建议减少预算 {decrease:.1f}%，可节省 {current_budget - recommended_budget:.2f}元")

        return reasoning

    def _calculate_recommendation_confidence(
        self, prediction, historical: Dict[str, Any], strategy: BudgetStrategy
    ) -> float:
        """计算建议置信度"""

        # 基础置信度来自预测
        base_confidence = prediction.confidence

        # 历史数据质量调整
        consistency = historical.get('consistency', 0.5)
        data_quality_boost = consistency * 0.2

        # 策略调整
        strategy_confidence = {
            BudgetStrategy.CONSERVATIVE: 0.1,  # 保守策略增加置信度
            BudgetStrategy.BALANCED: 0.0,
            BudgetStrategy.AGGRESSIVE: -0.1   # 激进策略降低置信度
        }

        final_confidence = base_confidence + data_quality_boost + strategy_confidence[strategy]

        return min(max(final_confidence, 0.1), 0.95)

    def _determine_priority(
        self, adjustment_percentage: float, importance: float, confidence: float
    ) -> str:
        """确定优先级"""

        # 综合评分
        score = abs(adjustment_percentage) * 0.4 + importance * 0.3 + confidence * 0.3

        if score > 70:
            return 'high'
        elif score > 40:
            return 'medium'
        else:
            return 'low'

    def _calculate_optimization_score(
        self, recommendations: List[BudgetRecommendation], historical_analysis: Dict[str, Any]
    ) -> float:
        """计算优化分数"""

        if not recommendations:
            return 0

        # 基于节省金额和置信度计算分数
        total_savings = sum(rec.expected_savings for rec in recommendations)
        total_budget = sum(rec.current_budget for rec in recommendations)

        savings_ratio = total_savings / total_budget if total_budget > 0 else 0
        avg_confidence = np.mean([rec.confidence for rec in recommendations])

        # 综合分数
        optimization_score = (savings_ratio * 50 + avg_confidence * 50)

        return min(max(optimization_score, 0), 100)
