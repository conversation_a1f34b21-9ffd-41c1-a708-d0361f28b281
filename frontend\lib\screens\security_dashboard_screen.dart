import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/security_service.dart';
import '../services/language_service.dart';
import '../l10n/app_localizations.dart';
import '../widgets/loading_widget.dart';
import '../widgets/error_widget.dart';

class SecurityDashboardScreen extends StatefulWidget {
  const SecurityDashboardScreen({Key? key}) : super(key: key);

  @override
  State<SecurityDashboardScreen> createState() => _SecurityDashboardScreenState();
}

class _SecurityDashboardScreenState extends State<SecurityDashboardScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final SecurityService _securityService = SecurityService();
  
  Map<String, dynamic>? _securityStatus;
  List<Map<String, dynamic>> _alerts = [];
  List<Map<String, dynamic>> _auditEvents = [];
  List<Map<String, dynamic>> _rotationHistory = [];
  
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadSecurityData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSecurityData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // 并行加载所有安全数据
      final results = await Future.wait([
        _securityService.getSecurityStatus(),
        _securityService.getSecurityAlerts(),
        _securityService.getRecentAuditEvents(),
        _securityService.getKeyRotationHistory(),
      ]);

      setState(() {
        _securityStatus = results[0];
        _alerts = List<Map<String, dynamic>>.from(results[1]);
        _auditEvents = List<Map<String, dynamic>>.from(results[2]);
        _rotationHistory = List<Map<String, dynamic>>.from(results[3]);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.securityDashboard),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              icon: const Icon(Icons.security),
              text: l10n.overview,
            ),
            Tab(
              icon: const Icon(Icons.warning),
              text: l10n.alerts,
            ),
            Tab(
              icon: const Icon(Icons.history),
              text: l10n.auditLog,
            ),
            Tab(
              icon: const Icon(Icons.vpn_key),
              text: l10n.keyRotation,
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSecurityData,
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : _error != null
              ? ErrorWidget(
                  message: _error!,
                  onRetry: _loadSecurityData,
                )
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildOverviewTab(context, l10n),
                    _buildAlertsTab(context, l10n),
                    _buildAuditTab(context, l10n),
                    _buildKeyRotationTab(context, l10n),
                  ],
                ),
    );
  }

  Widget _buildOverviewTab(BuildContext context, AppLocalizations l10n) {
    if (_securityStatus == null) {
      return const Center(child: Text('No security status data'));
    }

    return RefreshIndicator(
      onRefresh: _loadSecurityData,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildStatusCard(context, l10n),
          const SizedBox(height: 16),
          _buildQuickStatsCard(context, l10n),
          const SizedBox(height: 16),
          _buildRecentAlertsCard(context, l10n),
        ],
      ),
    );
  }

  Widget _buildStatusCard(BuildContext context, AppLocalizations l10n) {
    final status = _securityStatus!['monitoring_status'] ?? 'inactive';
    final isActive = status == 'active';
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isActive ? Icons.security : Icons.security_outlined,
                  color: isActive ? Colors.green : Colors.orange,
                  size: 32,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        l10n.securityStatus,
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      Text(
                        isActive ? l10n.active : l10n.inactive,
                        style: TextStyle(
                          color: isActive ? Colors.green : Colors.orange,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatusItem(
                  context,
                  l10n.blockedIPs,
                  _securityStatus!['blocked_ips_count']?.toString() ?? '0',
                  Icons.block,
                  Colors.red,
                ),
                _buildStatusItem(
                  context,
                  l10n.suspiciousIPs,
                  _securityStatus!['suspicious_ips_count']?.toString() ?? '0',
                  Icons.warning,
                  Colors.orange,
                ),
                _buildStatusItem(
                  context,
                  l10n.totalAlerts,
                  _securityStatus!['total_alerts']?.toString() ?? '0',
                  Icons.notifications,
                  Colors.blue,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildQuickStatsCard(BuildContext context, AppLocalizations l10n) {
    final alertsByLevel = _securityStatus!['alerts_by_level'] as Map<String, dynamic>? ?? {};
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.alertsByThreatLevel,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildThreatLevelItem(
                  context,
                  l10n.critical,
                  alertsByLevel['critical']?.toString() ?? '0',
                  Colors.red,
                ),
                _buildThreatLevelItem(
                  context,
                  l10n.high,
                  alertsByLevel['high']?.toString() ?? '0',
                  Colors.orange,
                ),
                _buildThreatLevelItem(
                  context,
                  l10n.medium,
                  alertsByLevel['medium']?.toString() ?? '0',
                  Colors.yellow,
                ),
                _buildThreatLevelItem(
                  context,
                  l10n.low,
                  alertsByLevel['low']?.toString() ?? '0',
                  Colors.green,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThreatLevelItem(
    BuildContext context,
    String label,
    String count,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Center(
            child: Text(
              count,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildRecentAlertsCard(BuildContext context, AppLocalizations l10n) {
    final recentAlerts = _alerts.take(3).toList();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  l10n.recentAlerts,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                TextButton(
                  onPressed: () => _tabController.animateTo(1),
                  child: Text(l10n.viewAll),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (recentAlerts.isEmpty)
              Center(
                child: Text(
                  l10n.noRecentAlerts,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey,
                      ),
                ),
              )
            else
              ...recentAlerts.map((alert) => _buildAlertItem(context, alert, compact: true)),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertsTab(BuildContext context, AppLocalizations l10n) {
    return RefreshIndicator(
      onRefresh: _loadSecurityData,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          if (_alerts.isEmpty)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.security,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    l10n.noSecurityAlerts,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.grey,
                        ),
                  ),
                ],
              ),
            )
          else
            ..._alerts.map((alert) => _buildAlertItem(context, alert)),
        ],
      ),
    );
  }

  Widget _buildAlertItem(BuildContext context, Map<String, dynamic> alert, {bool compact = false}) {
    final threatLevel = alert['threat_level'] ?? 'low';
    final eventType = alert['event_type'] ?? 'unknown';
    final description = alert['description'] ?? '';
    final timestamp = DateTime.tryParse(alert['timestamp'] ?? '') ?? DateTime.now();
    final resolved = alert['resolved'] ?? false;
    
    Color levelColor;
    IconData levelIcon;
    
    switch (threatLevel) {
      case 'critical':
        levelColor = Colors.red;
        levelIcon = Icons.dangerous;
        break;
      case 'high':
        levelColor = Colors.orange;
        levelIcon = Icons.warning;
        break;
      case 'medium':
        levelColor = Colors.yellow[700]!;
        levelIcon = Icons.info;
        break;
      default:
        levelColor = Colors.green;
        levelIcon = Icons.check_circle;
    }
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          levelIcon,
          color: resolved ? Colors.grey : levelColor,
        ),
        title: Text(
          description,
          style: TextStyle(
            decoration: resolved ? TextDecoration.lineThrough : null,
            color: resolved ? Colors.grey : null,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${eventType.toUpperCase()} • ${_formatDateTime(timestamp)}'),
            if (!compact && alert['source_ip'] != null)
              Text('IP: ${alert['source_ip']}'),
          ],
        ),
        trailing: resolved
            ? const Icon(Icons.check, color: Colors.green)
            : Chip(
                label: Text(
                  threatLevel.toUpperCase(),
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                backgroundColor: levelColor.withOpacity(0.2),
                side: BorderSide(color: levelColor),
              ),
        onTap: compact ? null : () => _showAlertDetails(context, alert),
      ),
    );
  }

  Widget _buildAuditTab(BuildContext context, AppLocalizations l10n) {
    return RefreshIndicator(
      onRefresh: _loadSecurityData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _auditEvents.length,
        itemBuilder: (context, index) {
          final event = _auditEvents[index];
          return _buildAuditEventItem(context, event);
        },
      ),
    );
  }

  Widget _buildAuditEventItem(BuildContext context, Map<String, dynamic> event) {
    final eventType = event['event_type'] ?? 'unknown';
    final action = event['action'] ?? '';
    final timestamp = DateTime.tryParse(event['timestamp'] ?? '') ?? DateTime.now();
    final level = event['level'] ?? 'low';
    
    IconData eventIcon;
    Color eventColor;
    
    switch (eventType) {
      case 'login':
        eventIcon = Icons.login;
        eventColor = Colors.blue;
        break;
      case 'api_access':
        eventIcon = Icons.api;
        eventColor = Colors.green;
        break;
      case 'security_event':
        eventIcon = Icons.security;
        eventColor = Colors.red;
        break;
      case 'system_config':
        eventIcon = Icons.settings;
        eventColor = Colors.orange;
        break;
      default:
        eventIcon = Icons.event;
        eventColor = Colors.grey;
    }
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(eventIcon, color: eventColor),
        title: Text('$eventType: $action'),
        subtitle: Text(_formatDateTime(timestamp)),
        trailing: Chip(
          label: Text(
            level.toUpperCase(),
            style: const TextStyle(fontSize: 10),
          ),
          backgroundColor: _getLevelColor(level).withOpacity(0.2),
        ),
        onTap: () => _showAuditEventDetails(context, event),
      ),
    );
  }

  Widget _buildKeyRotationTab(BuildContext context, AppLocalizations l10n) {
    return RefreshIndicator(
      onRefresh: _loadSecurityData,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: ElevatedButton.icon(
              onPressed: () => _showScheduleRotationDialog(context),
              icon: const Icon(Icons.schedule),
              label: Text(l10n.scheduleRotation),
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _rotationHistory.length,
              itemBuilder: (context, index) {
                final rotation = _rotationHistory[index];
                return _buildRotationItem(context, rotation);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRotationItem(BuildContext context, Map<String, dynamic> rotation) {
    final provider = rotation['provider'] ?? 'unknown';
    final status = rotation['status'] ?? 'pending';
    final scheduledTime = DateTime.tryParse(rotation['scheduled_time'] ?? '') ?? DateTime.now();
    final reason = rotation['reason'] ?? 'manual';
    
    IconData statusIcon;
    Color statusColor;
    
    switch (status) {
      case 'completed':
        statusIcon = Icons.check_circle;
        statusColor = Colors.green;
        break;
      case 'failed':
        statusIcon = Icons.error;
        statusColor = Colors.red;
        break;
      case 'in_progress':
        statusIcon = Icons.hourglass_empty;
        statusColor = Colors.orange;
        break;
      default:
        statusIcon = Icons.schedule;
        statusColor = Colors.blue;
    }
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(statusIcon, color: statusColor),
        title: Text('$provider Key Rotation'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Status: ${status.toUpperCase()}'),
            Text('Scheduled: ${_formatDateTime(scheduledTime)}'),
            Text('Reason: $reason'),
          ],
        ),
        trailing: status == 'pending'
            ? IconButton(
                icon: const Icon(Icons.cancel),
                onPressed: () => _cancelRotation(rotation['id']),
              )
            : null,
      ),
    );
  }

  Color _getLevelColor(String level) {
    switch (level) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showAlertDetails(BuildContext context, Map<String, dynamic> alert) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Alert Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Type: ${alert['event_type']}'),
              Text('Level: ${alert['threat_level']}'),
              Text('Description: ${alert['description']}'),
              if (alert['source_ip'] != null)
                Text('Source IP: ${alert['source_ip']}'),
              Text('Time: ${_formatDateTime(DateTime.parse(alert['timestamp']))}'),
              if (alert['actions_taken'] != null && (alert['actions_taken'] as List).isNotEmpty)
                Text('Actions: ${(alert['actions_taken'] as List).join(', ')}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAuditEventDetails(BuildContext context, Map<String, dynamic> event) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Audit Event Details'),
        content: SingleChildScrollView(
          child: Text(event.toString()),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showScheduleRotationDialog(BuildContext context) {
    // TODO: 实现密钥轮换调度对话框
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Schedule Key Rotation'),
        content: const Text('Key rotation scheduling feature coming soon.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _cancelRotation(String rotationId) {
    // TODO: 实现取消密钥轮换
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Rotation cancellation feature coming soon.')),
    );
  }
}
