#!/usr/bin/env python3
import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🚦 测试限流功能...")

try:
    from app.utils.rate_limiter import get_rate_limiter, RateLimit
    
    async def test():
        limiter = await get_rate_limiter()
        
        # 创建测试限制：3次/10秒
        test_limit = RateLimit(limit=3, window=10)
        limiter.add_custom_limit("test", test_limit)
        
        print("测试限制: 3次/10秒")
        
        # 测试5次请求
        for i in range(5):
            result = await limiter.check_rate_limit("test_user", "test")
            status = "✅ 允许" if result.allowed else "❌ 限流"
            print(f"请求 {i+1}: {status} (剩余: {result.remaining})")
        
        # 获取统计信息
        stats = await limiter.get_global_stats()
        print(f"限流类型数量: {stats['total_limit_types']}")
        
        print("🎉 限流功能测试完成!")
    
    asyncio.run(test())
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

print("📊 测试结束")
