"""
高级分析和洞察服务
提供支出模式分析、预算建议、趋势预测等功能
"""

import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc, asc
from sqlalchemy.sql import text
import statistics
from collections import defaultdict, Counter
import calendar

from ..models.transaction import Transaction
from ..models.category import Category
from ..models.budget import Budget
from ..models.user import User
from ..utils.cache import get_cache_manager
from loguru import logger


class AnalyticsService:
    """高级分析服务"""
    
    def __init__(self):
        self.cache = None
        
    async def _get_cache(self):
        """获取缓存管理器"""
        if not self.cache:
            self.cache = await get_cache_manager()
        return self.cache
    
    async def analyze_spending_patterns(
        self, 
        user_id: str, 
        db: AsyncSession,
        months: int = 6
    ) -> Dict[str, Any]:
        """分析用户支出模式"""
        try:
            cache = await self._get_cache()
            cache_key = f"spending_patterns:{user_id}:{months}"
            
            # 尝试从缓存获取
            cached_result = await cache.get(cache_key)
            if cached_result:
                return cached_result
            
            # 计算时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=months * 30)
            
            # 获取交易数据
            result = await db.execute(
                select(Transaction, Category)
                .join(Category, Transaction.category_id == Category.id, isouter=True)
                .where(
                    and_(
                        Transaction.user_id == user_id,
                        Transaction.transaction_date >= start_date,
                        Transaction.transaction_date <= end_date,
                        Transaction.transaction_type == "expense"
                    )
                )
                .order_by(Transaction.transaction_date.desc())
            )
            
            transactions = result.all()
            
            if not transactions:
                return {
                    "total_transactions": 0,
                    "analysis_period": f"{months}个月",
                    "message": "暂无足够数据进行分析"
                }
            
            # 分析数据
            analysis = await self._analyze_transaction_patterns(transactions, months)
            
            # 缓存结果（1小时）
            await cache.set(cache_key, analysis, ttl=3600)
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析支出模式失败: {e}")
            raise
    
    async def _analyze_transaction_patterns(
        self, 
        transactions: List[Tuple], 
        months: int
    ) -> Dict[str, Any]:
        """分析交易模式"""
        
        # 基础统计
        total_amount = sum(float(trans.Transaction.amount) for trans in transactions)
        total_count = len(transactions)
        avg_amount = total_amount / total_count if total_count > 0 else 0
        
        # 按类别分析
        category_stats = defaultdict(lambda: {"amount": 0, "count": 0, "transactions": []})
        
        for trans_tuple in transactions:
            trans = trans_tuple.Transaction
            category = trans_tuple.Category
            
            category_name = category.name if category else "未分类"
            category_stats[category_name]["amount"] += float(trans.amount)
            category_stats[category_name]["count"] += 1
            category_stats[category_name]["transactions"].append({
                "amount": float(trans.amount),
                "date": trans.transaction_date.isoformat(),
                "description": trans.description
            })
        
        # 排序类别（按金额）
        sorted_categories = sorted(
            category_stats.items(),
            key=lambda x: x[1]["amount"],
            reverse=True
        )
        
        # 按月份分析
        monthly_stats = defaultdict(lambda: {"amount": 0, "count": 0})
        
        for trans_tuple in transactions:
            trans = trans_tuple.Transaction
            month_key = trans.transaction_date.strftime("%Y-%m")
            monthly_stats[month_key]["amount"] += float(trans.amount)
            monthly_stats[month_key]["count"] += 1
        
        # 按星期分析
        weekday_stats = defaultdict(lambda: {"amount": 0, "count": 0})
        weekday_names = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        
        for trans_tuple in transactions:
            trans = trans_tuple.Transaction
            weekday = trans.transaction_date.weekday()
            weekday_name = weekday_names[weekday]
            weekday_stats[weekday_name]["amount"] += float(trans.amount)
            weekday_stats[weekday_name]["count"] += 1
        
        # 按时间段分析（上午、下午、晚上）
        time_period_stats = defaultdict(lambda: {"amount": 0, "count": 0})
        
        for trans_tuple in transactions:
            trans = trans_tuple.Transaction
            hour = trans.created_at.hour
            
            if 6 <= hour < 12:
                period = "上午"
            elif 12 <= hour < 18:
                period = "下午"
            else:
                period = "晚上"
            
            time_period_stats[period]["amount"] += float(trans.amount)
            time_period_stats[period]["count"] += 1
        
        # 计算趋势
        amounts = [float(trans.Transaction.amount) for trans in transactions]
        trend_analysis = self._calculate_trend(amounts)
        
        # 异常检测
        anomalies = self._detect_anomalies(amounts)
        
        return {
            "analysis_period": f"{months}个月",
            "total_transactions": total_count,
            "total_amount": round(total_amount, 2),
            "average_amount": round(avg_amount, 2),
            "median_amount": round(statistics.median(amounts) if amounts else 0, 2),
            "max_amount": round(max(amounts) if amounts else 0, 2),
            "min_amount": round(min(amounts) if amounts else 0, 2),
            
            "category_analysis": [
                {
                    "category": cat_name,
                    "total_amount": round(stats["amount"], 2),
                    "transaction_count": stats["count"],
                    "average_amount": round(stats["amount"] / stats["count"], 2),
                    "percentage": round((stats["amount"] / total_amount) * 100, 1) if total_amount > 0 else 0,
                    "recent_transactions": stats["transactions"][:5]  # 最近5笔
                }
                for cat_name, stats in sorted_categories[:10]  # 前10个类别
            ],
            
            "monthly_trend": [
                {
                    "month": month,
                    "amount": round(stats["amount"], 2),
                    "count": stats["count"],
                    "average": round(stats["amount"] / stats["count"], 2) if stats["count"] > 0 else 0
                }
                for month, stats in sorted(monthly_stats.items())
            ],
            
            "weekday_pattern": [
                {
                    "weekday": day,
                    "amount": round(stats["amount"], 2),
                    "count": stats["count"],
                    "average": round(stats["amount"] / stats["count"], 2) if stats["count"] > 0 else 0
                }
                for day, stats in weekday_stats.items()
            ],
            
            "time_period_pattern": [
                {
                    "period": period,
                    "amount": round(stats["amount"], 2),
                    "count": stats["count"],
                    "percentage": round((stats["amount"] / total_amount) * 100, 1) if total_amount > 0 else 0
                }
                for period, stats in time_period_stats.items()
            ],
            
            "trend_analysis": trend_analysis,
            "anomalies": anomalies,
            "generated_at": datetime.now().isoformat()
        }
    
    def _calculate_trend(self, amounts: List[float]) -> Dict[str, Any]:
        """计算趋势分析"""
        if len(amounts) < 2:
            return {"trend": "insufficient_data", "description": "数据不足以分析趋势"}
        
        # 简单线性趋势计算
        n = len(amounts)
        x = list(range(n))
        
        # 计算斜率
        x_mean = sum(x) / n
        y_mean = sum(amounts) / n
        
        numerator = sum((x[i] - x_mean) * (amounts[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            slope = 0
        else:
            slope = numerator / denominator
        
        # 判断趋势
        if slope > 0.1:
            trend = "increasing"
            description = "支出呈上升趋势"
        elif slope < -0.1:
            trend = "decreasing"
            description = "支出呈下降趋势"
        else:
            trend = "stable"
            description = "支出相对稳定"
        
        # 计算变异系数（稳定性指标）
        std_dev = statistics.stdev(amounts) if len(amounts) > 1 else 0
        mean_amount = statistics.mean(amounts)
        cv = (std_dev / mean_amount) * 100 if mean_amount > 0 else 0
        
        stability = "high" if cv < 30 else "medium" if cv < 60 else "low"
        
        return {
            "trend": trend,
            "description": description,
            "slope": round(slope, 4),
            "stability": stability,
            "coefficient_of_variation": round(cv, 2)
        }
    
    def _detect_anomalies(self, amounts: List[float]) -> List[Dict[str, Any]]:
        """检测异常支出"""
        if len(amounts) < 5:
            return []
        
        # 使用IQR方法检测异常值
        sorted_amounts = sorted(amounts)
        n = len(sorted_amounts)
        
        q1_idx = n // 4
        q3_idx = 3 * n // 4
        
        q1 = sorted_amounts[q1_idx]
        q3 = sorted_amounts[q3_idx]
        iqr = q3 - q1
        
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        anomalies = []
        for amount in amounts:
            if amount < lower_bound:
                anomalies.append({
                    "type": "unusually_low",
                    "amount": round(amount, 2),
                    "description": f"异常低支出：{amount:.2f}元"
                })
            elif amount > upper_bound:
                anomalies.append({
                    "type": "unusually_high", 
                    "amount": round(amount, 2),
                    "description": f"异常高支出：{amount:.2f}元"
                })
        
        return anomalies[:10]  # 最多返回10个异常
    
    async def generate_budget_recommendations(
        self,
        user_id: str,
        db: AsyncSession,
        analysis_months: int = 3
    ) -> Dict[str, Any]:
        """生成预算建议"""
        try:
            cache = await self._get_cache()
            cache_key = f"budget_recommendations:{user_id}:{analysis_months}"
            
            # 尝试从缓存获取
            cached_result = await cache.get(cache_key)
            if cached_result:
                return cached_result
            
            # 获取支出分析
            spending_analysis = await self.analyze_spending_patterns(
                user_id, db, analysis_months
            )
            
            if spending_analysis["total_transactions"] == 0:
                return {
                    "recommendations": [],
                    "message": "暂无足够数据生成预算建议"
                }
            
            # 获取现有预算
            current_budgets = await self._get_current_budgets(user_id, db)
            
            # 生成建议
            recommendations = await self._generate_recommendations(
                spending_analysis, current_budgets
            )
            
            result = {
                "analysis_period": f"{analysis_months}个月",
                "recommendations": recommendations,
                "generated_at": datetime.now().isoformat()
            }
            
            # 缓存结果（2小时）
            await cache.set(cache_key, result, ttl=7200)
            
            return result
            
        except Exception as e:
            logger.error(f"生成预算建议失败: {e}")
            raise
    
    async def _get_current_budgets(self, user_id: str, db: AsyncSession) -> Dict[str, Any]:
        """获取当前预算"""
        current_month = datetime.now().replace(day=1)
        
        result = await db.execute(
            select(Budget, Category)
            .join(Category, Budget.category_id == Category.id, isouter=True)
            .where(
                and_(
                    Budget.user_id == user_id,
                    Budget.period_start <= current_month,
                    Budget.period_end >= current_month,
                    Budget.is_active == True
                )
            )
        )
        
        budgets = {}
        for budget_tuple in result.all():
            budget = budget_tuple.Budget
            category = budget_tuple.Category
            
            category_name = category.name if category else "未分类"
            budgets[category_name] = {
                "amount": float(budget.amount),
                "spent": float(budget.spent_amount),
                "remaining": float(budget.amount - budget.spent_amount)
            }
        
        return budgets
    
    async def _generate_recommendations(
        self,
        spending_analysis: Dict[str, Any],
        current_budgets: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成具体建议"""
        recommendations = []
        
        # 基于类别分析生成建议
        for category_data in spending_analysis["category_analysis"]:
            category_name = category_data["category"]
            avg_monthly = category_data["total_amount"] / (spending_analysis.get("analysis_period_months", 3) or 3)
            
            # 建议预算金额（平均值 + 20%缓冲）
            suggested_budget = avg_monthly * 1.2
            
            current_budget = current_budgets.get(category_name, {}).get("amount", 0)
            
            if current_budget == 0:
                # 没有预算，建议创建
                recommendations.append({
                    "type": "create_budget",
                    "category": category_name,
                    "suggested_amount": round(suggested_budget, 2),
                    "reason": f"基于{spending_analysis['analysis_period']}的平均支出",
                    "priority": "high" if category_data["percentage"] > 20 else "medium"
                })
            elif current_budget < avg_monthly * 0.8:
                # 预算过低
                recommendations.append({
                    "type": "increase_budget",
                    "category": category_name,
                    "current_amount": current_budget,
                    "suggested_amount": round(suggested_budget, 2),
                    "reason": "当前预算可能不足以覆盖正常支出",
                    "priority": "high"
                })
            elif current_budget > avg_monthly * 2:
                # 预算过高
                recommendations.append({
                    "type": "reduce_budget",
                    "category": category_name,
                    "current_amount": current_budget,
                    "suggested_amount": round(suggested_budget, 2),
                    "reason": "当前预算远超实际支出，可以适当降低",
                    "priority": "low"
                })
        
        # 基于趋势分析生成建议
        trend = spending_analysis.get("trend_analysis", {})
        if trend.get("trend") == "increasing":
            recommendations.append({
                "type": "spending_alert",
                "category": "总体支出",
                "reason": "支出呈上升趋势，建议关注支出控制",
                "priority": "high",
                "suggestion": "考虑制定更严格的预算计划"
            })
        
        # 基于异常检测生成建议
        anomalies = spending_analysis.get("anomalies", [])
        if len(anomalies) > 5:
            recommendations.append({
                "type": "spending_pattern_alert",
                "category": "支出模式",
                "reason": f"检测到{len(anomalies)}个异常支出",
                "priority": "medium",
                "suggestion": "建议审查大额或异常支出的必要性"
            })
        
        return sorted(recommendations, key=lambda x: {"high": 3, "medium": 2, "low": 1}[x["priority"]], reverse=True)


# 全局服务实例
_analytics_service = None


async def get_analytics_service() -> AnalyticsService:
    """获取分析服务实例"""
    global _analytics_service
    if _analytics_service is None:
        _analytics_service = AnalyticsService()
    return _analytics_service





