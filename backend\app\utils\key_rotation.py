"""
API密钥轮换系统
提供自动和手动的API密钥轮换功能，确保密钥安全性
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass, asdict
from pathlib import Path

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_
from loguru import logger

from ..models.user import User
from ..models.user_ai_config import UserAIConfig
from ..utils.encryption import APIKeyEncryption
from ..utils.cache import get_cache_manager_sync
from .audit_logger import AuditLogger


class RotationStatus(Enum):
    """密钥轮换状态"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class RotationReason(Enum):
    """密钥轮换原因"""
    SCHEDULED = "scheduled"  # 定期轮换
    MANUAL = "manual"  # 手动轮换
    SECURITY_BREACH = "security_breach"  # 安全事件
    KEY_COMPROMISE = "key_compromise"  # 密钥泄露
    POLICY_CHANGE = "policy_change"  # 策略变更


@dataclass
class RotationRecord:
    """密钥轮换记录"""
    id: str
    user_id: str
    provider: str
    old_key_hash: str
    new_key_hash: str
    status: RotationStatus
    reason: RotationReason
    created_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    metadata: Optional[Dict] = None

    def to_dict(self) -> Dict:
        """转换为字典"""
        data = asdict(self)
        data['status'] = self.status.value
        data['reason'] = self.reason.value
        data['created_at'] = self.created_at.isoformat()
        if self.completed_at:
            data['completed_at'] = self.completed_at.isoformat()
        return data


class KeyRotationManager:
    """API密钥轮换管理器"""

    def __init__(self):
        self.encryption_manager = APIKeyEncryption()
        self.cache_manager = get_cache_manager_sync()
        self.audit_logger = AuditLogger()
        self.rotation_records: Dict[str, RotationRecord] = {}
        self.rotation_policies: Dict[str, Dict] = {}
        self._load_rotation_policies()

    def _load_rotation_policies(self):
        """加载轮换策略"""
        self.rotation_policies = {
            "default": {
                "rotation_interval_days": 90,  # 90天轮换一次
                "warning_days": 7,  # 提前7天警告
                "max_key_age_days": 365,  # 密钥最大使用期限
                "auto_rotation": True,  # 自动轮换
                "backup_old_keys": True,  # 备份旧密钥
                "notification_enabled": True,  # 启用通知
            },
            "high_security": {
                "rotation_interval_days": 30,  # 30天轮换一次
                "warning_days": 3,  # 提前3天警告
                "max_key_age_days": 60,  # 密钥最大使用期限
                "auto_rotation": True,
                "backup_old_keys": True,
                "notification_enabled": True,
            },
            "low_security": {
                "rotation_interval_days": 180,  # 180天轮换一次
                "warning_days": 14,  # 提前14天警告
                "max_key_age_days": 730,  # 密钥最大使用期限
                "auto_rotation": False,  # 手动轮换
                "backup_old_keys": False,
                "notification_enabled": False,
            }
        }

    async def schedule_rotation(
        self,
        user_id: str,
        provider: str,
        reason: RotationReason = RotationReason.SCHEDULED,
        delay_hours: int = 0,
        metadata: Optional[Dict] = None
    ) -> str:
        """安排密钥轮换"""
        try:
            rotation_id = f"rot_{user_id}_{provider}_{int(datetime.now().timestamp())}"
            
            # 获取当前密钥信息
            current_key_hash = await self._get_current_key_hash(user_id, provider)
            if not current_key_hash:
                raise ValueError(f"未找到用户 {user_id} 的 {provider} 密钥")

            # 创建轮换记录
            rotation_record = RotationRecord(
                id=rotation_id,
                user_id=user_id,
                provider=provider,
                old_key_hash=current_key_hash,
                new_key_hash="",  # 将在轮换时生成
                status=RotationStatus.PENDING,
                reason=reason,
                created_at=datetime.now(),
                metadata=metadata or {}
            )

            self.rotation_records[rotation_id] = rotation_record

            # 记录审计日志
            await self.audit_logger.log_security_event(
                user_id=user_id,
                event_type="key_rotation_scheduled",
                details={
                    "rotation_id": rotation_id,
                    "provider": provider,
                    "reason": reason.value,
                    "delay_hours": delay_hours
                }
            )

            # 如果有延迟，安排异步执行
            if delay_hours > 0:
                asyncio.create_task(
                    self._delayed_rotation(rotation_id, delay_hours)
                )
            else:
                # 立即执行轮换
                asyncio.create_task(self._execute_rotation(rotation_id))

            logger.info(f"已安排密钥轮换: {rotation_id}")
            return rotation_id

        except Exception as e:
            logger.error(f"安排密钥轮换失败: {e}")
            raise

    async def _delayed_rotation(self, rotation_id: str, delay_hours: int):
        """延迟执行轮换"""
        await asyncio.sleep(delay_hours * 3600)  # 转换为秒
        await self._execute_rotation(rotation_id)

    async def _execute_rotation(self, rotation_id: str):
        """执行密钥轮换"""
        try:
            rotation_record = self.rotation_records.get(rotation_id)
            if not rotation_record:
                raise ValueError(f"未找到轮换记录: {rotation_id}")

            # 更新状态为进行中
            rotation_record.status = RotationStatus.IN_PROGRESS
            
            logger.info(f"开始执行密钥轮换: {rotation_id}")

            # 生成新密钥（这里模拟生成过程）
            new_key = await self._generate_new_key(
                rotation_record.user_id,
                rotation_record.provider
            )

            # 加密新密钥
            encrypted_new_key = self.encryption_manager.encrypt_api_key(
                rotation_record.provider,
                new_key
            )

            # 更新数据库中的密钥
            await self._update_key_in_database(
                rotation_record.user_id,
                rotation_record.provider,
                encrypted_new_key
            )

            # 备份旧密钥（如果策略要求）
            policy = self._get_rotation_policy(rotation_record.user_id)
            if policy.get("backup_old_keys", False):
                await self._backup_old_key(rotation_record)

            # 更新轮换记录
            rotation_record.new_key_hash = self._hash_key(new_key)
            rotation_record.status = RotationStatus.COMPLETED
            rotation_record.completed_at = datetime.now()

            # 清除相关缓存
            await self._clear_key_cache(rotation_record.user_id, rotation_record.provider)

            # 记录审计日志
            await self.audit_logger.log_security_event(
                user_id=rotation_record.user_id,
                event_type="key_rotation_completed",
                details={
                    "rotation_id": rotation_id,
                    "provider": rotation_record.provider,
                    "reason": rotation_record.reason.value
                }
            )

            # 发送通知（如果启用）
            if policy.get("notification_enabled", False):
                await self._send_rotation_notification(rotation_record)

            logger.info(f"密钥轮换完成: {rotation_id}")

        except Exception as e:
            # 更新状态为失败
            if rotation_id in self.rotation_records:
                self.rotation_records[rotation_id].status = RotationStatus.FAILED
                self.rotation_records[rotation_id].error_message = str(e)

            # 记录错误日志
            await self.audit_logger.log_security_event(
                user_id=rotation_record.user_id if 'rotation_record' in locals() else "unknown",
                event_type="key_rotation_failed",
                details={
                    "rotation_id": rotation_id,
                    "error": str(e)
                }
            )

            logger.error(f"密钥轮换失败: {rotation_id}, 错误: {e}")
            raise

    async def _generate_new_key(self, user_id: str, provider: str) -> str:
        """生成新的API密钥"""
        # 这里应该调用相应服务商的API来生成新密钥
        # 目前返回模拟密钥
        import secrets
        import string
        
        if provider.lower() == "openai":
            # OpenAI密钥格式: sk-...
            return f"sk-{''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(48))}"
        elif provider.lower() == "anthropic":
            # Anthropic密钥格式: sk-ant-...
            return f"sk-ant-{''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(40))}"
        elif provider.lower() == "google":
            # Google密钥格式
            return f"AIza{''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(35))}"
        else:
            # 通用格式
            return f"key_{''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))}"

    async def _get_current_key_hash(self, user_id: str, provider: str) -> Optional[str]:
        """获取当前密钥的哈希值"""
        try:
            # 从缓存获取
            cache_key = f"key_hash:{user_id}:{provider}"
            cached_hash = await self.cache_manager.get(cache_key)
            if cached_hash:
                return cached_hash

            # 从数据库获取（这里需要实际的数据库查询）
            # 模拟返回哈希值
            import hashlib
            mock_key = f"current_key_{user_id}_{provider}"
            key_hash = hashlib.sha256(mock_key.encode()).hexdigest()
            
            # 缓存结果
            await self.cache_manager.set(cache_key, key_hash, ttl=3600)
            return key_hash

        except Exception as e:
            logger.error(f"获取当前密钥哈希失败: {e}")
            return None

    def _hash_key(self, key: str) -> str:
        """计算密钥哈希值"""
        import hashlib
        return hashlib.sha256(key.encode()).hexdigest()

    async def _update_key_in_database(self, user_id: str, provider: str, encrypted_key: str):
        """更新数据库中的密钥"""
        # 这里应该实际更新数据库
        # 目前只是模拟
        logger.info(f"更新数据库中的密钥: user_id={user_id}, provider={provider}")

    async def _backup_old_key(self, rotation_record: RotationRecord):
        """备份旧密钥"""
        backup_data = {
            "rotation_id": rotation_record.id,
            "user_id": rotation_record.user_id,
            "provider": rotation_record.provider,
            "old_key_hash": rotation_record.old_key_hash,
            "backup_time": datetime.now().isoformat()
        }
        
        # 保存到备份存储
        backup_file = Path(f"backups/keys/{rotation_record.id}.json")
        backup_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(backup_file, 'w') as f:
            json.dump(backup_data, f, indent=2)
        
        logger.info(f"已备份旧密钥: {backup_file}")

    async def _clear_key_cache(self, user_id: str, provider: str):
        """清除密钥相关缓存"""
        cache_keys = [
            f"key_hash:{user_id}:{provider}",
            f"api_key:{user_id}:{provider}",
            f"key_config:{user_id}:{provider}"
        ]
        
        for cache_key in cache_keys:
            await self.cache_manager.delete(cache_key)

    def _get_rotation_policy(self, user_id: str) -> Dict:
        """获取用户的轮换策略"""
        # 这里应该从数据库获取用户的安全策略
        # 目前返回默认策略
        return self.rotation_policies["default"]

    async def _send_rotation_notification(self, rotation_record: RotationRecord):
        """发送轮换通知"""
        # 这里应该发送邮件或推送通知
        logger.info(f"发送轮换通知: {rotation_record.id}")

    async def get_rotation_status(self, rotation_id: str) -> Optional[RotationRecord]:
        """获取轮换状态"""
        return self.rotation_records.get(rotation_id)

    async def cancel_rotation(self, rotation_id: str, user_id: str) -> bool:
        """取消密钥轮换"""
        try:
            rotation_record = self.rotation_records.get(rotation_id)
            if not rotation_record:
                return False

            if rotation_record.user_id != user_id:
                raise PermissionError("无权限取消此轮换")

            if rotation_record.status == RotationStatus.IN_PROGRESS:
                raise ValueError("轮换正在进行中，无法取消")

            if rotation_record.status == RotationStatus.COMPLETED:
                raise ValueError("轮换已完成，无法取消")

            rotation_record.status = RotationStatus.CANCELLED
            rotation_record.completed_at = datetime.now()

            # 记录审计日志
            await self.audit_logger.log_security_event(
                user_id=user_id,
                event_type="key_rotation_cancelled",
                details={"rotation_id": rotation_id}
            )

            logger.info(f"已取消密钥轮换: {rotation_id}")
            return True

        except Exception as e:
            logger.error(f"取消密钥轮换失败: {e}")
            raise

    async def get_rotation_history(
        self,
        user_id: str,
        provider: Optional[str] = None,
        limit: int = 50
    ) -> List[RotationRecord]:
        """获取轮换历史"""
        records = []
        for record in self.rotation_records.values():
            if record.user_id == user_id:
                if provider is None or record.provider == provider:
                    records.append(record)

        # 按创建时间倒序排列
        records.sort(key=lambda x: x.created_at, reverse=True)
        return records[:limit]

    async def check_keys_need_rotation(self) -> List[Dict]:
        """检查需要轮换的密钥"""
        # 这里应该查询数据库中的所有密钥
        # 检查哪些密钥需要轮换
        keys_need_rotation = []
        
        # 模拟检查逻辑
        logger.info("检查需要轮换的密钥...")
        
        return keys_need_rotation

    async def auto_rotate_expired_keys(self):
        """自动轮换过期密钥"""
        try:
            keys_to_rotate = await self.check_keys_need_rotation()
            
            for key_info in keys_to_rotate:
                await self.schedule_rotation(
                    user_id=key_info["user_id"],
                    provider=key_info["provider"],
                    reason=RotationReason.SCHEDULED,
                    metadata={"auto_rotation": True}
                )
            
            logger.info(f"已安排 {len(keys_to_rotate)} 个密钥的自动轮换")

        except Exception as e:
            logger.error(f"自动轮换密钥失败: {e}")


# 全局密钥轮换管理器实例
_key_rotation_manager = None


def get_key_rotation_manager() -> KeyRotationManager:
    """获取密钥轮换管理器实例"""
    global _key_rotation_manager
    if _key_rotation_manager is None:
        _key_rotation_manager = KeyRotationManager()
    return _key_rotation_manager
