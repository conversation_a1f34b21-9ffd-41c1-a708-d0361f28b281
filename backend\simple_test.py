#!/usr/bin/env python3
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔐 测试加密功能...")

try:
    from app.utils.encryption import APIKeyEncryption
    
    enc = APIKeyEncryption()
    test_key = "sk-test123456789"
    
    print(f"原始密钥: {test_key}")
    
    # 加密
    encrypted = enc.encrypt_api_key(test_key)
    print(f"加密后: {encrypted[:20]}...")
    
    # 解密
    decrypted = enc.decrypt_api_key(encrypted)
    print(f"解密后: {decrypted}")
    
    # 验证
    if test_key == decrypted:
        print("✅ 加密解密测试通过!")
    else:
        print("❌ 加密解密测试失败!")
        
    # 脱敏测试
    masked = enc.mask_api_key(test_key)
    print(f"脱敏显示: {masked}")
    
    print("🎉 加密功能正常工作!")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n💾 测试缓存功能...")

try:
    import asyncio
    from app.utils.cache import CacheManager
    
    async def test_cache():
        cache = CacheManager()
        await cache.connect()
        
        # 基本测试
        await cache.set("test", {"data": "hello"}, 60)
        result = await cache.get("test")
        
        if result and result.get("data") == "hello":
            print("✅ 缓存基本功能测试通过!")
        else:
            print("❌ 缓存基本功能测试失败!")
        
        await cache.disconnect()
        print("🎉 缓存功能正常工作!")
    
    asyncio.run(test_cache())
    
except Exception as e:
    print(f"❌ 缓存测试失败: {e}")

print("\n📊 测试完成!")
