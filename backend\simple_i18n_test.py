#!/usr/bin/env python3
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🌍 测试国际化功能...")

try:
    from app.utils.i18n import get_i18n_manager, SupportedLanguage, t, set_language
    
    print("✅ 国际化模块导入成功")
    
    # 获取管理器
    i18n_manager = get_i18n_manager()
    print(f"✅ 国际化管理器初始化成功")
    
    # 测试支持的语言
    languages = i18n_manager.get_supported_languages()
    print(f"✅ 支持的语言: {list(languages.keys())}")
    
    # 测试翻译文件目录
    print(f"✅ 翻译文件目录: {i18n_manager.translations_dir}")
    
    # 测试基本翻译
    set_language(SupportedLanguage.CHINESE)
    chinese_text = t("common.success")
    print(f"✅ 中文翻译: common.success = '{chinese_text}'")
    
    set_language(SupportedLanguage.ENGLISH)
    english_text = t("common.success")
    print(f"✅ 英文翻译: common.success = '{english_text}'")
    
    # 测试嵌套键
    auth_login = t("auth.login")
    print(f"✅ 嵌套键翻译: auth.login = '{auth_login}'")
    
    # 测试不存在的键
    non_existent = t("non.existent.key")
    print(f"✅ 不存在的键: non.existent.key = '{non_existent}'")
    
    print("🎉 国际化基础功能测试完成!")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

print("🌍 测试结束")
