"""
安全中间件
集成安全监控、访问控制和威胁检测功能
"""

import time
import json
from datetime import datetime
from typing import Optional, Dict, Any
from urllib.parse import urlparse

from fastapi import Request, Response, HTTPException, status
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger

from ..utils.security_monitor import get_security_monitor
from ..utils.access_control import get_access_control_manager, Permission
from ..utils.audit_logger import get_audit_logger, AuditEventType, AuditLevel
from ..auth.jwt_handler import jwt_handler


class SecurityMiddleware(BaseHTTPMiddleware):
    """安全中间件"""

    def __init__(self, app):
        super().__init__(app)
        self.security_monitor = get_security_monitor()
        self.access_control = get_access_control_manager()
        self.audit_logger = get_audit_logger()
        
        # 需要权限检查的端点映射
        self.endpoint_permissions = {
            # 用户管理
            "GET:/api/users": Permission.USER_READ,
            "POST:/api/users": Permission.USER_WRITE,
            "PUT:/api/users": Permission.USER_WRITE,
            "DELETE:/api/users": Permission.USER_DELETE,
            
            # 交易管理
            "GET:/api/transactions": Permission.TRANSACTION_READ,
            "POST:/api/transactions": Permission.TRANSACTION_WRITE,
            "PUT:/api/transactions": Permission.TRANSACTION_WRITE,
            "DELETE:/api/transactions": Permission.TRANSACTION_DELETE,
            "POST:/api/transactions/bulk": Permission.TRANSACTION_BULK,
            
            # 类别管理
            "GET:/api/categories": Permission.CATEGORY_READ,
            "POST:/api/categories": Permission.CATEGORY_WRITE,
            "PUT:/api/categories": Permission.CATEGORY_WRITE,
            "DELETE:/api/categories": Permission.CATEGORY_DELETE,
            
            # 预算管理
            "GET:/api/budgets": Permission.BUDGET_READ,
            "POST:/api/budgets": Permission.BUDGET_WRITE,
            "PUT:/api/budgets": Permission.BUDGET_WRITE,
            "DELETE:/api/budgets": Permission.BUDGET_DELETE,
            
            # AI功能
            "POST:/api/ai/categorize": Permission.AI_USE,
            "POST:/api/ai/config": Permission.AI_CONFIG,
            "GET:/api/ai/providers": Permission.AI_USE,
            
            # 分析功能
            "GET:/api/analytics": Permission.ANALYTICS_READ,
            "GET:/api/analytics/advanced": Permission.ANALYTICS_ADVANCED,
            
            # 系统管理
            "GET:/api/system/config": Permission.SYSTEM_CONFIG,
            "POST:/api/system/config": Permission.SYSTEM_CONFIG,
            "GET:/api/system/admin": Permission.SYSTEM_ADMIN,
            
            # API密钥管理
            "GET:/api/api-keys": Permission.API_KEY_READ,
            "POST:/api/api-keys": Permission.API_KEY_WRITE,
            "PUT:/api/api-keys": Permission.API_KEY_WRITE,
            "DELETE:/api/api-keys": Permission.API_KEY_DELETE,
            
            # 安全管理
            "POST:/api/security/key-rotation": Permission.API_KEY_ROTATE,
            "GET:/api/security/audit": Permission.SYSTEM_AUDIT,
            "GET:/api/security/monitor": Permission.SYSTEM_AUDIT,
        }
        
        # 公开端点（不需要认证）
        self.public_endpoints = {
            "/",
            "/health",
            "/api/v1/auth/login",
            "/api/v1/auth/register",
            "/api/v1/auth/refresh",
            "/docs",
            "/openapi.json",
            "/redoc",
            "/favicon.ico"
        }
        
        # 管理员端点（需要管理员权限）
        self.admin_endpoints = {
            "/api/security/audit/search",
            "/api/security/monitor/alerts",
            "/api/security/access/assign-role",
            "/api/system/admin"
        }

    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        start_time = time.time()
        
        try:
            # 获取客户端信息
            client_ip = self._get_client_ip(request)
            user_agent = request.headers.get("user-agent", "")
            
            # 检查IP是否被阻止
            if await self.security_monitor.is_ip_blocked(client_ip):
                return JSONResponse(
                    status_code=status.HTTP_403_FORBIDDEN,
                    content={
                        "success": False,
                        "message": "IP地址已被阻止",
                        "error_code": "IP_BLOCKED"
                    }
                )
            
            # 记录请求
            await self.security_monitor.record_request(
                ip_address=client_ip,
                endpoint=str(request.url.path),
                method=request.method,
                user_agent=user_agent
            )
            
            # 检查是否为公开端点
            if self._is_public_endpoint(request.url.path):
                response = await call_next(request)
                await self._log_request(request, response, start_time, None)
                return response
            
            # 获取用户信息
            user_id = await self._get_user_from_request(request)
            if not user_id:
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={
                        "success": False,
                        "message": "未授权访问",
                        "error_code": "UNAUTHORIZED"
                    }
                )
            
            # 检查权限
            if not await self._check_permissions(request, user_id):
                # 记录权限拒绝事件
                await self.audit_logger.log_security_event(
                    user_id=user_id,
                    event_type="permission_denied",
                    details={
                        "endpoint": str(request.url.path),
                        "method": request.method,
                        "ip_address": client_ip,
                        "user_agent": user_agent
                    },
                    level=AuditLevel.MEDIUM
                )
                
                return JSONResponse(
                    status_code=status.HTTP_403_FORBIDDEN,
                    content={
                        "success": False,
                        "message": "权限不足",
                        "error_code": "PERMISSION_DENIED"
                    }
                )
            
            # 处理请求
            response = await call_next(request)
            
            # 记录请求日志
            await self._log_request(request, response, start_time, user_id)
            
            return response
            
        except Exception as e:
            logger.error(f"安全中间件处理请求失败: {e}")
            
            # 记录错误
            await self.audit_logger.log_event(
                event_type=AuditEventType.SYSTEM_ERROR,
                level=AuditLevel.HIGH,
                action="middleware_error",
                details={
                    "error": str(e),
                    "endpoint": str(request.url.path),
                    "method": request.method
                }
            )
            
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "success": False,
                    "message": "服务器内部错误",
                    "error_code": "INTERNAL_ERROR"
                }
            )

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # 使用客户端IP
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"

    def _is_public_endpoint(self, path: str) -> bool:
        """检查是否为公开端点"""
        # 精确匹配
        if path in self.public_endpoints:
            return True
        
        # 前缀匹配
        public_prefixes = ["/docs", "/redoc", "/openapi.json", "/static"]
        for prefix in public_prefixes:
            if path.startswith(prefix):
                return True
        
        return False

    async def _get_user_from_request(self, request: Request) -> Optional[str]:
        """从请求中获取用户ID"""
        try:
            # 从Authorization头获取token
            auth_header = request.headers.get("authorization")
            if not auth_header or not auth_header.startswith("Bearer "):
                return None
            
            token = auth_header.split(" ")[1]
            
            # 解码JWT token
            payload = jwt_handler.decode_token(token)
            if not payload:
                return None
            
            return payload.get("sub")  # 用户ID通常存储在sub字段
            
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            return None

    async def _check_permissions(self, request: Request, user_id: str) -> bool:
        """检查用户权限"""
        try:
            # 构建端点键
            endpoint_key = f"{request.method}:{request.url.path}"
            
            # 检查是否需要特定权限
            required_permission = self.endpoint_permissions.get(endpoint_key)
            if required_permission:
                return await self.access_control.has_permission(user_id, required_permission)
            
            # 检查管理员端点
            if request.url.path in self.admin_endpoints:
                return await self.access_control.has_permission(user_id, Permission.SYSTEM_ADMIN)
            
            # 检查资源访问权限
            if await self._is_resource_endpoint(request):
                return await self._check_resource_permissions(request, user_id)
            
            # 默认允许访问（对于未明确定义权限的端点）
            return True
            
        except Exception as e:
            logger.error(f"检查权限失败: {e}")
            return False

    async def _is_resource_endpoint(self, request: Request) -> bool:
        """检查是否为资源端点"""
        path = request.url.path
        
        # 检查是否包含资源ID
        resource_patterns = [
            "/api/transactions/",
            "/api/categories/",
            "/api/budgets/",
            "/api/users/"
        ]
        
        for pattern in resource_patterns:
            if pattern in path and path != pattern.rstrip("/"):
                return True
        
        return False

    async def _check_resource_permissions(self, request: Request, user_id: str) -> bool:
        """检查资源权限"""
        try:
            path = request.url.path
            method = request.method
            
            # 解析资源类型和ID
            if "/transactions/" in path:
                resource_type = "transaction"
                action = self._method_to_action(method)
            elif "/categories/" in path:
                resource_type = "category"
                action = self._method_to_action(method)
            elif "/budgets/" in path:
                resource_type = "budget"
                action = self._method_to_action(method)
            elif "/users/" in path:
                resource_type = "user"
                action = self._method_to_action(method)
            else:
                return True
            
            # 提取资源ID
            path_parts = path.split("/")
            resource_id = None
            for i, part in enumerate(path_parts):
                if part == resource_type + "s" and i + 1 < len(path_parts):
                    resource_id = path_parts[i + 1]
                    break
            
            if not resource_id:
                return True
            
            # 检查资源访问权限
            return await self.access_control.check_resource_access(
                user_id=user_id,
                resource_type=resource_type,
                resource_id=resource_id,
                action=action
            )
            
        except Exception as e:
            logger.error(f"检查资源权限失败: {e}")
            return False

    def _method_to_action(self, method: str) -> str:
        """将HTTP方法转换为操作"""
        method_mapping = {
            "GET": "read",
            "POST": "write",
            "PUT": "write",
            "PATCH": "write",
            "DELETE": "delete"
        }
        return method_mapping.get(method, "read")

    async def _log_request(
        self,
        request: Request,
        response: Response,
        start_time: float,
        user_id: Optional[str]
    ):
        """记录请求日志"""
        try:
            duration = time.time() - start_time
            
            # 构建日志数据
            log_data = {
                "method": request.method,
                "path": str(request.url.path),
                "query_params": dict(request.query_params),
                "status_code": response.status_code,
                "duration_ms": round(duration * 1000, 2),
                "user_id": user_id,
                "ip_address": self._get_client_ip(request),
                "user_agent": request.headers.get("user-agent", ""),
                "timestamp": datetime.now().isoformat()
            }
            
            # 记录到审计日志
            await self.audit_logger.log_event(
                event_type=AuditEventType.API_ACCESS,
                level=AuditLevel.LOW,
                user_id=user_id,
                action="api_request",
                details=log_data
            )
            
            # 如果是错误响应，提升日志级别
            if response.status_code >= 400:
                await self.audit_logger.log_event(
                    event_type=AuditEventType.API_ERROR,
                    level=AuditLevel.MEDIUM,
                    user_id=user_id,
                    action="api_error",
                    details=log_data
                )
            
        except Exception as e:
            logger.error(f"记录请求日志失败: {e}")


class RateLimitMiddleware(BaseHTTPMiddleware):
    """请求频率限制中间件"""

    def __init__(self, app):
        super().__init__(app)
        self.security_monitor = get_security_monitor()

    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        try:
            client_ip = self._get_client_ip(request)
            
            # 检查请求频率
            if await self._is_rate_limited(client_ip, request):
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={
                        "success": False,
                        "message": "请求过于频繁，请稍后再试",
                        "error_code": "RATE_LIMITED"
                    },
                    headers={"Retry-After": "60"}
                )
            
            return await call_next(request)
            
        except Exception as e:
            logger.error(f"频率限制中间件错误: {e}")
            return await call_next(request)

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"

    async def _is_rate_limited(self, client_ip: str, request: Request) -> bool:
        """检查是否被频率限制"""
        try:
            # 这里可以实现更复杂的频率限制逻辑
            # 目前简化实现，依赖安全监控器的检查
            return False
            
        except Exception as e:
            logger.error(f"检查频率限制失败: {e}")
            return False


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""

    async def dispatch(self, request: Request, call_next):
        """添加安全头"""
        response = await call_next(request)
        
        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = "default-src 'self'"
        
        return response
