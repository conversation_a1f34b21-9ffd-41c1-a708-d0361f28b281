"""
警报模型
"""

from sqlalchemy import Column, String, Float, DateTime, Text, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime

from ..database.base import Base


class Alert(Base):
    """警报模型"""
    __tablename__ = "alerts"
    
    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False)
    
    # 警报信息
    alert_type = Column(String(50), nullable=False)  # 警报类型
    severity = Column(String(20), nullable=False)    # 严重程度
    title = Column(String(200), nullable=False)      # 警报标题
    description = Column(Text, nullable=False)       # 警报描述
    
    # 相关信息
    transaction_id = Column(String(36), ForeignKey("transactions.id"), nullable=True)
    category_id = Column(String(36), ForeignKey("categories.id"), nullable=True)
    amount = Column(Float, nullable=True)
    
    # 状态信息
    is_read = Column(Boolean, default=False)         # 是否已读
    is_resolved = Column(Boolean, default=False)     # 是否已解决
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    resolved_at = Column(DateTime, nullable=True)
    
    # 关系
    user = relationship("User", back_populates="alerts")
    transaction = relationship("Transaction", backref="alerts")
    category = relationship("Category", backref="alerts")
