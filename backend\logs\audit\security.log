[2025-07-30T09:37:18.239967] HIGH: {
  "id": "4d3c4df7c37ad3e15f1825be3575dc0d",
  "timestamp": "2025-07-30T09:37:18.239967",
  "event_type": "system_error",
  "level": "high",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "middleware_error",
  "details": {
    "error": "'coroutine' object has no attribute 'get'",
    "endpoint": "/health",
    "method": "GET"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
[2025-07-30T09:42:35.485797] HIGH: {
  "id": "********************************",
  "timestamp": "2025-07-30T09:42:35.485797",
  "event_type": "system_error",
  "level": "high",
  "user_id": null,
  "session_id": null,
  "ip_address": null,
  "user_agent": null,
  "resource": null,
  "action": "middleware_error",
  "details": {
    "error": "'coroutine' object has no attribute 'get'",
    "endpoint": "/health",
    "method": "GET"
  },
  "result": "success",
  "error_message": null,
  "metadata": null
}
