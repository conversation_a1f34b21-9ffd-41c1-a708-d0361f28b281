import 'app_localizations.dart';

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  // Common
  @override
  String get success => '成功';

  @override
  String get error => '错误';

  @override
  String get warning => '警告';

  @override
  String get info => '信息';

  @override
  String get loading => '加载中...';

  @override
  String get save => '保存';

  @override
  String get cancel => '取消';

  @override
  String get confirm => '确认';

  @override
  String get delete => '删除';

  @override
  String get edit => '编辑';

  @override
  String get add => '添加';

  @override
  String get search => '搜索';

  @override
  String get filter => '筛选';

  @override
  String get export => '导出';

  @override
  String get import => '导入';

  @override
  String get refresh => '刷新';

  @override
  String get back => '返回';

  @override
  String get next => '下一步';

  @override
  String get previous => '上一步';

  @override
  String get submit => '提交';

  @override
  String get reset => '重置';

  // Auth
  @override
  String get login => '登录';

  @override
  String get logout => '退出登录';

  @override
  String get register => '注册';

  @override
  String get email => '邮箱';

  @override
  String get password => '密码';

  @override
  String get username => '用户名';

  @override
  String get loginSuccess => '登录成功';

  @override
  String get loginFailed => '登录失败';

  @override
  String get registerSuccess => '注册成功';

  @override
  String get registerFailed => '注册失败';

  @override
  String get invalidCredentials => '用户名或密码错误';

  @override
  String get emailAlreadyExists => '邮箱已存在';

  @override
  String get usernameAlreadyExists => '用户名已存在';

  // Transaction
  @override
  String get transaction => '交易';

  @override
  String get transactions => '交易记录';

  @override
  String get amount => '金额';

  @override
  String get description => '描述';

  @override
  String get category => '类别';

  @override
  String get date => '日期';

  @override
  String get type => '类型';

  @override
  String get income => '收入';

  @override
  String get expense => '支出';

  @override
  String get addTransaction => '添加交易';

  @override
  String get editTransaction => '编辑交易';

  @override
  String get deleteTransaction => '删除交易';

  @override
  String get transactionAdded => '交易添加成功';

  @override
  String get transactionUpdated => '交易更新成功';

  @override
  String get transactionDeleted => '交易删除成功';

  // Category
  @override
  String get categories => '类别管理';

  @override
  String get categoryName => '类别名称';

  @override
  String get categoryColor => '类别颜色';

  @override
  String get categoryType => '类别类型';

  @override
  String get addCategory => '添加类别';

  @override
  String get editCategory => '编辑类别';

  @override
  String get deleteCategory => '删除类别';

  @override
  String get categoryAdded => '类别添加成功';

  @override
  String get categoryUpdated => '类别更新成功';

  @override
  String get categoryDeleted => '类别删除成功';

  // Budget
  @override
  String get budget => '预算';

  @override
  String get budgets => '预算管理';

  @override
  String get budgetAmount => '预算金额';

  @override
  String get spentAmount => '已花费金额';

  @override
  String get remainingAmount => '剩余金额';

  @override
  String get budgetPeriod => '预算周期';

  @override
  String get monthly => '月度';

  @override
  String get yearly => '年度';

  @override
  String get addBudget => '添加预算';

  @override
  String get editBudget => '编辑预算';

  @override
  String get deleteBudget => '删除预算';

  @override
  String get budgetExceeded => '预算超支';

  @override
  String get budgetWarning => '预算预警';

  // AI
  @override
  String get aiCategorization => 'AI智能分类';

  @override
  String get aiConfig => 'AI配置';

  @override
  String get apiKey => 'API密钥';

  @override
  String get modelName => '模型名称';

  @override
  String get provider => '服务商';

  @override
  String get categorize => '分类';

  @override
  String get categorizing => '分类中...';

  @override
  String get categorizationSuccess => '分类成功';

  @override
  String get categorizationFailed => '分类失败';

  @override
  String get invalidApiKey => '无效的API密钥';

  @override
  String get aiServiceUnavailable => 'AI服务不可用';

  // Analytics
  @override
  String get analytics => '数据分析';

  @override
  String get spendingPatterns => '支出模式';

  @override
  String get budgetRecommendations => '预算建议';

  @override
  String get financialHealth => '财务健康';

  @override
  String get trends => '趋势分析';

  @override
  String get insights => '智能洞察';

  @override
  String get predictions => '支出预测';

  @override
  String get comparison => '对比分析';

  @override
  String get healthScore => '健康评分';

  @override
  String get excellent => '优秀';

  @override
  String get good => '良好';

  @override
  String get average => '一般';

  @override
  String get needsImprovement => '需改进';

  // Errors
  @override
  String get networkError => '网络错误';

  @override
  String get serverError => '服务器错误';

  @override
  String get validationError => '验证错误';

  @override
  String get permissionDenied => '权限不足';

  @override
  String get notFound => '未找到';

  @override
  String get rateLimitExceeded => '请求频率超限';

  @override
  String get insufficientData => '数据不足';

  @override
  String get operationFailed => '操作失败';

  // Security
  @override
  String get securityDashboard => '安全仪表板';

  @override
  String get overview => '概览';

  @override
  String get alerts => '警报';

  @override
  String get auditLog => '审计日志';

  @override
  String get keyRotation => '密钥轮换';

  @override
  String get securityStatus => '安全状态';

  @override
  String get active => '活跃';

  @override
  String get inactive => '非活跃';

  @override
  String get blockedIPs => '被阻止的IP';

  @override
  String get suspiciousIPs => '可疑IP';

  @override
  String get totalAlerts => '总警报数';

  @override
  String get alertsByThreatLevel => '按威胁级别分类的警报';

  @override
  String get critical => '严重';

  @override
  String get high => '高';

  @override
  String get medium => '中';

  @override
  String get low => '低';

  @override
  String get recentAlerts => '最近警报';

  @override
  String get viewAll => '查看全部';

  @override
  String get noRecentAlerts => '没有最近的警报';

  @override
  String get noSecurityAlerts => '没有安全警报';

  @override
  String get scheduleRotation => '安排轮换';
}
