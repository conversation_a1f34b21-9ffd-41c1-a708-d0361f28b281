import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_zh.dart';

/// Callers can lookup localized strings with Localizations.of<AppLocalizations>(context)!.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationsDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml file to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you'll need to edit this
/// file.
///
/// First, open your project's ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project's Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('zh')
  ];

  // Common
  String get success;
  String get error;
  String get warning;
  String get info;
  String get loading;
  String get save;
  String get cancel;
  String get confirm;
  String get delete;
  String get edit;
  String get add;
  String get search;
  String get filter;
  String get export;
  String get import;
  String get refresh;
  String get back;
  String get next;
  String get previous;
  String get submit;
  String get reset;

  // Auth
  String get login;
  String get logout;
  String get register;
  String get email;
  String get password;
  String get username;
  String get loginSuccess;
  String get loginFailed;
  String get registerSuccess;
  String get registerFailed;
  String get invalidCredentials;
  String get emailAlreadyExists;
  String get usernameAlreadyExists;

  // Transaction
  String get transaction;
  String get transactions;
  String get amount;
  String get description;
  String get category;
  String get date;
  String get type;
  String get income;
  String get expense;
  String get addTransaction;
  String get editTransaction;
  String get deleteTransaction;
  String get transactionAdded;
  String get transactionUpdated;
  String get transactionDeleted;

  // Category
  String get categories;
  String get categoryName;
  String get categoryColor;
  String get categoryType;
  String get addCategory;
  String get editCategory;
  String get deleteCategory;
  String get categoryAdded;
  String get categoryUpdated;
  String get categoryDeleted;

  // Budget
  String get budget;
  String get budgets;
  String get budgetAmount;
  String get spentAmount;
  String get remainingAmount;
  String get budgetPeriod;
  String get monthly;
  String get yearly;
  String get addBudget;
  String get editBudget;
  String get deleteBudget;
  String get budgetExceeded;
  String get budgetWarning;

  // AI
  String get aiCategorization;
  String get aiConfig;
  String get apiKey;
  String get modelName;
  String get provider;
  String get categorize;
  String get categorizing;
  String get categorizationSuccess;
  String get categorizationFailed;
  String get invalidApiKey;
  String get aiServiceUnavailable;

  // Analytics
  String get analytics;
  String get spendingPatterns;
  String get budgetRecommendations;
  String get financialHealth;
  String get trends;
  String get insights;
  String get predictions;
  String get comparison;
  String get healthScore;
  String get excellent;
  String get good;
  String get average;
  String get needsImprovement;

  // Errors
  String get networkError;
  String get serverError;
  String get validationError;
  String get permissionDenied;
  String get notFound;
  String get rateLimitExceeded;
  String get insufficientData;
  String get operationFailed;

  // Security
  String get securityDashboard;
  String get overview;
  String get alerts;
  String get auditLog;
  String get keyRotation;
  String get securityStatus;
  String get active;
  String get inactive;
  String get blockedIPs;
  String get suspiciousIPs;
  String get totalAlerts;
  String get alertsByThreatLevel;
  String get critical;
  String get high;
  String get medium;
  String get low;
  String get recentAlerts;
  String get viewAll;
  String get noRecentAlerts;
  String get noSecurityAlerts;
  String get scheduleRotation;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'zh': return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue on GitHub with a '
    'reproducible example of the issue and the full stacktrace.'
  );
}
