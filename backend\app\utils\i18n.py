"""
国际化(i18n)支持模块
提供多语言文本翻译和本地化功能
"""

import json
import os
from typing import Dict, Any, Optional
from enum import Enum
from pathlib import Path
from loguru import logger


class SupportedLanguage(str, Enum):
    """支持的语言"""
    CHINESE = "zh-CN"
    ENGLISH = "en-US"
    TRADITIONAL_CHINESE = "zh-TW"


class I18nManager:
    """国际化管理器"""
    
    def __init__(self):
        self.translations: Dict[str, Dict[str, Any]] = {}
        self.default_language = SupportedLanguage.CHINESE
        self.current_language = SupportedLanguage.CHINESE
        self.translations_dir = Path(__file__).parent.parent / "translations"
        
        # 确保翻译目录存在
        self.translations_dir.mkdir(exist_ok=True)
        
        # 加载所有翻译文件
        self._load_translations()
    
    def _load_translations(self):
        """加载翻译文件"""
        try:
            for language in SupportedLanguage:
                translation_file = self.translations_dir / f"{language.value}.json"
                
                if translation_file.exists():
                    with open(translation_file, 'r', encoding='utf-8') as f:
                        self.translations[language.value] = json.load(f)
                        logger.info(f"已加载 {language.value} 翻译文件")
                else:
                    # 创建默认翻译文件
                    self._create_default_translation_file(language.value)
                    
        except Exception as e:
            logger.error(f"加载翻译文件失败: {e}")
    
    def _create_default_translation_file(self, language: str):
        """创建默认翻译文件"""
        try:
            default_translations = self._get_default_translations(language)
            
            translation_file = self.translations_dir / f"{language}.json"
            with open(translation_file, 'w', encoding='utf-8') as f:
                json.dump(default_translations, f, ensure_ascii=False, indent=2)
            
            self.translations[language] = default_translations
            logger.info(f"已创建默认翻译文件: {language}")
            
        except Exception as e:
            logger.error(f"创建默认翻译文件失败: {e}")
            self.translations[language] = {}
    
    def _get_default_translations(self, language: str) -> Dict[str, Any]:
        """获取默认翻译内容"""
        if language == SupportedLanguage.CHINESE:
            return {
                "common": {
                    "success": "成功",
                    "error": "错误",
                    "warning": "警告",
                    "info": "信息",
                    "loading": "加载中...",
                    "save": "保存",
                    "cancel": "取消",
                    "confirm": "确认",
                    "delete": "删除",
                    "edit": "编辑",
                    "add": "添加",
                    "search": "搜索",
                    "filter": "筛选",
                    "export": "导出",
                    "import": "导入",
                    "refresh": "刷新",
                    "back": "返回",
                    "next": "下一步",
                    "previous": "上一步",
                    "submit": "提交",
                    "reset": "重置"
                },
                "auth": {
                    "login": "登录",
                    "logout": "退出登录",
                    "register": "注册",
                    "email": "邮箱",
                    "password": "密码",
                    "username": "用户名",
                    "login_success": "登录成功",
                    "login_failed": "登录失败",
                    "register_success": "注册成功",
                    "register_failed": "注册失败",
                    "invalid_credentials": "用户名或密码错误",
                    "email_already_exists": "邮箱已存在",
                    "username_already_exists": "用户名已存在"
                },
                "transaction": {
                    "transaction": "交易",
                    "transactions": "交易记录",
                    "amount": "金额",
                    "description": "描述",
                    "category": "类别",
                    "date": "日期",
                    "type": "类型",
                    "income": "收入",
                    "expense": "支出",
                    "add_transaction": "添加交易",
                    "edit_transaction": "编辑交易",
                    "delete_transaction": "删除交易",
                    "transaction_added": "交易添加成功",
                    "transaction_updated": "交易更新成功",
                    "transaction_deleted": "交易删除成功"
                },
                "category": {
                    "category": "类别",
                    "categories": "类别管理",
                    "category_name": "类别名称",
                    "category_color": "类别颜色",
                    "category_type": "类别类型",
                    "add_category": "添加类别",
                    "edit_category": "编辑类别",
                    "delete_category": "删除类别",
                    "category_added": "类别添加成功",
                    "category_updated": "类别更新成功",
                    "category_deleted": "类别删除成功"
                },
                "budget": {
                    "budget": "预算",
                    "budgets": "预算管理",
                    "budget_amount": "预算金额",
                    "spent_amount": "已花费金额",
                    "remaining_amount": "剩余金额",
                    "budget_period": "预算周期",
                    "monthly": "月度",
                    "yearly": "年度",
                    "add_budget": "添加预算",
                    "edit_budget": "编辑预算",
                    "delete_budget": "删除预算",
                    "budget_exceeded": "预算超支",
                    "budget_warning": "预算预警"
                },
                "ai": {
                    "ai_categorization": "AI智能分类",
                    "ai_config": "AI配置",
                    "api_key": "API密钥",
                    "model_name": "模型名称",
                    "provider": "服务商",
                    "categorize": "分类",
                    "categorizing": "分类中...",
                    "categorization_success": "分类成功",
                    "categorization_failed": "分类失败",
                    "invalid_api_key": "无效的API密钥",
                    "ai_service_unavailable": "AI服务不可用"
                },
                "analytics": {
                    "analytics": "数据分析",
                    "spending_patterns": "支出模式",
                    "budget_recommendations": "预算建议",
                    "financial_health": "财务健康",
                    "trends": "趋势分析",
                    "insights": "智能洞察",
                    "predictions": "支出预测",
                    "comparison": "对比分析",
                    "health_score": "健康评分",
                    "excellent": "优秀",
                    "good": "良好",
                    "average": "一般",
                    "needs_improvement": "需改进"
                },
                "errors": {
                    "network_error": "网络错误",
                    "server_error": "服务器错误",
                    "validation_error": "验证错误",
                    "permission_denied": "权限不足",
                    "not_found": "未找到",
                    "rate_limit_exceeded": "请求频率超限",
                    "insufficient_data": "数据不足",
                    "operation_failed": "操作失败"
                }
            }
        
        elif language == SupportedLanguage.ENGLISH:
            return {
                "common": {
                    "success": "Success",
                    "error": "Error",
                    "warning": "Warning",
                    "info": "Information",
                    "loading": "Loading...",
                    "save": "Save",
                    "cancel": "Cancel",
                    "confirm": "Confirm",
                    "delete": "Delete",
                    "edit": "Edit",
                    "add": "Add",
                    "search": "Search",
                    "filter": "Filter",
                    "export": "Export",
                    "import": "Import",
                    "refresh": "Refresh",
                    "back": "Back",
                    "next": "Next",
                    "previous": "Previous",
                    "submit": "Submit",
                    "reset": "Reset"
                },
                "auth": {
                    "login": "Login",
                    "logout": "Logout",
                    "register": "Register",
                    "email": "Email",
                    "password": "Password",
                    "username": "Username",
                    "login_success": "Login successful",
                    "login_failed": "Login failed",
                    "register_success": "Registration successful",
                    "register_failed": "Registration failed",
                    "invalid_credentials": "Invalid username or password",
                    "email_already_exists": "Email already exists",
                    "username_already_exists": "Username already exists"
                },
                "transaction": {
                    "transaction": "Transaction",
                    "transactions": "Transactions",
                    "amount": "Amount",
                    "description": "Description",
                    "category": "Category",
                    "date": "Date",
                    "type": "Type",
                    "income": "Income",
                    "expense": "Expense",
                    "add_transaction": "Add Transaction",
                    "edit_transaction": "Edit Transaction",
                    "delete_transaction": "Delete Transaction",
                    "transaction_added": "Transaction added successfully",
                    "transaction_updated": "Transaction updated successfully",
                    "transaction_deleted": "Transaction deleted successfully"
                },
                "category": {
                    "category": "Category",
                    "categories": "Categories",
                    "category_name": "Category Name",
                    "category_color": "Category Color",
                    "category_type": "Category Type",
                    "add_category": "Add Category",
                    "edit_category": "Edit Category",
                    "delete_category": "Delete Category",
                    "category_added": "Category added successfully",
                    "category_updated": "Category updated successfully",
                    "category_deleted": "Category deleted successfully"
                },
                "budget": {
                    "budget": "Budget",
                    "budgets": "Budgets",
                    "budget_amount": "Budget Amount",
                    "spent_amount": "Spent Amount",
                    "remaining_amount": "Remaining Amount",
                    "budget_period": "Budget Period",
                    "monthly": "Monthly",
                    "yearly": "Yearly",
                    "add_budget": "Add Budget",
                    "edit_budget": "Edit Budget",
                    "delete_budget": "Delete Budget",
                    "budget_exceeded": "Budget Exceeded",
                    "budget_warning": "Budget Warning"
                },
                "ai": {
                    "ai_categorization": "AI Categorization",
                    "ai_config": "AI Configuration",
                    "api_key": "API Key",
                    "model_name": "Model Name",
                    "provider": "Provider",
                    "categorize": "Categorize",
                    "categorizing": "Categorizing...",
                    "categorization_success": "Categorization successful",
                    "categorization_failed": "Categorization failed",
                    "invalid_api_key": "Invalid API key",
                    "ai_service_unavailable": "AI service unavailable"
                },
                "analytics": {
                    "analytics": "Analytics",
                    "spending_patterns": "Spending Patterns",
                    "budget_recommendations": "Budget Recommendations",
                    "financial_health": "Financial Health",
                    "trends": "Trends",
                    "insights": "Insights",
                    "predictions": "Predictions",
                    "comparison": "Comparison",
                    "health_score": "Health Score",
                    "excellent": "Excellent",
                    "good": "Good",
                    "average": "Average",
                    "needs_improvement": "Needs Improvement"
                },
                "errors": {
                    "network_error": "Network Error",
                    "server_error": "Server Error",
                    "validation_error": "Validation Error",
                    "permission_denied": "Permission Denied",
                    "not_found": "Not Found",
                    "rate_limit_exceeded": "Rate Limit Exceeded",
                    "insufficient_data": "Insufficient Data",
                    "operation_failed": "Operation Failed"
                }
            }
        
        elif language == SupportedLanguage.TRADITIONAL_CHINESE:
            return {
                "common": {
                    "success": "成功",
                    "error": "錯誤",
                    "warning": "警告",
                    "info": "資訊",
                    "loading": "載入中...",
                    "save": "儲存",
                    "cancel": "取消",
                    "confirm": "確認",
                    "delete": "刪除",
                    "edit": "編輯",
                    "add": "新增",
                    "search": "搜尋",
                    "filter": "篩選",
                    "export": "匯出",
                    "import": "匯入",
                    "refresh": "重新整理",
                    "back": "返回",
                    "next": "下一步",
                    "previous": "上一步",
                    "submit": "提交",
                    "reset": "重置"
                },
                "auth": {
                    "login": "登入",
                    "logout": "登出",
                    "register": "註冊",
                    "email": "電子郵件",
                    "password": "密碼",
                    "username": "使用者名稱",
                    "login_success": "登入成功",
                    "login_failed": "登入失敗",
                    "register_success": "註冊成功",
                    "register_failed": "註冊失敗",
                    "invalid_credentials": "使用者名稱或密碼錯誤",
                    "email_already_exists": "電子郵件已存在",
                    "username_already_exists": "使用者名稱已存在"
                }
                # 其他翻译内容类似...
            }
        
        return {}
    
    def set_language(self, language: SupportedLanguage):
        """设置当前语言"""
        if language.value in self.translations:
            self.current_language = language
            logger.info(f"语言已切换到: {language.value}")
        else:
            logger.warning(f"不支持的语言: {language.value}")
    
    def get_text(self, key: str, language: Optional[SupportedLanguage] = None, **kwargs) -> str:
        """获取翻译文本"""
        lang = language.value if language else self.current_language.value
        
        try:
            # 支持嵌套键，如 "auth.login"
            keys = key.split('.')
            text = self.translations.get(lang, {})
            
            for k in keys:
                if isinstance(text, dict) and k in text:
                    text = text[k]
                else:
                    # 回退到默认语言
                    text = self.translations.get(self.default_language.value, {})
                    for k in keys:
                        if isinstance(text, dict) and k in text:
                            text = text[k]
                        else:
                            return key  # 如果都找不到，返回原始键
                    break
            
            # 如果找到的是字符串，进行参数替换
            if isinstance(text, str) and kwargs:
                try:
                    text = text.format(**kwargs)
                except (KeyError, ValueError):
                    pass
            
            return text if isinstance(text, str) else key
            
        except Exception as e:
            logger.error(f"获取翻译文本失败: {e}")
            return key
    
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        return {
            SupportedLanguage.CHINESE: "简体中文",
            SupportedLanguage.ENGLISH: "English",
            SupportedLanguage.TRADITIONAL_CHINESE: "繁體中文"
        }
    
    def add_translation(self, language: SupportedLanguage, key: str, value: str):
        """添加翻译"""
        try:
            if language.value not in self.translations:
                self.translations[language.value] = {}
            
            # 支持嵌套键
            keys = key.split('.')
            current = self.translations[language.value]
            
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
            
            current[keys[-1]] = value
            
            # 保存到文件
            self._save_translation_file(language.value)
            
        except Exception as e:
            logger.error(f"添加翻译失败: {e}")
    
    def _save_translation_file(self, language: str):
        """保存翻译文件"""
        try:
            translation_file = self.translations_dir / f"{language}.json"
            with open(translation_file, 'w', encoding='utf-8') as f:
                json.dump(self.translations[language], f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存翻译文件失败: {e}")


# 全局实例
_i18n_manager = None

def get_i18n_manager() -> I18nManager:
    """获取国际化管理器实例"""
    global _i18n_manager
    if _i18n_manager is None:
        _i18n_manager = I18nManager()
    return _i18n_manager

def t(key: str, language: Optional[SupportedLanguage] = None, **kwargs) -> str:
    """翻译函数的简写"""
    return get_i18n_manager().get_text(key, language, **kwargs)

def set_language(language: SupportedLanguage):
    """设置语言的简写"""
    get_i18n_manager().set_language(language)
