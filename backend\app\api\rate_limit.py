"""
限流管理API
提供限流状态查询和管理功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from ..database.connection import get_db
from ..models.user import User
from ..auth.dependencies import get_current_user, get_current_admin_user
from ..utils.rate_limiter import get_rate_limiter, RateLimit, RateLimitResult
from ..middleware.rate_limit import ai_request_limit, api_call_limit

router = APIRouter(prefix="/rate-limit", tags=["限流管理"])


class RateLimitConfig(BaseModel):
    """限流配置模型"""
    limit: int = Field(..., ge=1, description="限制次数")
    window: int = Field(..., ge=1, description="时间窗口（秒）")
    burst_limit: Optional[int] = Field(None, ge=1, description="突发限制")
    burst_window: Optional[int] = Field(None, ge=1, description="突发时间窗口（秒）")


class RateLimitStatus(BaseModel):
    """限流状态模型"""
    limit_type: str
    limit: int
    remaining: int
    used: int
    reset_time: datetime
    window_seconds: int


@router.get("/status")
async def get_rate_limit_status(
    current_user: User = Depends(get_current_user),
    request: Request = None
):
    """获取当前用户的限流状态"""
    try:
        limiter = await get_rate_limiter()
        user_id = str(current_user.id)
        
        # 获取用户相关的限流类型
        limit_types = [
            "user_ai_requests",
            "user_api_calls", 
            "feedback_submit",
            "config_update",
            "batch_operations"
        ]
        
        status_list = []
        for limit_type in limit_types:
            try:
                info = await limiter.get_limit_info(user_id, limit_type)
                if "error" not in info:
                    status_list.append(RateLimitStatus(
                        limit_type=limit_type,
                        limit=info["limit"],
                        remaining=info["remaining"],
                        used=info["used"],
                        reset_time=datetime.fromisoformat(info["reset_time"]),
                        window_seconds=info["window_seconds"]
                    ))
            except Exception as e:
                continue
        
        return JSONResponse(content={
            "user_id": user_id,
            "limits": [status.dict() for status in status_list],
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取限流状态失败: {str(e)}"
        )


@router.get("/status/{limit_type}")
async def get_specific_rate_limit_status(
    limit_type: str,
    current_user: User = Depends(get_current_user)
):
    """获取特定类型的限流状态"""
    try:
        limiter = await get_rate_limiter()
        user_id = str(current_user.id)
        
        info = await limiter.get_limit_info(user_id, limit_type)
        
        if "error" in info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未知的限流类型: {limit_type}"
            )
        
        return JSONResponse(content={
            "limit_type": limit_type,
            "user_id": user_id,
            **info
        })
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取限流状态失败: {str(e)}"
        )


@router.get("/ip-status")
async def get_ip_rate_limit_status(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """获取当前IP的限流状态"""
    try:
        limiter = await get_rate_limiter()
        
        # 获取客户端IP
        client_ip = request.client.host if hasattr(request.client, "host") else "unknown"
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        # IP相关的限流类型
        ip_limit_types = ["ip_requests", "ip_burst"]
        
        status_list = []
        for limit_type in ip_limit_types:
            try:
                info = await limiter.get_limit_info(client_ip, limit_type)
                if "error" not in info:
                    status_list.append({
                        "limit_type": limit_type,
                        **info
                    })
            except Exception:
                continue
        
        return JSONResponse(content={
            "client_ip": client_ip,
            "limits": status_list,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取IP限流状态失败: {str(e)}"
        )


@router.post("/test-limit")
@api_call_limit
async def test_rate_limit(
    current_user: User = Depends(get_current_user)
):
    """测试限流功能"""
    return JSONResponse(content={
        "message": "限流测试成功",
        "user_id": str(current_user.id),
        "timestamp": datetime.now().isoformat()
    })


@router.post("/admin/reset/{user_id}/{limit_type}")
async def reset_user_rate_limit(
    user_id: str,
    limit_type: str,
    current_admin: User = Depends(get_current_admin_user)
):
    """重置用户的限流计数（管理员功能）"""
    try:
        limiter = await get_rate_limiter()
        success = await limiter.reset_limit(user_id, limit_type)
        
        if success:
            return JSONResponse(content={
                "message": f"成功重置用户 {user_id} 的 {limit_type} 限流计数",
                "admin_id": str(current_admin.id),
                "timestamp": datetime.now().isoformat()
            })
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="重置限流计数失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重置限流计数失败: {str(e)}"
        )


@router.post("/admin/config/{limit_type}")
async def update_rate_limit_config(
    limit_type: str,
    config: RateLimitConfig,
    current_admin: User = Depends(get_current_admin_user)
):
    """更新限流配置（管理员功能）"""
    try:
        limiter = await get_rate_limiter()
        
        # 创建新的限流配置
        rate_limit = RateLimit(
            limit=config.limit,
            window=config.window,
            burst_limit=config.burst_limit,
            burst_window=config.burst_window
        )
        
        # 添加自定义限流规则
        limiter.add_custom_limit(limit_type, rate_limit)
        
        return JSONResponse(content={
            "message": f"成功更新 {limit_type} 的限流配置",
            "config": {
                "limit": config.limit,
                "window": config.window,
                "burst_limit": config.burst_limit,
                "burst_window": config.burst_window
            },
            "admin_id": str(current_admin.id),
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新限流配置失败: {str(e)}"
        )


@router.get("/admin/global-stats")
async def get_global_rate_limit_stats(
    current_admin: User = Depends(get_current_admin_user)
):
    """获取全局限流统计（管理员功能）"""
    try:
        limiter = await get_rate_limiter()
        stats = await limiter.get_global_stats()
        
        return JSONResponse(content={
            "global_stats": stats,
            "admin_id": str(current_admin.id),
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取全局统计失败: {str(e)}"
        )


@router.get("/admin/active-limits")
async def get_active_rate_limits(
    current_admin: User = Depends(get_current_admin_user)
):
    """获取当前活跃的限流记录（管理员功能）"""
    try:
        limiter = await get_rate_limiter()
        
        # 获取内存存储中的活跃限制
        active_limits = []
        for cache_key, windows in limiter.memory_store.items():
            for window_key, requests in windows.items():
                if requests:  # 有活跃请求
                    active_limits.append({
                        "cache_key": cache_key,
                        "window_key": window_key,
                        "request_count": len(requests),
                        "latest_request": max(requests) if requests else None
                    })
        
        return JSONResponse(content={
            "active_limits": active_limits,
            "total_active": len(active_limits),
            "admin_id": str(current_admin.id),
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取活跃限流记录失败: {str(e)}"
        )


@router.delete("/admin/clear-all")
async def clear_all_rate_limits(
    current_admin: User = Depends(get_current_admin_user)
):
    """清除所有限流记录（管理员功能）"""
    try:
        limiter = await get_rate_limiter()
        
        # 清除内存存储
        limiter.memory_store.clear()
        
        # 尝试清除Redis缓存
        try:
            if limiter.cache and hasattr(limiter.cache, 'redis') and limiter.cache.redis:
                # 删除所有rate_limit:*的键
                keys = await limiter.cache.redis.keys("rate_limit:*")
                if keys:
                    await limiter.cache.redis.delete(*keys)
        except Exception:
            pass
        
        return JSONResponse(content={
            "message": "成功清除所有限流记录",
            "admin_id": str(current_admin.id),
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清除限流记录失败: {str(e)}"
        )


@router.get("/health")
async def rate_limit_health_check():
    """限流系统健康检查"""
    try:
        limiter = await get_rate_limiter()
        
        # 测试基本功能
        test_result = await limiter.check_rate_limit("health_check", "user_api_calls")
        
        health_status = {
            "status": "healthy",
            "limiter_initialized": limiter is not None,
            "cache_connected": limiter.cache is not None,
            "memory_store_keys": len(limiter.memory_store),
            "test_check_passed": test_result.allowed,
            "timestamp": datetime.now().isoformat()
        }
        
        return JSONResponse(content=health_status)
        
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )
