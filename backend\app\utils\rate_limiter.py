"""
API限流和请求节流工具
防止API滥用和保护系统资源
"""

import time
import asyncio
from typing import Dict, Optional, Tuple, Any
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass
from enum import Enum
import hashlib
import json
from loguru import logger

from .cache import get_cache_manager


class RateLimitType(Enum):
    """限流类型"""
    PER_SECOND = "per_second"
    PER_MINUTE = "per_minute"
    PER_HOUR = "per_hour"
    PER_DAY = "per_day"
    BURST = "burst"  # 突发限制


@dataclass
class RateLimit:
    """限流配置"""
    limit: int  # 限制次数
    window: int  # 时间窗口（秒）
    burst_limit: Optional[int] = None  # 突发限制
    burst_window: Optional[int] = None  # 突发时间窗口


@dataclass
class RateLimitResult:
    """限流结果"""
    allowed: bool
    remaining: int
    reset_time: datetime
    retry_after: Optional[int] = None
    limit_type: Optional[str] = None


class RateLimiter:
    """API限流器"""
    
    def __init__(self):
        self.cache = None
        self.memory_store = defaultdict(lambda: defaultdict(deque))
        self.last_cleanup = time.time()
        
        # 默认限流配置
        self.default_limits = {
            # 用户级别限制
            "user_ai_requests": RateLimit(100, 3600),  # 每小时100次AI请求
            "user_api_calls": RateLimit(1000, 3600),   # 每小时1000次API调用
            "user_login": RateLimit(10, 300),          # 5分钟内10次登录尝试
            
            # IP级别限制
            "ip_requests": RateLimit(500, 3600),       # 每小时500次请求
            "ip_burst": RateLimit(20, 60),             # 每分钟20次突发请求
            
            # 全局限制
            "global_ai_requests": RateLimit(10000, 3600),  # 全局每小时10000次AI请求
            
            # 特定功能限制
            "feedback_submit": RateLimit(50, 3600),    # 每小时50次反馈提交
            "config_update": RateLimit(20, 3600),      # 每小时20次配置更新
            "batch_operations": RateLimit(10, 3600),   # 每小时10次批量操作
        }
    
    async def init_cache(self):
        """初始化缓存连接"""
        if not self.cache:
            self.cache = await get_cache_manager()
    
    def _get_cache_key(self, identifier: str, limit_type: str) -> str:
        """生成缓存键"""
        return f"rate_limit:{limit_type}:{identifier}"
    
    def _get_window_key(self, timestamp: float, window: int) -> int:
        """获取时间窗口键"""
        return int(timestamp // window)
    
    async def _get_request_count(self, cache_key: str, window_key: int) -> int:
        """获取请求计数"""
        await self.init_cache()
        
        try:
            # 尝试从Redis获取
            if self.cache and hasattr(self.cache, 'redis') and self.cache.redis:
                count = await self.cache.redis.hget(cache_key, str(window_key))
                return int(count) if count else 0
        except Exception:
            pass
        
        # 使用内存存储
        return len(self.memory_store[cache_key][window_key])
    
    async def _increment_request_count(self, cache_key: str, window_key: int, ttl: int) -> int:
        """增加请求计数"""
        await self.init_cache()
        
        try:
            # 尝试使用Redis
            if self.cache and hasattr(self.cache, 'redis') and self.cache.redis:
                pipe = self.cache.redis.pipeline()
                pipe.hincrby(cache_key, str(window_key), 1)
                pipe.expire(cache_key, ttl)
                results = await pipe.execute()
                return results[0]
        except Exception:
            pass
        
        # 使用内存存储
        current_time = time.time()
        self.memory_store[cache_key][window_key].append(current_time)
        
        # 清理过期数据
        self._cleanup_memory_store()
        
        return len(self.memory_store[cache_key][window_key])
    
    def _cleanup_memory_store(self):
        """清理内存存储中的过期数据"""
        current_time = time.time()
        
        # 每5分钟清理一次
        if current_time - self.last_cleanup < 300:
            return
        
        self.last_cleanup = current_time
        
        # 清理过期的时间窗口
        for cache_key in list(self.memory_store.keys()):
            for window_key in list(self.memory_store[cache_key].keys()):
                # 清理1小时前的数据
                if window_key < int((current_time - 3600) // 60):
                    del self.memory_store[cache_key][window_key]
            
            # 如果缓存键为空，删除它
            if not self.memory_store[cache_key]:
                del self.memory_store[cache_key]
    
    async def check_rate_limit(
        self,
        identifier: str,
        limit_type: str,
        custom_limit: Optional[RateLimit] = None
    ) -> RateLimitResult:
        """检查限流状态"""
        
        # 获取限流配置
        rate_limit = custom_limit or self.default_limits.get(limit_type)
        if not rate_limit:
            # 如果没有配置，默认允许
            return RateLimitResult(
                allowed=True,
                remaining=999,
                reset_time=datetime.now() + timedelta(hours=1)
            )
        
        current_time = time.time()
        window_key = self._get_window_key(current_time, rate_limit.window)
        cache_key = self._get_cache_key(identifier, limit_type)
        
        # 获取当前请求计数
        current_count = await self._get_request_count(cache_key, window_key)
        
        # 检查是否超过限制
        if current_count >= rate_limit.limit:
            # 计算重置时间
            reset_time = datetime.fromtimestamp(
                (window_key + 1) * rate_limit.window
            )
            retry_after = int((reset_time - datetime.now()).total_seconds())
            
            return RateLimitResult(
                allowed=False,
                remaining=0,
                reset_time=reset_time,
                retry_after=max(1, retry_after),
                limit_type=limit_type
            )
        
        # 增加请求计数
        new_count = await self._increment_request_count(
            cache_key, window_key, rate_limit.window + 60
        )
        
        # 计算剩余次数
        remaining = max(0, rate_limit.limit - new_count)
        
        # 计算重置时间
        reset_time = datetime.fromtimestamp(
            (window_key + 1) * rate_limit.window
        )
        
        return RateLimitResult(
            allowed=True,
            remaining=remaining,
            reset_time=reset_time,
            limit_type=limit_type
        )
    
    async def check_multiple_limits(
        self,
        identifier: str,
        limit_types: list[str],
        custom_limits: Optional[Dict[str, RateLimit]] = None
    ) -> Dict[str, RateLimitResult]:
        """检查多个限流规则"""
        results = {}
        
        for limit_type in limit_types:
            custom_limit = custom_limits.get(limit_type) if custom_limits else None
            result = await self.check_rate_limit(identifier, limit_type, custom_limit)
            results[limit_type] = result
        
        return results
    
    async def is_allowed(
        self,
        identifier: str,
        limit_type: str,
        custom_limit: Optional[RateLimit] = None
    ) -> bool:
        """简单的限流检查，只返回是否允许"""
        result = await self.check_rate_limit(identifier, limit_type, custom_limit)
        return result.allowed
    
    async def get_limit_info(
        self,
        identifier: str,
        limit_type: str
    ) -> Dict[str, Any]:
        """获取限流信息（不增加计数）"""
        rate_limit = self.default_limits.get(limit_type)
        if not rate_limit:
            return {"error": "Unknown limit type"}
        
        current_time = time.time()
        window_key = self._get_window_key(current_time, rate_limit.window)
        cache_key = self._get_cache_key(identifier, limit_type)
        
        current_count = await self._get_request_count(cache_key, window_key)
        remaining = max(0, rate_limit.limit - current_count)
        
        reset_time = datetime.fromtimestamp(
            (window_key + 1) * rate_limit.window
        )
        
        return {
            "limit": rate_limit.limit,
            "remaining": remaining,
            "used": current_count,
            "reset_time": reset_time.isoformat(),
            "window_seconds": rate_limit.window
        }
    
    async def reset_limit(self, identifier: str, limit_type: str) -> bool:
        """重置特定的限流计数"""
        await self.init_cache()
        
        try:
            # 从Redis删除
            if self.cache and hasattr(self.cache, 'redis') and self.cache.redis:
                cache_key = self._get_cache_key(identifier, limit_type)
                await self.cache.redis.delete(cache_key)
            
            # 从内存删除
            cache_key = self._get_cache_key(identifier, limit_type)
            if cache_key in self.memory_store:
                del self.memory_store[cache_key]
            
            logger.info(f"重置限流计数: {identifier} - {limit_type}")
            return True
            
        except Exception as e:
            logger.error(f"重置限流计数失败: {e}")
            return False
    
    def add_custom_limit(self, limit_type: str, rate_limit: RateLimit):
        """添加自定义限流规则"""
        self.default_limits[limit_type] = rate_limit
        logger.info(f"添加自定义限流规则: {limit_type} - {rate_limit.limit}/{rate_limit.window}s")
    
    async def get_global_stats(self) -> Dict[str, Any]:
        """获取全局限流统计"""
        stats = {
            "total_limit_types": len(self.default_limits),
            "memory_store_keys": len(self.memory_store),
            "limit_configurations": {}
        }
        
        for limit_type, rate_limit in self.default_limits.items():
            stats["limit_configurations"][limit_type] = {
                "limit": rate_limit.limit,
                "window": rate_limit.window,
                "burst_limit": rate_limit.burst_limit,
                "burst_window": rate_limit.burst_window
            }
        
        return stats


# 全局限流器实例
_rate_limiter = None


async def get_rate_limiter() -> RateLimiter:
    """获取全局限流器实例"""
    global _rate_limiter
    if _rate_limiter is None:
        _rate_limiter = RateLimiter()
        await _rate_limiter.init_cache()
    return _rate_limiter


# 便捷函数
async def check_rate_limit(identifier: str, limit_type: str) -> RateLimitResult:
    """检查限流状态的便捷函数"""
    limiter = await get_rate_limiter()
    return await limiter.check_rate_limit(identifier, limit_type)


async def is_rate_limited(identifier: str, limit_type: str) -> bool:
    """检查是否被限流的便捷函数"""
    limiter = await get_rate_limiter()
    result = await limiter.check_rate_limit(identifier, limit_type)
    return not result.allowed
