import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import 'dart:convert';
import '../services/batch_service.dart';
import '../widgets/loading_overlay.dart';
import '../utils/snackbar_utils.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class BatchManagementScreen extends StatefulWidget {
  const BatchManagementScreen({Key? key}) : super(key: key);

  @override
  State<BatchManagementScreen> createState() => _BatchManagementScreenState();
}

class _BatchManagementScreenState extends State<BatchManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final BatchService _batchService = BatchService();
  
  List<BatchJob> _jobs = [];
  bool _isLoading = false;
  String? _selectedJobId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadBatchJobs();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadBatchJobs() async {
    setState(() => _isLoading = true);
    try {
      final jobs = await _batchService.getBatchJobs(limit: 50);
      setState(() => _jobs = jobs);
    } catch (e) {
      if (mounted) {
        SnackbarUtils.showError(context, '加载任务列表失败: $e');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.batchManagement ?? '批处理管理'),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: l10n.importData ?? '数据导入'),
            Tab(text: l10n.exportData ?? '数据导出'),
            Tab(text: l10n.taskList ?? '任务列表'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadBatchJobs,
          ),
        ],
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildImportTab(context, l10n),
            _buildExportTab(context, l10n),
            _buildTaskListTab(context, l10n),
          ],
        ),
      ),
    );
  }

  Widget _buildImportTab(BuildContext context, AppLocalizations l10n) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.csvImport ?? 'CSV导入',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    l10n.csvImportDescription ?? '从CSV文件导入交易数据，支持自定义分隔符和编码格式',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _importFromCsv(context),
                          icon: const Icon(Icons.upload_file),
                          label: Text(l10n.selectCsvFile ?? '选择CSV文件'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      OutlinedButton.icon(
                        onPressed: () => _showCsvTemplate(context),
                        icon: const Icon(Icons.download),
                        label: Text(l10n.downloadTemplate ?? '下载模板'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.jsonImport ?? 'JSON导入',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    l10n.jsonImportDescription ?? '从JSON文件导入交易数据，支持完整的数据结构',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _importFromJson(context),
                          icon: const Icon(Icons.upload_file),
                          label: Text(l10n.selectJsonFile ?? '选择JSON文件'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      OutlinedButton.icon(
                        onPressed: () => _showJsonTemplate(context),
                        icon: const Icon(Icons.download),
                        label: Text(l10n.downloadTemplate ?? '下载模板'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExportTab(BuildContext context, AppLocalizations l10n) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.exportOptions ?? '导出选项',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  _buildExportOptionsForm(context, l10n),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExportOptionsForm(BuildContext context, AppLocalizations l10n) {
    return Column(
      children: [
        // 日期范围选择
        Row(
          children: [
            Expanded(
              child: TextFormField(
                decoration: InputDecoration(
                  labelText: l10n.startDate ?? '开始日期',
                  suffixIcon: const Icon(Icons.calendar_today),
                ),
                readOnly: true,
                onTap: () => _selectDate(context, true),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                decoration: InputDecoration(
                  labelText: l10n.endDate ?? '结束日期',
                  suffixIcon: const Icon(Icons.calendar_today),
                ),
                readOnly: true,
                onTap: () => _selectDate(context, false),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        // 导出格式选择
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _exportToCsv(context),
                icon: const Icon(Icons.table_chart),
                label: Text(l10n.exportToCsv ?? '导出为CSV'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _exportToJson(context),
                icon: const Icon(Icons.code),
                label: Text(l10n.exportToJson ?? '导出为JSON'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTaskListTab(BuildContext context, AppLocalizations l10n) {
    if (_jobs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.task_alt,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              l10n.noTasks ?? '暂无任务',
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadBatchJobs,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _jobs.length,
        itemBuilder: (context, index) {
          final job = _jobs[index];
          return _buildJobCard(context, job, l10n);
        },
      ),
    );
  }

  Widget _buildJobCard(BuildContext context, BatchJob job, AppLocalizations l10n) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ExpansionTile(
        leading: _getStatusIcon(job.status),
        title: Text('任务 ${job.jobId.substring(0, 8)}...'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(job.statusText),
            if (job.status == BatchStatus.processing)
              LinearProgressIndicator(value: job.progress / 100),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildJobDetailRow('总数量', '${job.totalItems}'),
                _buildJobDetailRow('已处理', '${job.processedItems}'),
                _buildJobDetailRow('成功', '${job.successCount}'),
                _buildJobDetailRow('失败', '${job.errorCount}'),
                _buildJobDetailRow('成功率', '${job.successRate.toStringAsFixed(1)}%'),
                _buildJobDetailRow('创建时间', _formatDateTime(job.createdAt)),
                if (job.completedAt != null)
                  _buildJobDetailRow('完成时间', _formatDateTime(job.completedAt!)),
                if (job.duration != null)
                  _buildJobDetailRow('耗时', '${job.duration!.toStringAsFixed(1)}秒'),
                
                if (job.errors.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    '错误信息:',
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  const SizedBox(height: 4),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.errorContainer,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      job.errors.take(3).map((e) => e['error']).join('\n'),
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onErrorContainer,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
                
                const SizedBox(height: 16),
                Row(
                  children: [
                    if (job.status == BatchStatus.processing ||
                        job.status == BatchStatus.pending)
                      ElevatedButton.icon(
                        onPressed: () => _cancelJob(job.jobId),
                        icon: const Icon(Icons.cancel),
                        label: Text(l10n.cancel ?? '取消'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.error,
                          foregroundColor: Theme.of(context).colorScheme.onError,
                        ),
                      ),
                    const Spacer(),
                    TextButton.icon(
                      onPressed: () => _refreshJobStatus(job.jobId),
                      icon: const Icon(Icons.refresh),
                      label: Text(l10n.refresh ?? '刷新'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildJobDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _getStatusIcon(BatchStatus status) {
    switch (status) {
      case BatchStatus.pending:
        return const Icon(Icons.schedule, color: Colors.orange);
      case BatchStatus.processing:
        return const Icon(Icons.sync, color: Colors.blue);
      case BatchStatus.completed:
        return const Icon(Icons.check_circle, color: Colors.green);
      case BatchStatus.failed:
        return const Icon(Icons.error, color: Colors.red);
      case BatchStatus.cancelled:
        return const Icon(Icons.cancel, color: Colors.grey);
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _importFromCsv(BuildContext context) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (result != null) {
        File file = File(result.files.single.path!);

        // 验证文件内容
        String content = await file.readAsString();
        if (!_batchService.validateCsvFormat(content)) {
          if (mounted) {
            SnackbarUtils.showError(context, 'CSV文件格式不正确');
          }
          return;
        }

        setState(() => _isLoading = true);

        String jobId = await _batchService.importTransactionsFromCsv(file: file);

        if (mounted) {
          SnackbarUtils.showSuccess(context, '导入任务已创建: $jobId');
          _tabController.animateTo(2); // 切换到任务列表
          _loadBatchJobs();
        }
      }
    } catch (e) {
      if (mounted) {
        SnackbarUtils.showError(context, '导入失败: $e');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _importFromJson(BuildContext context) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result != null) {
        File file = File(result.files.single.path!);

        // 验证文件内容
        String content = await file.readAsString();
        if (!_batchService.validateJsonFormat(content)) {
          if (mounted) {
            SnackbarUtils.showError(context, 'JSON文件格式不正确');
          }
          return;
        }

        setState(() => _isLoading = true);

        String jobId = await _batchService.importTransactionsFromJson(file: file);

        if (mounted) {
          SnackbarUtils.showSuccess(context, '导入任务已创建: $jobId');
          _tabController.animateTo(2); // 切换到任务列表
          _loadBatchJobs();
        }
      }
    } catch (e) {
      if (mounted) {
        SnackbarUtils.showError(context, '导入失败: $e');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _exportToCsv(BuildContext context) async {
    try {
      setState(() => _isLoading = true);

      String csvContent = await _batchService.exportTransactionsToCsv();

      // 保存文件
      await _saveExportedFile(csvContent, 'transactions.csv');

      if (mounted) {
        SnackbarUtils.showSuccess(context, 'CSV导出成功');
      }
    } catch (e) {
      if (mounted) {
        SnackbarUtils.showError(context, '导出失败: $e');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _exportToJson(BuildContext context) async {
    try {
      setState(() => _isLoading = true);

      String jsonContent = await _batchService.exportTransactionsToJson();

      // 保存文件
      await _saveExportedFile(jsonContent, 'transactions.json');

      if (mounted) {
        SnackbarUtils.showSuccess(context, 'JSON导出成功');
      }
    } catch (e) {
      if (mounted) {
        SnackbarUtils.showError(context, '导出失败: $e');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveExportedFile(String content, String filename) async {
    // 这里可以实现文件保存逻辑
    // 例如使用 path_provider 保存到下载目录
    // 或者显示分享对话框让用户选择保存位置
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      // 这里可以保存选择的日期到状态变量
    }
  }

  Future<void> _showCsvTemplate(BuildContext context) async {
    String template = _batchService.getCsvTemplate();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('CSV模板'),
        content: SingleChildScrollView(
          child: SelectableText(template),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Clipboard.setData(ClipboardData(text: template));
              Navigator.of(context).pop();
              SnackbarUtils.showSuccess(context, '模板已复制到剪贴板');
            },
            child: const Text('复制'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Future<void> _showJsonTemplate(BuildContext context) async {
    String template = _batchService.getJsonTemplate();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('JSON模板'),
        content: SingleChildScrollView(
          child: SelectableText(template),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Clipboard.setData(ClipboardData(text: template));
              Navigator.of(context).pop();
              SnackbarUtils.showSuccess(context, '模板已复制到剪贴板');
            },
            child: const Text('复制'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelJob(String jobId) async {
    try {
      await _batchService.cancelBatchJob(jobId);
      SnackbarUtils.showSuccess(context, '任务已取消');
      _loadBatchJobs();
    } catch (e) {
      SnackbarUtils.showError(context, '取消任务失败: $e');
    }
  }

  Future<void> _refreshJobStatus(String jobId) async {
    try {
      final job = await _batchService.getBatchJobStatus(jobId);
      setState(() {
        int index = _jobs.indexWhere((j) => j.jobId == jobId);
        if (index != -1) {
          _jobs[index] = job;
        }
      });
    } catch (e) {
      SnackbarUtils.showError(context, '刷新状态失败: $e');
    }
  }
}
