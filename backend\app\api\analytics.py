"""
高级分析和洞察API
提供支出模式分析、预算建议、趋势预测等功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from decimal import Decimal
import asyncio

from ..database.connection import get_db
from ..models.user import User
from ..auth.dependencies import get_current_user
from ..services.analytics import get_analytics_service
from ..middleware.rate_limit import api_call_limit

router = APIRouter(prefix="/analytics", tags=["高级分析"])


class SpendingAnalysisRequest(BaseModel):
    """支出分析请求"""
    months: int = Field(default=6, ge=1, le=24, description="分析月数（1-24个月）")
    categories: Optional[List[str]] = Field(None, description="指定分析的类别")
    include_subcategories: bool = Field(default=True, description="是否包含子类别")


class BudgetRecommendationRequest(BaseModel):
    """预算建议请求"""
    analysis_months: int = Field(default=3, ge=1, le=12, description="分析基础月数")
    target_savings_rate: Optional[float] = Field(None, ge=0, le=1, description="目标储蓄率")
    risk_tolerance: str = Field(default="medium", description="风险承受度：low/medium/high")


class TrendPredictionRequest(BaseModel):
    """趋势预测请求"""
    prediction_months: int = Field(default=3, ge=1, le=12, description="预测月数")
    confidence_level: float = Field(default=0.8, ge=0.5, le=0.99, description="置信水平")


@router.get("/spending-patterns")
@api_call_limit
async def get_spending_patterns(
    months: int = Query(default=6, ge=1, le=24, description="分析月数"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户支出模式分析"""
    try:
        analytics_service = await get_analytics_service()
        
        analysis = await analytics_service.analyze_spending_patterns(
            user_id=str(current_user.id),
            db=db,
            months=months
        )
        
        return JSONResponse(content={
            "success": True,
            "data": analysis,
            "message": "支出模式分析完成"
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取支出模式分析失败: {str(e)}"
        )


@router.get("/budget-recommendations")
@api_call_limit
async def get_budget_recommendations(
    analysis_months: int = Query(default=3, ge=1, le=12, description="分析基础月数"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取预算建议"""
    try:
        analytics_service = await get_analytics_service()
        
        recommendations = await analytics_service.generate_budget_recommendations(
            user_id=str(current_user.id),
            db=db,
            analysis_months=analysis_months
        )
        
        return JSONResponse(content={
            "success": True,
            "data": recommendations,
            "message": "预算建议生成完成"
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取预算建议失败: {str(e)}"
        )


@router.get("/category-insights/{category_name}")
@api_call_limit
async def get_category_insights(
    category_name: str,
    months: int = Query(default=6, ge=1, le=24, description="分析月数"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取特定类别的深度分析"""
    try:
        analytics_service = await get_analytics_service()
        
        # 获取完整分析
        full_analysis = await analytics_service.analyze_spending_patterns(
            user_id=str(current_user.id),
            db=db,
            months=months
        )
        
        # 提取特定类别的数据
        category_data = None
        for cat_analysis in full_analysis.get("category_analysis", []):
            if cat_analysis["category"] == category_name:
                category_data = cat_analysis
                break
        
        if not category_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到类别 '{category_name}' 的数据"
            )
        
        # 生成类别特定的洞察
        insights = await _generate_category_insights(category_data, full_analysis)
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "category": category_name,
                "analysis_period": full_analysis["analysis_period"],
                "category_data": category_data,
                "insights": insights,
                "generated_at": datetime.now().isoformat()
            },
            "message": f"类别 '{category_name}' 分析完成"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取类别洞察失败: {str(e)}"
        )


@router.get("/spending-forecast")
@api_call_limit
async def get_spending_forecast(
    prediction_months: int = Query(default=3, ge=1, le=12, description="预测月数"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取支出预测"""
    try:
        analytics_service = await get_analytics_service()
        
        # 获取历史数据分析
        historical_analysis = await analytics_service.analyze_spending_patterns(
            user_id=str(current_user.id),
            db=db,
            months=6  # 使用6个月历史数据进行预测
        )
        
        if historical_analysis["total_transactions"] == 0:
            return JSONResponse(content={
                "success": True,
                "data": {
                    "forecast": [],
                    "message": "暂无足够历史数据进行预测"
                },
                "message": "支出预测完成"
            })
        
        # 生成预测
        forecast = await _generate_spending_forecast(
            historical_analysis, prediction_months
        )
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "prediction_months": prediction_months,
                "historical_period": "6个月",
                "forecast": forecast,
                "generated_at": datetime.now().isoformat()
            },
            "message": "支出预测完成"
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取支出预测失败: {str(e)}"
        )


@router.get("/financial-health-score")
@api_call_limit
async def get_financial_health_score(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取财务健康评分"""
    try:
        analytics_service = await get_analytics_service()
        
        # 获取分析数据
        spending_analysis = await analytics_service.analyze_spending_patterns(
            user_id=str(current_user.id),
            db=db,
            months=3
        )
        
        budget_recommendations = await analytics_service.generate_budget_recommendations(
            user_id=str(current_user.id),
            db=db,
            analysis_months=3
        )
        
        # 计算健康评分
        health_score = await _calculate_financial_health_score(
            spending_analysis, budget_recommendations
        )
        
        return JSONResponse(content={
            "success": True,
            "data": health_score,
            "message": "财务健康评分完成"
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取财务健康评分失败: {str(e)}"
        )


@router.get("/comparison-report")
@api_call_limit
async def get_comparison_report(
    compare_months: int = Query(default=3, ge=1, le=12, description="对比月数"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取同期对比报告"""
    try:
        analytics_service = await get_analytics_service()
        
        # 获取当前期间数据
        current_analysis = await analytics_service.analyze_spending_patterns(
            user_id=str(current_user.id),
            db=db,
            months=compare_months
        )
        
        # 获取上一期间数据（需要实现时间偏移）
        # 这里简化处理，实际应该获取对应的历史期间数据
        
        comparison_report = {
            "comparison_period": f"{compare_months}个月",
            "current_period": {
                "total_amount": current_analysis["total_amount"],
                "transaction_count": current_analysis["total_transactions"],
                "average_amount": current_analysis["average_amount"]
            },
            "previous_period": {
                "total_amount": 0,  # 需要实现历史数据获取
                "transaction_count": 0,
                "average_amount": 0
            },
            "changes": {
                "amount_change": 0,
                "count_change": 0,
                "average_change": 0
            },
            "insights": [
                "对比分析需要更多历史数据",
                "建议继续使用应用积累数据"
            ],
            "generated_at": datetime.now().isoformat()
        }
        
        return JSONResponse(content={
            "success": True,
            "data": comparison_report,
            "message": "对比报告生成完成"
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取对比报告失败: {str(e)}"
        )


async def _generate_category_insights(
    category_data: Dict[str, Any], 
    full_analysis: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """生成类别特定洞察"""
    insights = []
    
    # 支出占比洞察
    percentage = category_data["percentage"]
    if percentage > 30:
        insights.append({
            "type": "high_spending",
            "message": f"该类别占总支出的{percentage}%，属于主要支出类别",
            "suggestion": "建议重点关注此类别的预算控制"
        })
    elif percentage < 5:
        insights.append({
            "type": "low_spending",
            "message": f"该类别占总支出的{percentage}%，支出较少",
            "suggestion": "可以考虑适当增加此类别的预算"
        })
    
    # 频率洞察
    avg_amount = category_data["average_amount"]
    total_avg = full_analysis["average_amount"]
    
    if avg_amount > total_avg * 2:
        insights.append({
            "type": "high_value_transactions",
            "message": f"该类别单笔平均金额({avg_amount:.2f}元)较高",
            "suggestion": "建议在大额支出前进行预算检查"
        })
    elif avg_amount < total_avg * 0.5:
        insights.append({
            "type": "frequent_small_transactions",
            "message": f"该类别多为小额高频支出",
            "suggestion": "可以考虑批量处理或寻找优惠方案"
        })
    
    return insights


async def _generate_spending_forecast(
    historical_analysis: Dict[str, Any], 
    prediction_months: int
) -> List[Dict[str, Any]]:
    """生成支出预测"""
    forecast = []
    
    # 基于月度趋势进行简单预测
    monthly_trend = historical_analysis.get("monthly_trend", [])
    
    if len(monthly_trend) < 2:
        return [{
            "month": "预测数据不足",
            "predicted_amount": 0,
            "confidence": "low"
        }]
    
    # 计算平均月支出
    recent_amounts = [month["amount"] for month in monthly_trend[-3:]]
    avg_monthly = sum(recent_amounts) / len(recent_amounts)
    
    # 计算趋势
    trend_slope = historical_analysis.get("trend_analysis", {}).get("slope", 0)
    
    # 生成未来几个月的预测
    base_date = datetime.now().replace(day=1)
    
    for i in range(1, prediction_months + 1):
        future_date = base_date + timedelta(days=32 * i)
        future_month = future_date.strftime("%Y-%m")
        
        # 简单线性预测
        predicted_amount = avg_monthly + (trend_slope * i * 100)  # 放大趋势影响
        predicted_amount = max(0, predicted_amount)  # 确保非负
        
        # 置信度评估
        stability = historical_analysis.get("trend_analysis", {}).get("stability", "medium")
        confidence_map = {"high": "high", "medium": "medium", "low": "low"}
        confidence = confidence_map.get(stability, "medium")
        
        forecast.append({
            "month": future_month,
            "predicted_amount": round(predicted_amount, 2),
            "confidence": confidence,
            "factors": [
                f"基于{len(recent_amounts)}个月平均值",
                f"趋势调整: {trend_slope:.4f}"
            ]
        })
    
    return forecast


async def _calculate_financial_health_score(
    spending_analysis: Dict[str, Any],
    budget_recommendations: Dict[str, Any]
) -> Dict[str, Any]:
    """计算财务健康评分"""
    
    score = 100  # 基础分数
    factors = []
    
    # 支出稳定性评分 (30分)
    trend = spending_analysis.get("trend_analysis", {})
    stability = trend.get("stability", "medium")
    
    if stability == "high":
        stability_score = 30
        factors.append("支出模式稳定 (+30分)")
    elif stability == "medium":
        stability_score = 20
        factors.append("支出模式较稳定 (+20分)")
    else:
        stability_score = 10
        factors.append("支出模式不稳定 (+10分)")
    
    # 预算管理评分 (25分)
    recommendations = budget_recommendations.get("recommendations", [])
    high_priority_issues = len([r for r in recommendations if r.get("priority") == "high"])
    
    if high_priority_issues == 0:
        budget_score = 25
        factors.append("预算管理良好 (+25分)")
    elif high_priority_issues <= 2:
        budget_score = 15
        factors.append("预算管理一般 (+15分)")
    else:
        budget_score = 5
        factors.append("预算管理需改进 (+5分)")
    
    # 支出多样性评分 (20分)
    categories = spending_analysis.get("category_analysis", [])
    if len(categories) >= 5:
        diversity_score = 20
        factors.append("支出类别多样化 (+20分)")
    elif len(categories) >= 3:
        diversity_score = 15
        factors.append("支出类别较多样 (+15分)")
    else:
        diversity_score = 10
        factors.append("支出类别单一 (+10分)")
    
    # 异常支出评分 (25分)
    anomalies = spending_analysis.get("anomalies", [])
    if len(anomalies) == 0:
        anomaly_score = 25
        factors.append("无异常支出 (+25分)")
    elif len(anomalies) <= 3:
        anomaly_score = 15
        factors.append("少量异常支出 (+15分)")
    else:
        anomaly_score = 5
        factors.append("异常支出较多 (+5分)")
    
    total_score = stability_score + budget_score + diversity_score + anomaly_score
    
    # 评级
    if total_score >= 80:
        grade = "优秀"
        description = "财务管理状况良好"
    elif total_score >= 60:
        grade = "良好"
        description = "财务管理基本健康"
    elif total_score >= 40:
        grade = "一般"
        description = "财务管理有待改进"
    else:
        grade = "需改进"
        description = "建议加强财务管理"
    
    return {
        "total_score": total_score,
        "grade": grade,
        "description": description,
        "score_breakdown": {
            "stability": stability_score,
            "budget_management": budget_score,
            "spending_diversity": diversity_score,
            "anomaly_control": anomaly_score
        },
        "factors": factors,
        "recommendations": [
            "继续保持良好的记账习惯",
            "定期审查和调整预算",
            "关注支出趋势变化"
        ],
        "generated_at": datetime.now().isoformat()
    }


@router.get("/smart-insights")
@api_call_limit
async def get_smart_insights(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取智能洞察和建议"""
    try:
        analytics_service = await get_analytics_service()

        insights = await analytics_service.generate_smart_insights(
            user_id=str(current_user.id),
            db=db
        )

        return JSONResponse(content={
            "success": True,
            "data": insights,
            "message": "智能洞察生成完成"
        })

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取智能洞察失败: {str(e)}"
        )


@router.get("/spending-predictions")
@api_call_limit
async def get_spending_predictions(
    prediction_months: int = Query(default=3, ge=1, le=12, description="预测月数"),
    categories: Optional[str] = Query(None, description="指定类别（逗号分隔）"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取高级支出预测"""
    try:
        analytics_service = await get_analytics_service()

        # 处理类别过滤
        category_filter = None
        if categories:
            category_filter = [cat.strip() for cat in categories.split(",")]

        predictions = await analytics_service.predict_future_spending(
            user_id=str(current_user.id),
            db=db,
            prediction_months=prediction_months,
            category_filter=category_filter
        )

        return JSONResponse(content={
            "success": True,
            "data": predictions,
            "message": "支出预测完成"
        })

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取支出预测失败: {str(e)}"
        )


@router.get("/dashboard-summary")
@api_call_limit
async def get_dashboard_summary(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取仪表板摘要数据"""
    try:
        analytics_service = await get_analytics_service()

        # 并行获取多个分析结果
        spending_analysis, budget_recommendations, health_score, insights = await asyncio.gather(
            analytics_service.analyze_spending_patterns(str(current_user.id), db, 3),
            analytics_service.generate_budget_recommendations(str(current_user.id), db, 3),
            _calculate_financial_health_score_simple(str(current_user.id), db),
            analytics_service.generate_smart_insights(str(current_user.id), db)
        )

        # 构建仪表板摘要
        summary = {
            "overview": {
                "total_spending_3months": spending_analysis.get("total_amount", 0),
                "transaction_count": spending_analysis.get("total_transactions", 0),
                "average_daily_spending": round(
                    spending_analysis.get("total_amount", 0) / 90, 2
                ),
                "top_category": (
                    spending_analysis.get("category_analysis", [{}])[0].get("category", "无")
                    if spending_analysis.get("category_analysis") else "无"
                )
            },

            "trends": {
                "spending_trend": spending_analysis.get("trend_analysis", {}).get("trend", "stable"),
                "trend_description": spending_analysis.get("trend_analysis", {}).get("description", ""),
                "stability": spending_analysis.get("trend_analysis", {}).get("stability", "medium")
            },

            "alerts": {
                "high_priority_recommendations": len([
                    r for r in budget_recommendations.get("recommendations", [])
                    if r.get("priority") == "high"
                ]),
                "anomalies_count": len(spending_analysis.get("anomalies", [])),
                "urgent_insights": len([
                    i for i in insights.get("insights", [])
                    if i.get("priority") == "high"
                ])
            },

            "health_score": health_score,

            "quick_insights": insights.get("insights", [])[:3],  # 前3个洞察

            "generated_at": datetime.now().isoformat()
        }

        return JSONResponse(content={
            "success": True,
            "data": summary,
            "message": "仪表板摘要获取完成"
        })

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取仪表板摘要失败: {str(e)}"
        )


async def _calculate_financial_health_score_simple(user_id: str, db: AsyncSession) -> Dict[str, Any]:
    """简化的财务健康评分计算"""
    try:
        analytics_service = await get_analytics_service()

        spending_analysis = await analytics_service.analyze_spending_patterns(user_id, db, 3)
        budget_recommendations = await analytics_service.generate_budget_recommendations(user_id, db, 3)

        return await _calculate_financial_health_score(spending_analysis, budget_recommendations)
    except Exception:
        return {
            "total_score": 50,
            "grade": "一般",
            "description": "数据不足，无法准确评估"
        }
