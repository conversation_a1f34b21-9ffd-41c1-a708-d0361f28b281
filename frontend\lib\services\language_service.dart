import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/api_response.dart';
import 'api_service.dart';

/// 语言设置服务
class LanguageService {
  static const String _languageKey = 'selected_language';
  static const String _timezoneKey = 'selected_timezone';
  static const String _dateFormatKey = 'selected_date_format';
  static const String _currencyKey = 'selected_currency';

  final ApiService _apiService;
  SharedPreferences? _prefs;

  LanguageService(this._apiService);

  /// 初始化服务
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// 获取支持的语言列表
  Future<ApiResponse<Map<String, dynamic>>> getSupportedLanguages() async {
    try {
      final response = await _apiService.get('/i18n/languages');
      return ApiResponse.fromJson(response);
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: '获取支持语言列表失败: $e',
      );
    }
  }

  /// 设置语言偏好
  Future<ApiResponse<Map<String, dynamic>>> setLanguagePreference({
    required String language,
    String? timezone,
    String? dateFormat,
    String? currency,
  }) async {
    try {
      final requestData = {
        'language': language,
        'timezone': timezone ?? 'Asia/Shanghai',
        'date_format': dateFormat ?? 'YYYY-MM-DD',
        'currency': currency ?? 'CNY',
      };

      final response = await _apiService.post('/i18n/set-language', requestData);
      final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(response);

      if (apiResponse.success) {
        // 保存到本地存储
        await _saveLanguagePreference(language, timezone, dateFormat, currency);
      }

      return apiResponse;
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: '设置语言偏好失败: $e',
      );
    }
  }

  /// 获取当前语言设置
  Future<ApiResponse<Map<String, dynamic>>> getCurrentLanguage() async {
    try {
      final response = await _apiService.get('/i18n/current-language');
      return ApiResponse.fromJson(response);
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: '获取当前语言设置失败: $e',
      );
    }
  }

  /// 批量翻译文本
  Future<ApiResponse<Map<String, dynamic>>> translateTexts({
    required List<String> keys,
    String? language,
  }) async {
    try {
      final requestData = {
        'keys': keys,
        if (language != null) 'language': language,
      };

      final response = await _apiService.post('/i18n/translate', requestData);
      return ApiResponse.fromJson(response);
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: '批量翻译失败: $e',
      );
    }
  }

  /// 翻译单个文本
  Future<ApiResponse<Map<String, dynamic>>> translateSingleText({
    required String key,
    String? language,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (language != null) {
        queryParams['language'] = language;
      }

      final response = await _apiService.get('/i18n/translate/$key', queryParams: queryParams);
      return ApiResponse.fromJson(response);
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: '翻译文本失败: $e',
      );
    }
  }

  /// 获取本地化数据
  Future<ApiResponse<Map<String, dynamic>>> getLocalizationData({
    String? language,
    List<String>? sections,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (language != null) {
        queryParams['language'] = language;
      }
      if (sections != null && sections.isNotEmpty) {
        queryParams['sections'] = sections.join(',');
      }

      final response = await _apiService.get('/i18n/localization-data', queryParams: queryParams);
      return ApiResponse.fromJson(response);
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: '获取本地化数据失败: $e',
      );
    }
  }

  /// 从本地存储获取语言偏好
  Future<LanguagePreference> getLocalLanguagePreference() async {
    await _ensurePrefsInitialized();

    final language = _prefs!.getString(_languageKey) ?? _getSystemLanguage();
    final timezone = _prefs!.getString(_timezoneKey) ?? 'Asia/Shanghai';
    final dateFormat = _prefs!.getString(_dateFormatKey) ?? 'YYYY-MM-DD';
    final currency = _prefs!.getString(_currencyKey) ?? 'CNY';

    return LanguagePreference(
      language: language,
      timezone: timezone,
      dateFormat: dateFormat,
      currency: currency,
    );
  }

  /// 保存语言偏好到本地存储
  Future<void> _saveLanguagePreference(
    String language,
    String? timezone,
    String? dateFormat,
    String? currency,
  ) async {
    await _ensurePrefsInitialized();

    await _prefs!.setString(_languageKey, language);
    if (timezone != null) {
      await _prefs!.setString(_timezoneKey, timezone);
    }
    if (dateFormat != null) {
      await _prefs!.setString(_dateFormatKey, dateFormat);
    }
    if (currency != null) {
      await _prefs!.setString(_currencyKey, currency);
    }
  }

  /// 获取系统语言
  String _getSystemLanguage() {
    final systemLocale = PlatformDispatcher.instance.locale;
    final languageCode = systemLocale.languageCode;
    final countryCode = systemLocale.countryCode;

    // 根据系统语言返回支持的语言代码
    if (languageCode == 'zh') {
      if (countryCode == 'TW' || countryCode == 'HK') {
        return 'zh-TW';
      }
      return 'zh-CN';
    } else if (languageCode == 'en') {
      return 'en-US';
    }

    // 默认返回中文
    return 'zh-CN';
  }

  /// 确保SharedPreferences已初始化
  Future<void> _ensurePrefsInitialized() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// 获取Locale对象
  Locale getLocaleFromLanguageCode(String languageCode) {
    switch (languageCode) {
      case 'zh-CN':
        return const Locale('zh', 'CN');
      case 'zh-TW':
        return const Locale('zh', 'TW');
      case 'en-US':
        return const Locale('en', 'US');
      default:
        return const Locale('zh', 'CN');
    }
  }

  /// 从Locale获取语言代码
  String getLanguageCodeFromLocale(Locale locale) {
    final languageCode = locale.languageCode;
    final countryCode = locale.countryCode;

    if (languageCode == 'zh') {
      if (countryCode == 'TW' || countryCode == 'HK') {
        return 'zh-TW';
      }
      return 'zh-CN';
    } else if (languageCode == 'en') {
      return 'en-US';
    }

    return 'zh-CN';
  }

  /// 检查国际化服务健康状态
  Future<ApiResponse<Map<String, dynamic>>> checkI18nHealth() async {
    try {
      final response = await _apiService.get('/i18n/health');
      return ApiResponse.fromJson(response);
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: '检查国际化服务健康状态失败: $e',
      );
    }
  }
}

/// 语言偏好设置模型
class LanguagePreference {
  final String language;
  final String timezone;
  final String dateFormat;
  final String currency;

  const LanguagePreference({
    required this.language,
    required this.timezone,
    required this.dateFormat,
    required this.currency,
  });

  Map<String, dynamic> toJson() {
    return {
      'language': language,
      'timezone': timezone,
      'date_format': dateFormat,
      'currency': currency,
    };
  }

  factory LanguagePreference.fromJson(Map<String, dynamic> json) {
    return LanguagePreference(
      language: json['language'] ?? 'zh-CN',
      timezone: json['timezone'] ?? 'Asia/Shanghai',
      dateFormat: json['date_format'] ?? 'YYYY-MM-DD',
      currency: json['currency'] ?? 'CNY',
    );
  }

  LanguagePreference copyWith({
    String? language,
    String? timezone,
    String? dateFormat,
    String? currency,
  }) {
    return LanguagePreference(
      language: language ?? this.language,
      timezone: timezone ?? this.timezone,
      dateFormat: dateFormat ?? this.dateFormat,
      currency: currency ?? this.currency,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LanguagePreference &&
        other.language == language &&
        other.timezone == timezone &&
        other.dateFormat == dateFormat &&
        other.currency == currency;
  }

  @override
  int get hashCode {
    return language.hashCode ^
        timezone.hashCode ^
        dateFormat.hashCode ^
        currency.hashCode;
  }

  @override
  String toString() {
    return 'LanguagePreference(language: $language, timezone: $timezone, dateFormat: $dateFormat, currency: $currency)';
  }
}
