"""
用户反馈模型
用于收集和存储用户对AI分类结果的反馈，以改进分类准确性
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, ForeignKey, Float, Integer
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
import json

from ..database.base import Base


class UserFeedback(Base):
    """用户反馈模型"""
    __tablename__ = "user_feedbacks"

    # 基本信息
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, index=True)
    
    # 原始交易信息
    transaction_description = Column(Text, nullable=False)
    transaction_amount = Column(Float, nullable=False)
    transaction_type = Column(String(20), nullable=False)  # income, expense, transfer
    
    # AI分类结果
    ai_suggested_category = Column(String(100), nullable=False)
    ai_confidence = Column(Float, nullable=False)
    ai_reasoning = Column(Text)  # AI的分类理由
    
    # 用户反馈
    user_accepted = Column(Boolean, nullable=False)  # 用户是否接受AI建议
    user_corrected_category = Column(String(100))  # 用户修正的分类
    user_comment = Column(Text)  # 用户评论
    
    # 反馈类型
    feedback_type = Column(String(20), nullable=False, default="correction")  # correction, confirmation, suggestion
    
    # 学习状态
    is_processed = Column(Boolean, default=False)  # 是否已被学习系统处理
    learning_weight = Column(Float, default=1.0)  # 学习权重
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime(timezone=True))
    
    def __repr__(self):
        return f"<UserFeedback(id={self.id}, user_id={self.user_id}, accepted={self.user_accepted})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "transaction_description": self.transaction_description,
            "transaction_amount": float(self.transaction_amount),
            "transaction_type": self.transaction_type,
            "ai_suggested_category": self.ai_suggested_category,
            "ai_confidence": float(self.ai_confidence),
            "ai_reasoning": self.ai_reasoning,
            "user_accepted": self.user_accepted,
            "user_corrected_category": self.user_corrected_category,
            "user_comment": self.user_comment,
            "feedback_type": self.feedback_type,
            "is_processed": self.is_processed,
            "learning_weight": float(self.learning_weight),
            "created_at": self.created_at.isoformat(),
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
        }


class UserLearningPattern(Base):
    """用户学习模式模型"""
    __tablename__ = "user_learning_patterns"

    # 基本信息
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, index=True)
    
    # 模式信息
    pattern_type = Column(String(50), nullable=False)  # keyword, amount_range, merchant, time_based
    pattern_key = Column(String(200), nullable=False)  # 模式的关键字或标识
    pattern_value = Column(Text, nullable=False)  # JSON格式的模式数据
    
    # 分类信息
    preferred_category = Column(String(100), nullable=False)
    confidence_boost = Column(Float, default=0.1)  # 置信度提升值
    
    # 统计信息
    usage_count = Column(Integer, default=0)  # 使用次数
    success_rate = Column(Float, default=0.0)  # 成功率
    last_accuracy = Column(Float, default=0.0)  # 最近准确率
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_used = Column(DateTime(timezone=True))
    
    def __repr__(self):
        return f"<UserLearningPattern(id={self.id}, pattern_type={self.pattern_type}, key={self.pattern_key})>"
    
    def to_dict(self):
        """转换为字典"""
        try:
            pattern_data = json.loads(self.pattern_value) if self.pattern_value else {}
        except (json.JSONDecodeError, TypeError):
            pattern_data = {}
        
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "pattern_type": self.pattern_type,
            "pattern_key": self.pattern_key,
            "pattern_data": pattern_data,
            "preferred_category": self.preferred_category,
            "confidence_boost": float(self.confidence_boost),
            "usage_count": int(self.usage_count),
            "success_rate": float(self.success_rate),
            "last_accuracy": float(self.last_accuracy),
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "last_used": self.last_used.isoformat() if self.last_used else None,
        }
    
    def update_pattern_data(self, data: dict):
        """更新模式数据"""
        self.pattern_value = json.dumps(data, ensure_ascii=False)
    
    def get_pattern_data(self) -> dict:
        """获取模式数据"""
        try:
            return json.loads(self.pattern_value) if self.pattern_value else {}
        except (json.JSONDecodeError, TypeError):
            return {}


class CategoryAccuracy(Base):
    """分类准确性统计模型"""
    __tablename__ = "category_accuracies"

    # 基本信息
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, index=True)
    category_name = Column(String(100), nullable=False)
    
    # 统计信息
    total_predictions = Column(Integer, default=0)  # 总预测次数
    correct_predictions = Column(Integer, default=0)  # 正确预测次数
    accuracy_rate = Column(Float, default=0.0)  # 准确率
    
    # 置信度统计
    avg_confidence = Column(Float, default=0.0)  # 平均置信度
    confidence_accuracy_correlation = Column(Float, default=0.0)  # 置信度与准确性的相关性
    
    # 时间统计
    last_prediction = Column(DateTime(timezone=True))
    last_correct_prediction = Column(DateTime(timezone=True))
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<CategoryAccuracy(user_id={self.user_id}, category={self.category_name}, accuracy={self.accuracy_rate})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "category_name": self.category_name,
            "total_predictions": int(self.total_predictions),
            "correct_predictions": int(self.correct_predictions),
            "accuracy_rate": float(self.accuracy_rate),
            "avg_confidence": float(self.avg_confidence),
            "confidence_accuracy_correlation": float(self.confidence_accuracy_correlation),
            "last_prediction": self.last_prediction.isoformat() if self.last_prediction else None,
            "last_correct_prediction": self.last_correct_prediction.isoformat() if self.last_correct_prediction else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
    
    def update_accuracy(self, is_correct: bool, confidence: float):
        """更新准确性统计"""
        self.total_predictions += 1
        if is_correct:
            self.correct_predictions += 1
            self.last_correct_prediction = datetime.now()
        
        # 更新准确率
        self.accuracy_rate = self.correct_predictions / self.total_predictions
        
        # 更新平均置信度
        if self.total_predictions == 1:
            self.avg_confidence = confidence
        else:
            # 使用指数移动平均
            alpha = 0.1
            self.avg_confidence = alpha * confidence + (1 - alpha) * self.avg_confidence
        
        self.last_prediction = datetime.now()
        self.updated_at = datetime.now()


class AIModelPerformance(Base):
    """AI模型性能统计模型"""
    __tablename__ = "ai_model_performances"

    # 基本信息
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, index=True)
    
    # 模型信息
    provider = Column(String(50), nullable=False)
    model_name = Column(String(100), nullable=False)
    
    # 性能统计
    total_requests = Column(Integer, default=0)
    successful_requests = Column(Integer, default=0)
    failed_requests = Column(Integer, default=0)
    
    # 准确性统计
    total_feedbacks = Column(Integer, default=0)
    positive_feedbacks = Column(Integer, default=0)
    accuracy_rate = Column(Float, default=0.0)
    
    # 响应时间统计
    avg_response_time = Column(Float, default=0.0)  # 平均响应时间（秒）
    min_response_time = Column(Float, default=0.0)
    max_response_time = Column(Float, default=0.0)
    
    # 成本统计
    total_tokens_used = Column(Integer, default=0)
    estimated_cost = Column(Float, default=0.0)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_used = Column(DateTime(timezone=True))
    
    def __repr__(self):
        return f"<AIModelPerformance(user_id={self.user_id}, provider={self.provider}, model={self.model_name})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "provider": self.provider,
            "model_name": self.model_name,
            "total_requests": int(self.total_requests),
            "successful_requests": int(self.successful_requests),
            "failed_requests": int(self.failed_requests),
            "success_rate": float(self.successful_requests / max(1, self.total_requests)),
            "total_feedbacks": int(self.total_feedbacks),
            "positive_feedbacks": int(self.positive_feedbacks),
            "accuracy_rate": float(self.accuracy_rate),
            "avg_response_time": float(self.avg_response_time),
            "min_response_time": float(self.min_response_time),
            "max_response_time": float(self.max_response_time),
            "total_tokens_used": int(self.total_tokens_used),
            "estimated_cost": float(self.estimated_cost),
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "last_used": self.last_used.isoformat() if self.last_used else None,
        }
    
    def update_performance(self, response_time: float, tokens_used: int = 0, cost: float = 0.0):
        """更新性能统计"""
        self.total_requests += 1
        self.successful_requests += 1
        
        # 更新响应时间统计
        if self.total_requests == 1:
            self.avg_response_time = response_time
            self.min_response_time = response_time
            self.max_response_time = response_time
        else:
            # 使用指数移动平均
            alpha = 0.1
            self.avg_response_time = alpha * response_time + (1 - alpha) * self.avg_response_time
            self.min_response_time = min(self.min_response_time, response_time)
            self.max_response_time = max(self.max_response_time, response_time)
        
        # 更新成本统计
        self.total_tokens_used += tokens_used
        self.estimated_cost += cost
        
        self.last_used = datetime.now()
        self.updated_at = datetime.now()
    
    def record_failure(self):
        """记录失败请求"""
        self.total_requests += 1
        self.failed_requests += 1
        self.updated_at = datetime.now()
    
    def update_feedback(self, is_positive: bool):
        """更新反馈统计"""
        self.total_feedbacks += 1
        if is_positive:
            self.positive_feedbacks += 1
        
        # 更新准确率
        self.accuracy_rate = self.positive_feedbacks / self.total_feedbacks
        self.updated_at = datetime.now()
