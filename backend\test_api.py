#!/usr/bin/env python3
"""
简单的API测试脚本
"""
import requests
import json

def test_api():
    base_url = "http://localhost:8000"
    
    print("🚀 测试记账报销软件API...")
    print(f"📍 服务地址: {base_url}")
    print("-" * 50)
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 健康检查: 通过")
            print(f"   状态: {data.get('status')}")
            print(f"   服务: {data.get('service')}")
            print(f"   版本: {data.get('version')}")
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 健康检查失败: {e}")
        return False
    
    # 测试根路径
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 根路径: 通过")
            print(f"   消息: {data.get('message')}")
            print(f"   文档: {data.get('docs')}")
        else:
            print(f"❌ 根路径测试失败: HTTP {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 根路径测试失败: {e}")
    
    print("-" * 50)
    print("🎉 API测试完成!")
    print(f"📖 API文档地址: {base_url}/docs")
    print(f"🔧 ReDoc文档地址: {base_url}/redoc")
    
    return True

if __name__ == "__main__":
    test_api()
