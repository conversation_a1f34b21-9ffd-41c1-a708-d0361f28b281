"""
测试服务器 - 用于验证性能监控和日志系统
"""

from fastapi import FastAPI
import uvicorn
from app.utils.performance_monitor import performance_monitor
from app.utils.structured_logger import structured_logger

app = FastAPI(title="测试服务器")

@app.get("/")
async def root():
    return {"message": "测试服务器运行正常"}

@app.get("/test/health")
async def test_health():
    """测试系统健康检查"""
    try:
        health = await performance_monitor.get_system_health()
        return {
            "status": health.status,
            "cpu_percent": health.cpu_percent,
            "memory_percent": health.memory_percent,
            "uptime": health.uptime
        }
    except Exception as e:
        return {"error": str(e)}

@app.get("/test/log")
async def test_log():
    """测试结构化日志"""
    try:
        structured_logger.info("测试日志消息")
        return {"message": "日志记录成功"}
    except Exception as e:
        return {"error": str(e)}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
