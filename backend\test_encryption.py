#!/usr/bin/env python3
"""
测试API密钥加密功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.encryption import APIKeyEncryption

def main():
    print("🔐 开始测试API密钥加密功能...")
    
    try:
        manager = APIKeyEncryption()
        
        # 测试不同类型的API密钥
        test_keys = {
            "openai": "sk-1234567890abcdef1234567890abcdef",
            "anthropic": "sk-ant-1234567890abcdef1234567890abcdef",
            "azure": "1234567890abcdef1234567890abcdef",
        }
        
        for provider, key in test_keys.items():
            print(f"\n📝 测试 {provider} 密钥:")
            print(f"   原始密钥: {key}")
            
            # 加密
            encrypted = manager.encrypt_api_key(key)
            print(f"   加密后: {encrypted[:20]}...")
            
            # 解密
            decrypted = manager.decrypt_api_key(encrypted)
            print(f"   解密后: {decrypted}")
            
            # 脱敏显示
            masked = manager.mask_api_key(key)
            print(f"   脱敏显示: {masked}")
            
            # 验证格式
            is_valid = manager.validate_api_key_format(key, provider.split("_")[0])
            print(f"   格式验证: {'✅' if is_valid else '❌'}")
            
            # 强度评估
            strength = manager.get_key_strength(key)
            print(f"   密钥强度: {strength}")
            
            # 验证加解密一致性
            if decrypted == key:
                print(f"   加解密一致性: ✅")
            else:
                print(f"   加解密一致性: ❌")
                return False
        
        print("\n🎉 所有加密测试通过!")
        return True
        
    except Exception as e:
        print(f"\n❌ 加密测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
