"""
监控和指标API端点
提供性能监控、系统健康检查和指标导出功能
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Response
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from pydantic import BaseModel
from ..auth.dependencies import get_current_user
from ..models.user import User
from ..utils.performance_monitor import performance_monitor
from ..utils.metrics_collector import metrics_collector
# from ..utils.access_control import AccessControlManager, Permission
from ..utils.structured_logger import structured_logger, LogCategory, LogLevel
import json

router = APIRouter()
# access_control = AccessControlManager()


class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str
    timestamp: float
    checks: Dict[str, Any]
    uptime: float


class MetricsExportRequest(BaseModel):
    """指标导出请求"""
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    format: str = "json"  # json, prometheus
    include_system: bool = True
    include_http: bool = True
    include_database: bool = True
    include_cache: bool = True
    include_ai: bool = True


class PerformanceTrendRequest(BaseModel):
    """性能趋势请求"""
    hours: int = 24
    metric_types: Optional[List[str]] = None


@router.get("/health", response_model=HealthResponse)
async def get_system_health():
    """获取系统健康状态"""
    try:
        health = await performance_monitor.get_system_health()
        
        return HealthResponse(
            status=health.status,
            timestamp=datetime.now().timestamp(),
            checks={
                "cpu": {
                    "status": "ok" if health.cpu_percent < 80 else "warning",
                    "value": health.cpu_percent,
                    "unit": "percent"
                },
                "memory": {
                    "status": "ok" if health.memory_percent < 80 else "warning",
                    "value": health.memory_percent,
                    "unit": "percent"
                },
                "disk": {
                    "status": "ok" if health.disk_usage < 80 else "warning",
                    "value": health.disk_usage,
                    "unit": "percent"
                },
                "response_time": {
                    "status": "ok" if health.response_time_avg < 1.0 else "warning",
                    "value": health.response_time_avg,
                    "unit": "seconds"
                },
                "error_rate": {
                    "status": "ok" if health.error_rate < 5.0 else "warning",
                    "value": health.error_rate,
                    "unit": "percent"
                },
                "connections": {
                    "status": "ok",
                    "value": health.active_connections,
                    "unit": "count"
                }
            },
            uptime=health.uptime
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统健康状态失败: {str(e)}")


@router.get("/metrics")
async def get_metrics(
    format: str = Query("json", description="导出格式: json, prometheus"),
    current_user: User = Depends(get_current_user)
):
    """获取系统指标"""
    try:
        # 检查权限
        has_permission = await access_control.has_permission(
            current_user.id, Permission.VIEW_SYSTEM_METRICS
        )
        if not has_permission:
            raise HTTPException(status_code=403, detail="权限不足")
        
        if format.lower() == "prometheus":
            # 返回Prometheus格式
            prometheus_data = metrics_collector.export_prometheus_format()
            return Response(
                content=prometheus_data,
                media_type="text/plain; version=0.0.4; charset=utf-8"
            )
        else:
            # 返回JSON格式
            return metrics_collector.export_json_format()
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统指标失败: {str(e)}")


@router.get("/performance/overview")
async def get_performance_overview(current_user: User = Depends(get_current_user)):
    """获取性能概览"""
    try:
        # 检查权限
        has_permission = await access_control.has_permission(
            current_user.id, Permission.VIEW_SYSTEM_METRICS
        )
        if not has_permission:
            raise HTTPException(status_code=403, detail="权限不足")
        
        # 获取系统健康状态
        health = await performance_monitor.get_system_health()
        
        # 获取端点统计
        endpoint_stats = await performance_monitor.get_endpoint_stats()
        
        # 获取缓存统计
        cache_stats = await performance_monitor.get_cache_stats()
        
        # 获取数据库统计
        db_stats = await performance_monitor.get_database_stats()
        
        # 获取活跃用户数
        active_users = await performance_monitor.get_active_users_count()
        
        return {
            "system_health": {
                "status": health.status,
                "cpu_percent": health.cpu_percent,
                "memory_percent": health.memory_percent,
                "disk_usage": health.disk_usage,
                "response_time_avg": health.response_time_avg,
                "error_rate": health.error_rate,
                "uptime": health.uptime
            },
            "endpoint_stats": endpoint_stats,
            "cache_stats": cache_stats,
            "database_stats": db_stats,
            "active_users": active_users,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取性能概览失败: {str(e)}")


@router.get("/performance/trends")
async def get_performance_trends(
    hours: int = Query(24, description="时间范围（小时）"),
    current_user: User = Depends(get_current_user)
):
    """获取性能趋势数据"""
    try:
        # 检查权限
        has_permission = await access_control.has_permission(
            current_user.id, Permission.VIEW_SYSTEM_METRICS
        )
        if not has_permission:
            raise HTTPException(status_code=403, detail="权限不足")
        
        trends = await performance_monitor.get_performance_trends(hours)
        
        return {
            "trends": trends,
            "period": {
                "hours": hours,
                "start_time": (datetime.now() - timedelta(hours=hours)).isoformat(),
                "end_time": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取性能趋势失败: {str(e)}")


@router.get("/performance/endpoints")
async def get_endpoint_performance(
    endpoint: Optional[str] = Query(None, description="特定端点"),
    current_user: User = Depends(get_current_user)
):
    """获取端点性能统计"""
    try:
        # 检查权限
        has_permission = await access_control.has_permission(
            current_user.id, Permission.VIEW_SYSTEM_METRICS
        )
        if not has_permission:
            raise HTTPException(status_code=403, detail="权限不足")
        
        stats = await performance_monitor.get_endpoint_stats(endpoint)
        
        return {
            "endpoint_stats": stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取端点性能统计失败: {str(e)}")


@router.post("/metrics/export")
async def export_metrics(
    request: MetricsExportRequest,
    current_user: User = Depends(get_current_user)
):
    """导出指标数据"""
    try:
        # 检查权限
        has_permission = await access_control.has_permission(
            current_user.id, Permission.EXPORT_SYSTEM_DATA
        )
        if not has_permission:
            raise HTTPException(status_code=403, detail="权限不足")
        
        if request.format.lower() == "prometheus":
            # 导出Prometheus格式
            data = metrics_collector.export_prometheus_format()
            return Response(
                content=data,
                media_type="text/plain; version=0.0.4; charset=utf-8",
                headers={
                    "Content-Disposition": f"attachment; filename=metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                }
            )
        else:
            # 导出JSON格式
            export_data = await performance_monitor.export_metrics(
                request.start_time, request.end_time
            )
            
            # 添加指标收集器数据
            if request.include_system or request.include_http or request.include_database or request.include_cache or request.include_ai:
                collector_data = metrics_collector.export_json_format()
                export_data["collector_metrics"] = collector_data
            
            return export_data
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出指标数据失败: {str(e)}")


@router.get("/metrics/summary")
async def get_metrics_summary(current_user: User = Depends(get_current_user)):
    """获取指标汇总"""
    try:
        # 检查权限
        has_permission = await access_control.has_permission(
            current_user.id, Permission.VIEW_SYSTEM_METRICS
        )
        if not has_permission:
            raise HTTPException(status_code=403, detail="权限不足")
        
        summary = metrics_collector.get_summary_stats()
        
        return summary
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取指标汇总失败: {str(e)}")


@router.delete("/metrics/cleanup")
async def cleanup_old_metrics(
    days: int = Query(7, description="保留天数"),
    current_user: User = Depends(get_current_user)
):
    """清理旧指标数据"""
    try:
        # 检查权限
        has_permission = await access_control.has_permission(
            current_user.id, Permission.MANAGE_SYSTEM
        )
        if not has_permission:
            raise HTTPException(status_code=403, detail="权限不足")
        
        await performance_monitor.clear_old_metrics(days)
        
        return {
            "success": True,
            "message": f"已清理 {days} 天前的指标数据",
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理指标数据失败: {str(e)}")


@router.get("/alerts/performance")
async def get_performance_alerts(current_user: User = Depends(get_current_user)):
    """获取性能警报"""
    try:
        # 检查权限
        has_permission = await access_control.has_permission(
            current_user.id, Permission.VIEW_SYSTEM_METRICS
        )
        if not has_permission:
            raise HTTPException(status_code=403, detail="权限不足")
        
        health = await performance_monitor.get_system_health()
        alerts = []
        
        # 检查CPU使用率
        if health.cpu_percent > 90:
            alerts.append({
                "type": "cpu_high",
                "level": "critical",
                "message": f"CPU使用率过高: {health.cpu_percent:.1f}%",
                "value": health.cpu_percent,
                "threshold": 90
            })
        elif health.cpu_percent > 80:
            alerts.append({
                "type": "cpu_warning",
                "level": "warning",
                "message": f"CPU使用率较高: {health.cpu_percent:.1f}%",
                "value": health.cpu_percent,
                "threshold": 80
            })
        
        # 检查内存使用率
        if health.memory_percent > 90:
            alerts.append({
                "type": "memory_high",
                "level": "critical",
                "message": f"内存使用率过高: {health.memory_percent:.1f}%",
                "value": health.memory_percent,
                "threshold": 90
            })
        elif health.memory_percent > 80:
            alerts.append({
                "type": "memory_warning",
                "level": "warning",
                "message": f"内存使用率较高: {health.memory_percent:.1f}%",
                "value": health.memory_percent,
                "threshold": 80
            })
        
        # 检查错误率
        if health.error_rate > 10:
            alerts.append({
                "type": "error_rate_high",
                "level": "critical",
                "message": f"错误率过高: {health.error_rate:.1f}%",
                "value": health.error_rate,
                "threshold": 10
            })
        elif health.error_rate > 5:
            alerts.append({
                "type": "error_rate_warning",
                "level": "warning",
                "message": f"错误率较高: {health.error_rate:.1f}%",
                "value": health.error_rate,
                "threshold": 5
            })
        
        # 检查响应时间
        if health.response_time_avg > 2.0:
            alerts.append({
                "type": "response_time_high",
                "level": "critical",
                "message": f"平均响应时间过长: {health.response_time_avg:.2f}s",
                "value": health.response_time_avg,
                "threshold": 2.0
            })
        elif health.response_time_avg > 1.0:
            alerts.append({
                "type": "response_time_warning",
                "level": "warning",
                "message": f"平均响应时间较长: {health.response_time_avg:.2f}s",
                "value": health.response_time_avg,
                "threshold": 1.0
            })
        
        return {
            "alerts": alerts,
            "total_alerts": len(alerts),
            "system_status": health.status,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取性能警报失败: {str(e)}")


@router.get("/dashboard/data")
async def get_dashboard_data(current_user: User = Depends(get_current_user)):
    """获取监控仪表板数据"""
    try:
        # 检查权限
        has_permission = await access_control.has_permission(
            current_user.id, Permission.VIEW_SYSTEM_METRICS
        )
        if not has_permission:
            raise HTTPException(status_code=403, detail="权限不足")
        
        # 获取各种数据
        health = await performance_monitor.get_system_health()
        endpoint_stats = await performance_monitor.get_endpoint_stats()
        cache_stats = await performance_monitor.get_cache_stats()
        db_stats = await performance_monitor.get_database_stats()
        trends = await performance_monitor.get_performance_trends(24)
        
        return {
            "system_health": {
                "status": health.status,
                "cpu_percent": health.cpu_percent,
                "memory_percent": health.memory_percent,
                "disk_usage": health.disk_usage,
                "response_time_avg": health.response_time_avg,
                "error_rate": health.error_rate,
                "uptime": health.uptime,
                "active_connections": health.active_connections
            },
            "endpoint_stats": endpoint_stats,
            "cache_stats": cache_stats,
            "database_stats": db_stats,
            "performance_trends": trends,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取仪表板数据失败: {str(e)}")


@router.get("/logs/search")
async def search_logs(
    category: Optional[str] = Query(None, description="日志分类"),
    level: Optional[str] = Query(None, description="日志级别"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    user_id: Optional[str] = Query(None, description="用户ID"),
    limit: int = Query(100, description="返回条数限制"),
    current_user: User = Depends(get_current_user)
):
    """搜索日志"""
    try:
        # 检查权限
        has_permission = await access_control.has_permission(
            current_user.id, Permission.VIEW_SYSTEM_LOGS
        )
        if not has_permission:
            raise HTTPException(status_code=403, detail="权限不足")

        # 转换参数
        log_category = None
        if category:
            try:
                log_category = LogCategory(category)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"无效的日志分类: {category}")

        log_level = None
        if level:
            try:
                log_level = LogLevel(level)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"无效的日志级别: {level}")

        # 搜索日志
        logs = await structured_logger.search_logs(
            category=log_category,
            level=log_level,
            start_time=start_time,
            end_time=end_time,
            user_id=user_id,
            limit=limit
        )

        return {
            "logs": logs,
            "total": len(logs),
            "filters": {
                "category": category,
                "level": level,
                "start_time": start_time.isoformat() if start_time else None,
                "end_time": end_time.isoformat() if end_time else None,
                "user_id": user_id,
                "limit": limit
            },
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索日志失败: {str(e)}")


@router.get("/logs/statistics")
async def get_log_statistics(
    hours: int = Query(24, description="统计时间范围（小时）"),
    current_user: User = Depends(get_current_user)
):
    """获取日志统计信息"""
    try:
        # 检查权限
        has_permission = await access_control.has_permission(
            current_user.id, Permission.VIEW_SYSTEM_LOGS
        )
        if not has_permission:
            raise HTTPException(status_code=403, detail="权限不足")

        stats = await structured_logger.get_log_statistics(hours)

        return stats

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取日志统计失败: {str(e)}")


@router.delete("/logs/cleanup")
async def cleanup_old_logs(
    days: int = Query(30, description="保留天数"),
    current_user: User = Depends(get_current_user)
):
    """清理旧日志"""
    try:
        # 检查权限
        has_permission = await access_control.has_permission(
            current_user.id, Permission.MANAGE_SYSTEM
        )
        if not has_permission:
            raise HTTPException(status_code=403, detail="权限不足")

        # 执行清理
        await structured_logger._cleanup_old_logs()

        return {
            "success": True,
            "message": f"已清理 {days} 天前的日志",
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理日志失败: {str(e)}")
