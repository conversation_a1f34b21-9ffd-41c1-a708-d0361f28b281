"""
安全监控系统
实时监控系统安全状态，检测异常行为和潜在威胁
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Tuple
from enum import Enum
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import ipaddress
import re

from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession

from ..utils.cache import get_cache_manager_sync
from .audit_logger import get_audit_logger, AuditEventType, AuditLevel


class ThreatLevel(Enum):
    """威胁级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class SecurityEventType(Enum):
    """安全事件类型"""
    BRUTE_FORCE_ATTACK = "brute_force_attack"
    SUSPICIOUS_LOGIN = "suspicious_login"
    UNUSUAL_ACTIVITY = "unusual_activity"
    RATE_LIMIT_ABUSE = "rate_limit_abuse"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    DATA_EXFILTRATION = "data_exfiltration"
    MALICIOUS_REQUEST = "malicious_request"
    ACCOUNT_TAKEOVER = "account_takeover"


@dataclass
class SecurityAlert:
    """安全警报"""
    id: str
    timestamp: datetime
    event_type: SecurityEventType
    threat_level: ThreatLevel
    source_ip: Optional[str]
    user_id: Optional[str]
    description: str
    details: Dict
    actions_taken: List[str]
    resolved: bool = False
    resolved_at: Optional[datetime] = None

    def to_dict(self) -> Dict:
        """转换为字典"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['event_type'] = self.event_type.value
        data['threat_level'] = self.threat_level.value
        if self.resolved_at:
            data['resolved_at'] = self.resolved_at.isoformat()
        return data


class SecurityMonitor:
    """安全监控器"""

    def __init__(self):
        self.cache_manager = get_cache_manager_sync()
        self.audit_logger = get_audit_logger()
        
        # 监控配置
        self.config = {
            "login_failure_threshold": 5,  # 登录失败阈值
            "login_failure_window": 300,   # 登录失败时间窗口（秒）
            "rate_limit_threshold": 100,   # 请求频率阈值
            "rate_limit_window": 60,       # 请求频率时间窗口（秒）
            "suspicious_ip_threshold": 10, # 可疑IP阈值
            "geo_anomaly_enabled": True,   # 地理位置异常检测
            "time_anomaly_enabled": True,  # 时间异常检测
        }
        
        # 内存存储
        self.active_alerts: Dict[str, SecurityAlert] = {}
        self.blocked_ips: Set[str] = set()
        self.suspicious_ips: Set[str] = set()
        self.user_sessions: Dict[str, Dict] = {}
        
        # 统计数据
        self.login_attempts: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.request_counts: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.user_activities: Dict[str, deque] = defaultdict(lambda: deque(maxlen=500))
        
        # 启动监控任务
        self.monitor_task = None
        self._start_monitoring()

    def _start_monitoring(self):
        """启动监控任务"""
        if self.monitor_task is None or self.monitor_task.done():
            self.monitor_task = asyncio.create_task(self._monitoring_loop())

    async def _monitoring_loop(self):
        """监控循环"""
        while True:
            try:
                await asyncio.sleep(30)  # 每30秒检查一次
                
                # 检查各种安全威胁
                await self._check_brute_force_attacks()
                await self._check_rate_limit_abuse()
                await self._check_suspicious_activities()
                await self._cleanup_old_data()
                
            except Exception as e:
                logger.error(f"安全监控循环错误: {e}")

    async def record_login_attempt(
        self,
        ip_address: str,
        user_id: Optional[str] = None,
        email: Optional[str] = None,
        success: bool = True,
        user_agent: Optional[str] = None
    ):
        """记录登录尝试"""
        try:
            timestamp = datetime.now()
            
            # 记录登录尝试
            attempt_data = {
                "timestamp": timestamp,
                "ip_address": ip_address,
                "user_id": user_id,
                "email": email,
                "success": success,
                "user_agent": user_agent
            }
            
            self.login_attempts[ip_address].append(attempt_data)
            
            # 如果登录失败，检查是否为暴力破解攻击
            if not success:
                await self._check_brute_force_for_ip(ip_address)
            
            # 如果登录成功，检查是否为可疑登录
            if success and user_id:
                await self._check_suspicious_login(user_id, ip_address, user_agent)
            
        except Exception as e:
            logger.error(f"记录登录尝试失败: {e}")

    async def record_request(
        self,
        ip_address: str,
        user_id: Optional[str] = None,
        endpoint: str = "",
        method: str = "GET",
        status_code: int = 200,
        user_agent: Optional[str] = None
    ):
        """记录请求"""
        try:
            timestamp = datetime.now()
            
            request_data = {
                "timestamp": timestamp,
                "ip_address": ip_address,
                "user_id": user_id,
                "endpoint": endpoint,
                "method": method,
                "status_code": status_code,
                "user_agent": user_agent
            }
            
            self.request_counts[ip_address].append(request_data)
            
            # 检查请求频率（在_check_rate_limit_abuse中统一处理）
            
            # 检查恶意请求模式
            await self._check_malicious_patterns(request_data)
            
        except Exception as e:
            logger.error(f"记录请求失败: {e}")

    async def _check_brute_force_attacks(self):
        """检查暴力破解攻击"""
        try:
            current_time = datetime.now()
            threshold = self.config["login_failure_threshold"]
            window = self.config["login_failure_window"]
            
            for ip_address, attempts in self.login_attempts.items():
                if not attempts:
                    continue
                
                # 统计时间窗口内的失败次数
                recent_failures = [
                    attempt for attempt in attempts
                    if not attempt["success"] and
                    (current_time - attempt["timestamp"]).total_seconds() <= window
                ]
                
                if len(recent_failures) >= threshold:
                    await self._create_security_alert(
                        event_type=SecurityEventType.BRUTE_FORCE_ATTACK,
                        threat_level=ThreatLevel.HIGH,
                        source_ip=ip_address,
                        description=f"检测到来自 {ip_address} 的暴力破解攻击",
                        details={
                            "failure_count": len(recent_failures),
                            "time_window": window,
                            "threshold": threshold
                        }
                    )
                    
                    # 自动阻止IP
                    await self._block_ip(ip_address, "brute_force_attack")
                    
        except Exception as e:
            logger.error(f"检查暴力破解攻击失败: {e}")

    async def _check_brute_force_for_ip(self, ip_address: str):
        """检查特定IP的暴力破解"""
        try:
            current_time = datetime.now()
            attempts = self.login_attempts.get(ip_address, deque())
            
            # 统计最近的失败次数
            recent_failures = [
                attempt for attempt in attempts
                if not attempt["success"] and
                (current_time - attempt["timestamp"]).total_seconds() <= self.config["login_failure_window"]
            ]
            
            if len(recent_failures) >= self.config["login_failure_threshold"]:
                await self._create_security_alert(
                    event_type=SecurityEventType.BRUTE_FORCE_ATTACK,
                    threat_level=ThreatLevel.HIGH,
                    source_ip=ip_address,
                    description=f"检测到来自 {ip_address} 的暴力破解攻击",
                    details={
                        "failure_count": len(recent_failures),
                        "recent_attempts": [
                            {
                                "timestamp": attempt["timestamp"].isoformat(),
                                "email": attempt.get("email", "unknown")
                            }
                            for attempt in recent_failures[-5:]  # 最近5次尝试
                        ]
                    }
                )
                
                await self._block_ip(ip_address, "brute_force_attack")
                
        except Exception as e:
            logger.error(f"检查IP暴力破解失败: {e}")

    async def _check_suspicious_login(self, user_id: str, ip_address: str, user_agent: Optional[str]):
        """检查可疑登录"""
        try:
            # 获取用户历史登录信息
            user_history = await self._get_user_login_history(user_id)
            
            suspicious_factors = []
            
            # 检查新IP地址
            if ip_address not in user_history.get("known_ips", set()):
                suspicious_factors.append("new_ip_address")
            
            # 检查地理位置异常
            if self.config["geo_anomaly_enabled"]:
                if await self._is_geo_anomaly(user_id, ip_address):
                    suspicious_factors.append("geo_anomaly")
            
            # 检查时间异常
            if self.config["time_anomaly_enabled"]:
                if await self._is_time_anomaly(user_id):
                    suspicious_factors.append("time_anomaly")
            
            # 检查User-Agent异常
            if user_agent and await self._is_user_agent_anomaly(user_id, user_agent):
                suspicious_factors.append("user_agent_anomaly")
            
            # 如果有可疑因素，创建警报
            if suspicious_factors:
                threat_level = ThreatLevel.MEDIUM if len(suspicious_factors) == 1 else ThreatLevel.HIGH
                
                await self._create_security_alert(
                    event_type=SecurityEventType.SUSPICIOUS_LOGIN,
                    threat_level=threat_level,
                    source_ip=ip_address,
                    user_id=user_id,
                    description=f"检测到用户 {user_id} 的可疑登录",
                    details={
                        "suspicious_factors": suspicious_factors,
                        "ip_address": ip_address,
                        "user_agent": user_agent
                    }
                )
                
        except Exception as e:
            logger.error(f"检查可疑登录失败: {e}")

    async def _check_rate_limit_abuse(self):
        """检查请求频率滥用"""
        try:
            current_time = datetime.now()
            threshold = self.config["rate_limit_threshold"]
            window = self.config["rate_limit_window"]
            
            for ip_address, requests in self.request_counts.items():
                if not requests:
                    continue
                
                # 统计时间窗口内的请求数
                recent_requests = [
                    req for req in requests
                    if (current_time - req["timestamp"]).total_seconds() <= window
                ]
                
                if len(recent_requests) >= threshold:
                    await self._create_security_alert(
                        event_type=SecurityEventType.RATE_LIMIT_ABUSE,
                        threat_level=ThreatLevel.MEDIUM,
                        source_ip=ip_address,
                        description=f"检测到来自 {ip_address} 的请求频率滥用",
                        details={
                            "request_count": len(recent_requests),
                            "time_window": window,
                            "threshold": threshold
                        }
                    )
                    
                    # 添加到可疑IP列表
                    self.suspicious_ips.add(ip_address)
                    
        except Exception as e:
            logger.error(f"检查请求频率滥用失败: {e}")

    async def _check_suspicious_activities(self):
        """检查可疑活动"""
        try:
            # 检查异常的用户活动模式
            for user_id, activities in self.user_activities.items():
                if await self._is_unusual_activity_pattern(user_id, activities):
                    await self._create_security_alert(
                        event_type=SecurityEventType.UNUSUAL_ACTIVITY,
                        threat_level=ThreatLevel.MEDIUM,
                        user_id=user_id,
                        description=f"检测到用户 {user_id} 的异常活动模式",
                        details={"activity_count": len(activities)}
                    )
                    
        except Exception as e:
            logger.error(f"检查可疑活动失败: {e}")

    async def _check_malicious_patterns(self, request_data: Dict):
        """检查恶意请求模式"""
        try:
            endpoint = request_data.get("endpoint", "")
            user_agent = request_data.get("user_agent", "")
            
            # 检查SQL注入模式
            sql_injection_patterns = [
                r"union\s+select", r"drop\s+table", r"insert\s+into",
                r"delete\s+from", r"update\s+set", r"exec\s*\(",
                r"script\s*>", r"javascript:", r"vbscript:"
            ]
            
            for pattern in sql_injection_patterns:
                if re.search(pattern, endpoint, re.IGNORECASE):
                    await self._create_security_alert(
                        event_type=SecurityEventType.MALICIOUS_REQUEST,
                        threat_level=ThreatLevel.HIGH,
                        source_ip=request_data["ip_address"],
                        description="检测到可能的SQL注入攻击",
                        details={
                            "endpoint": endpoint,
                            "pattern": pattern,
                            "user_agent": user_agent
                        }
                    )
                    break
            
            # 检查XSS模式
            xss_patterns = [
                r"<script", r"javascript:", r"onload=", r"onerror=",
                r"alert\s*\(", r"document\.cookie", r"eval\s*\("
            ]
            
            for pattern in xss_patterns:
                if re.search(pattern, endpoint, re.IGNORECASE):
                    await self._create_security_alert(
                        event_type=SecurityEventType.MALICIOUS_REQUEST,
                        threat_level=ThreatLevel.HIGH,
                        source_ip=request_data["ip_address"],
                        description="检测到可能的XSS攻击",
                        details={
                            "endpoint": endpoint,
                            "pattern": pattern,
                            "user_agent": user_agent
                        }
                    )
                    break
                    
        except Exception as e:
            logger.error(f"检查恶意请求模式失败: {e}")

    async def _create_security_alert(
        self,
        event_type: SecurityEventType,
        threat_level: ThreatLevel,
        description: str,
        details: Dict,
        source_ip: Optional[str] = None,
        user_id: Optional[str] = None
    ):
        """创建安全警报"""
        try:
            import uuid
            alert_id = str(uuid.uuid4())
            
            alert = SecurityAlert(
                id=alert_id,
                timestamp=datetime.now(),
                event_type=event_type,
                threat_level=threat_level,
                source_ip=source_ip,
                user_id=user_id,
                description=description,
                details=details,
                actions_taken=[]
            )
            
            self.active_alerts[alert_id] = alert
            
            # 记录到审计日志
            await self.audit_logger.log_security_event(
                user_id=user_id,
                event_type="security_alert_created",
                details={
                    "alert_id": alert_id,
                    "event_type": event_type.value,
                    "threat_level": threat_level.value,
                    "source_ip": source_ip,
                    "description": description
                },
                level=AuditLevel.HIGH if threat_level in [ThreatLevel.HIGH, ThreatLevel.CRITICAL] else AuditLevel.MEDIUM
            )
            
            # 自动响应
            await self._auto_respond_to_alert(alert)
            
            logger.warning(f"创建安全警报: {alert_id} - {description}")
            
        except Exception as e:
            logger.error(f"创建安全警报失败: {e}")

    async def _auto_respond_to_alert(self, alert: SecurityAlert):
        """自动响应安全警报"""
        try:
            actions = []
            
            # 根据威胁级别和类型采取行动
            if alert.threat_level == ThreatLevel.CRITICAL:
                if alert.source_ip:
                    await self._block_ip(alert.source_ip, alert.event_type.value)
                    actions.append(f"blocked_ip_{alert.source_ip}")
                
                if alert.user_id:
                    await self._suspend_user(alert.user_id, alert.event_type.value)
                    actions.append(f"suspended_user_{alert.user_id}")
            
            elif alert.threat_level == ThreatLevel.HIGH:
                if alert.source_ip and alert.event_type in [
                    SecurityEventType.BRUTE_FORCE_ATTACK,
                    SecurityEventType.MALICIOUS_REQUEST
                ]:
                    await self._block_ip(alert.source_ip, alert.event_type.value)
                    actions.append(f"blocked_ip_{alert.source_ip}")
            
            alert.actions_taken = actions
            
        except Exception as e:
            logger.error(f"自动响应安全警报失败: {e}")

    async def _block_ip(self, ip_address: str, reason: str):
        """阻止IP地址"""
        try:
            self.blocked_ips.add(ip_address)
            
            # 缓存阻止的IP
            await self.cache_manager.set(
                f"blocked_ip:{ip_address}",
                {"reason": reason, "blocked_at": datetime.now().isoformat()},
                ttl=86400  # 24小时
            )
            
            logger.warning(f"已阻止IP地址: {ip_address}, 原因: {reason}")
            
        except Exception as e:
            logger.error(f"阻止IP地址失败: {e}")

    async def _suspend_user(self, user_id: str, reason: str):
        """暂停用户账户"""
        try:
            # 这里应该实际暂停用户账户
            logger.warning(f"暂停用户账户: {user_id}, 原因: {reason}")
            
        except Exception as e:
            logger.error(f"暂停用户账户失败: {e}")

    async def is_ip_blocked(self, ip_address: str) -> bool:
        """检查IP是否被阻止"""
        if ip_address in self.blocked_ips:
            return True
        
        # 检查缓存
        blocked_info = await self.cache_manager.get(f"blocked_ip:{ip_address}")
        return blocked_info is not None

    async def get_security_status(self) -> Dict:
        """获取安全状态"""
        try:
            current_time = datetime.now()
            
            # 统计最近24小时的警报
            recent_alerts = [
                alert for alert in self.active_alerts.values()
                if (current_time - alert.timestamp).total_seconds() <= 86400
            ]
            
            # 按威胁级别分组
            alerts_by_level = defaultdict(int)
            for alert in recent_alerts:
                alerts_by_level[alert.threat_level.value] += 1
            
            return {
                "total_alerts": len(recent_alerts),
                "alerts_by_level": dict(alerts_by_level),
                "blocked_ips_count": len(self.blocked_ips),
                "suspicious_ips_count": len(self.suspicious_ips),
                "monitoring_status": "active" if self.monitor_task and not self.monitor_task.done() else "inactive"
            }
            
        except Exception as e:
            logger.error(f"获取安全状态失败: {e}")
            return {}

    async def _cleanup_old_data(self):
        """清理旧数据"""
        try:
            current_time = datetime.now()
            cutoff_time = current_time - timedelta(hours=24)
            
            # 清理旧的登录尝试记录
            for ip_address, attempts in list(self.login_attempts.items()):
                # 保留最近24小时的记录
                recent_attempts = deque([
                    attempt for attempt in attempts
                    if attempt["timestamp"] > cutoff_time
                ], maxlen=100)
                
                if recent_attempts:
                    self.login_attempts[ip_address] = recent_attempts
                else:
                    del self.login_attempts[ip_address]
            
            # 清理旧的请求记录
            for ip_address, requests in list(self.request_counts.items()):
                recent_requests = deque([
                    req for req in requests
                    if req["timestamp"] > cutoff_time
                ], maxlen=1000)
                
                if recent_requests:
                    self.request_counts[ip_address] = recent_requests
                else:
                    del self.request_counts[ip_address]
            
        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")

    # 辅助方法（简化实现）
    async def _get_user_login_history(self, user_id: str) -> Dict:
        """获取用户登录历史"""
        # 简化实现，返回模拟数据
        return {"known_ips": set(), "login_times": []}

    async def _is_geo_anomaly(self, user_id: str, ip_address: str) -> bool:
        """检查地理位置异常"""
        # 简化实现
        return False

    async def _is_time_anomaly(self, user_id: str) -> bool:
        """检查时间异常"""
        # 简化实现
        return False

    async def _is_user_agent_anomaly(self, user_id: str, user_agent: str) -> bool:
        """检查User-Agent异常"""
        # 简化实现
        return False

    async def _is_unusual_activity_pattern(self, user_id: str, activities: deque) -> bool:
        """检查异常活动模式"""
        # 简化实现
        return len(activities) > 100  # 如果活动过于频繁


# 全局安全监控器实例
_security_monitor = None


def get_security_monitor() -> SecurityMonitor:
    """获取安全监控器实例"""
    global _security_monitor
    if _security_monitor is None:
        _security_monitor = SecurityMonitor()
    return _security_monitor
