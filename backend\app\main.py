from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import HTT<PERSON><PERSON>earer
from contextlib import asynccontextmanager
import uvicorn
from loguru import logger

from .database.connection import init_db, close_db
from .api import auth, transactions, accounts, budgets, reports, ai, ai_config, ai_categorization, feedback, rate_limit, analytics, i18n, monitoring_simple, batch, advanced_ai
from .api import security as security_api
from .auth.dependencies import get_current_user
from .config import settings
from .middleware.rate_limit import RateLimitMiddleware as MainRateLimitMiddleware
from .middleware.i18n import I18nMiddleware
from .middleware.security import SecurityMiddleware, RateLimitMiddleware as SecurityRateLimitMiddleware, SecurityHeadersMiddleware
from .middleware.performance import (
    PerformanceMiddleware, SystemMetricsMiddleware, DatabaseMetricsMiddleware,
    CacheMetricsMiddleware, AIMetricsMiddleware, ErrorTrackingMiddleware, HealthCheckMiddleware
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化数据库
    logger.info("正在初始化数据库连接...")
    await init_db()
    logger.info("数据库连接初始化完成")
    
    yield
    
    # 关闭时清理资源
    logger.info("正在关闭数据库连接...")
    await close_db()
    logger.info("数据库连接已关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title="AI记账应用API",
    description="智能记账应用的后端API服务",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan,
)

# 安全配置
security = HTTPBearer()

# 中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)

# 添加错误跟踪中间件
app.add_middleware(ErrorTrackingMiddleware)

# 添加健康检查中间件
app.add_middleware(HealthCheckMiddleware)

# 添加性能监控中间件
app.add_middleware(PerformanceMiddleware)

# 添加系统指标中间件
app.add_middleware(SystemMetricsMiddleware)

# 添加数据库指标中间件
app.add_middleware(DatabaseMetricsMiddleware)

# 添加缓存指标中间件
app.add_middleware(CacheMetricsMiddleware)

# 添加AI指标中间件
app.add_middleware(AIMetricsMiddleware)

# 添加安全头中间件
app.add_middleware(SecurityHeadersMiddleware)

# 添加安全监控中间件
app.add_middleware(SecurityMiddleware)

# 添加安全频率限制中间件
app.add_middleware(SecurityRateLimitMiddleware)

# 添加限流中间件
app.add_middleware(
    MainRateLimitMiddleware,
    enable_ip_limiting=True,
    enable_user_limiting=True,
    enable_global_limiting=True,
    excluded_paths=["/docs", "/redoc", "/openapi.json", "/health", "/metrics"]
)

# 添加国际化中间件
app.add_middleware(I18nMiddleware)


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "ai-accounting-api",
        "version": "1.0.0"
    }


# 根路径
@app.get("/")
async def root():
    """API根路径"""
    return {
        "message": "AI记账应用API",
        "version": "1.0.0",
        "docs": "/docs" if settings.DEBUG else "文档在生产环境中不可用"
    }


# 受保护的测试端点
@app.get("/protected")
async def protected_route(current_user=Depends(get_current_user)):
    """受保护的测试端点"""
    return {
        "message": f"Hello {current_user.email}!",
        "user_id": current_user.id
    }


# 注册API路由
app.include_router(
    auth.router,
    prefix="/api/v1/auth",
    tags=["认证"]
)

app.include_router(
    accounts.router,
    prefix="/api/v1/accounts",
    tags=["账户管理"],
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    transactions.router,
    prefix="/api/v1/transactions",
    tags=["交易记录"],
    dependencies=[Depends(get_current_user)]
)



app.include_router(
    budgets.router,
    prefix="/api/v1/budgets",
    tags=["预算管理"],
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    reports.router,
    prefix="/api/v1/reports",
    tags=["报表分析"],
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    ai.router,
    prefix="/api/v1/ai",
    tags=["AI功能"],
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    ai_config.router,
    prefix="/api/v1",
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    ai_categorization.router,
    prefix="/api/v1",
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    feedback.router,
    prefix="/api/v1",
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    rate_limit.router,
    prefix="/api/v1",
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    analytics.router,
    prefix="/api/v1",
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    i18n.router,
    prefix="/api/v1",
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    security_api.router,
    prefix="/api/v1",
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    monitoring_simple.router,
    prefix="/api/v1/monitoring",
    tags=["监控"],
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    batch.router,
    prefix="/api/v1/batch",
    tags=["批处理"],
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    advanced_ai.router,
    dependencies=[Depends(get_current_user)]
)


# 全局异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理器"""
    logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
    from fastapi.responses import JSONResponse
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    import traceback
    error_traceback = traceback.format_exc()
    logger.error(f"未处理的异常: {str(exc)}")
    logger.error(f"异常堆栈: {error_traceback}")
    print(f"ERROR: {str(exc)}")
    print(f"TRACEBACK: {error_traceback}")

    from fastapi.responses import JSONResponse
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": f"服务器内部错误: {str(exc)}",
            "status_code": 500,
            "traceback": error_traceback if settings.DEBUG else None
        }
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
