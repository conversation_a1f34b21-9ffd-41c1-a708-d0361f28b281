"""
异常检测和警报系统
检测异常支出模式并提供实时警报
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import asyncio
import json

from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc, or_
from sqlalchemy.orm import selectinload

from ..models.transaction import Transaction
from ..models.category import Category
from ..models.user import User
from ..models.alert import Alert
from ..utils.cache import get_cache_manager
from ..utils.structured_logger import structured_logger, LogCategory


class AnomalyType(Enum):
    """异常类型"""
    UNUSUAL_AMOUNT = "unusual_amount"           # 异常金额
    UNUSUAL_FREQUENCY = "unusual_frequency"     # 异常频率
    UNUSUAL_CATEGORY = "unusual_category"       # 异常分类
    UNUSUAL_TIME = "unusual_time"              # 异常时间
    BUDGET_EXCEEDED = "budget_exceeded"         # 预算超支
    SPENDING_SPIKE = "spending_spike"           # 支出激增
    DUPLICATE_TRANSACTION = "duplicate_transaction"  # 重复交易


class AlertSeverity(Enum):
    """警报严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class AnomalyDetection:
    """异常检测结果"""
    anomaly_type: AnomalyType
    severity: AlertSeverity
    confidence: float
    description: str
    affected_transaction_id: Optional[str]
    category_name: Optional[str]
    amount: Optional[float]
    detection_time: datetime
    suggested_actions: List[str]
    context: Dict[str, Any]


@dataclass
class SpendingPattern:
    """支出模式"""
    category_name: str
    typical_amount_range: Tuple[float, float]
    typical_frequency: float  # 每月平均次数
    typical_times: List[int]  # 典型时间段（小时）
    seasonal_factors: Dict[int, float]  # 月份季节因子
    volatility: float


class AnomalyDetectionSystem:
    """异常检测和警报系统"""
    
    def __init__(self):
        self.cache_ttl = 900  # 15分钟缓存
        self.learning_period_days = 90  # 学习期90天
        
        # 异常检测阈值
        self.thresholds = {
            'amount_z_score': 2.5,      # 金额Z分数阈值
            'frequency_multiplier': 3.0,  # 频率异常倍数
            'time_deviation_hours': 4,    # 时间偏差小时数
            'budget_warning_ratio': 0.8,  # 预算警告比例
            'budget_critical_ratio': 1.0, # 预算严重比例
            'spike_multiplier': 2.0,      # 激增倍数
            'duplicate_time_window': 300  # 重复交易时间窗口（秒）
        }
        
        # 分类风险权重
        self.category_risk_weights = {
            '购物': 0.8,
            '娱乐': 0.7,
            '餐饮': 0.5,
            '交通': 0.6,
            '住房': 0.9,
            '医疗': 0.9,
            '教育': 0.8,
            '其他': 0.6
        }
    
    async def detect_transaction_anomalies(
        self,
        user_id: str,
        transaction_id: str,
        db: AsyncSession
    ) -> List[AnomalyDetection]:
        """检测单个交易的异常"""
        
        try:
            # 获取交易信息
            transaction = await self._get_transaction(transaction_id, db)
            if not transaction or transaction.user_id != user_id:
                return []
            
            # 获取用户支出模式
            patterns = await self._get_user_spending_patterns(user_id, db)
            
            anomalies = []
            
            # 检测金额异常
            amount_anomaly = await self._detect_amount_anomaly(
                transaction, patterns, user_id, db
            )
            if amount_anomaly:
                anomalies.append(amount_anomaly)
            
            # 检测频率异常
            frequency_anomaly = await self._detect_frequency_anomaly(
                transaction, patterns, user_id, db
            )
            if frequency_anomaly:
                anomalies.append(frequency_anomaly)
            
            # 检测时间异常
            time_anomaly = await self._detect_time_anomaly(
                transaction, patterns, user_id, db
            )
            if time_anomaly:
                anomalies.append(time_anomaly)
            
            # 检测重复交易
            duplicate_anomaly = await self._detect_duplicate_transaction(
                transaction, user_id, db
            )
            if duplicate_anomaly:
                anomalies.append(duplicate_anomaly)
            
            # 检测预算超支
            budget_anomaly = await self._detect_budget_anomaly(
                transaction, user_id, db
            )
            if budget_anomaly:
                anomalies.append(budget_anomaly)
            
            return anomalies
            
        except Exception as e:
            logger.error(f"检测交易异常失败: {e}")
            return []
    
    async def detect_spending_patterns_anomalies(
        self,
        user_id: str,
        db: AsyncSession,
        days: int = 7
    ) -> List[AnomalyDetection]:
        """检测支出模式异常"""
        
        try:
            # 获取最近的支出数据
            recent_transactions = await self._get_recent_transactions(
                user_id, db, days
            )
            
            if not recent_transactions:
                return []
            
            # 获取历史支出模式
            patterns = await self._get_user_spending_patterns(user_id, db)
            
            anomalies = []
            
            # 检测支出激增
            spike_anomaly = await self._detect_spending_spike(
                recent_transactions, patterns, user_id, db
            )
            if spike_anomaly:
                anomalies.append(spike_anomaly)
            
            # 检测分类异常
            category_anomalies = await self._detect_category_anomalies(
                recent_transactions, patterns, user_id, db
            )
            anomalies.extend(category_anomalies)
            
            return anomalies
            
        except Exception as e:
            logger.error(f"检测支出模式异常失败: {e}")
            return []
    
    async def _get_transaction(
        self, transaction_id: str, db: AsyncSession
    ) -> Optional[Transaction]:
        """获取交易信息"""
        
        query = select(Transaction).where(
            Transaction.id == transaction_id
        ).options(selectinload(Transaction.category))
        
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def _get_user_spending_patterns(
        self, user_id: str, db: AsyncSession
    ) -> Dict[str, SpendingPattern]:
        """获取用户支出模式"""
        
        # 检查缓存
        cache = await get_cache_manager()
        cache_key = f"spending_patterns:{user_id}"
        
        cached_patterns = await cache.get(cache_key)
        if cached_patterns:
            return {
                name: SpendingPattern(**pattern) 
                for name, pattern in cached_patterns.items()
            }
        
        # 获取历史数据
        cutoff_date = datetime.now() - timedelta(days=self.learning_period_days)
        
        query = select(Transaction).where(
            and_(
                Transaction.user_id == user_id,
                Transaction.transaction_type == 'expense',
                Transaction.transaction_date >= cutoff_date
            )
        ).options(selectinload(Transaction.category))
        
        result = await db.execute(query)
        transactions = result.scalars().all()
        
        if not transactions:
            return {}
        
        # 分析每个分类的模式
        patterns = {}
        
        # 按分类分组
        category_groups = {}
        for trans in transactions:
            category_name = trans.category.name if trans.category else '未分类'
            if category_name not in category_groups:
                category_groups[category_name] = []
            category_groups[category_name].append(trans)
        
        for category_name, category_transactions in category_groups.items():
            pattern = await self._analyze_category_pattern(category_transactions)
            if pattern:
                patterns[category_name] = pattern
        
        # 缓存结果
        cache_data = {
            name: pattern.__dict__ 
            for name, pattern in patterns.items()
        }
        await cache.set(cache_key, cache_data, self.cache_ttl)
        
        return patterns
    
    async def _analyze_category_pattern(
        self, transactions: List[Transaction]
    ) -> Optional[SpendingPattern]:
        """分析分类支出模式"""
        
        if len(transactions) < 5:  # 数据太少
            return None
        
        amounts = [float(trans.amount) for trans in transactions]
        times = [trans.transaction_date.hour for trans in transactions]
        months = [trans.transaction_date.month for trans in transactions]
        
        # 计算金额范围（使用IQR方法）
        q1, q3 = np.percentile(amounts, [25, 75])
        iqr = q3 - q1
        typical_min = max(0, q1 - 1.5 * iqr)
        typical_max = q3 + 1.5 * iqr
        
        # 计算频率（每月平均次数）
        df = pd.DataFrame({
            'date': [trans.transaction_date for trans in transactions],
            'amount': amounts
        })
        df['month'] = df['date'].dt.to_period('M')
        monthly_counts = df.groupby('month').size()
        typical_frequency = monthly_counts.mean()
        
        # 计算典型时间段
        time_hist, time_bins = np.histogram(times, bins=24, range=(0, 24))
        typical_times = [
            int(time_bins[i]) for i in range(len(time_hist)) 
            if time_hist[i] > len(transactions) * 0.1
        ]
        
        # 计算季节因子
        seasonal_factors = {}
        for month in range(1, 13):
            month_amounts = [amt for amt, m in zip(amounts, months) if m == month]
            if month_amounts:
                seasonal_factors[month] = np.mean(month_amounts) / np.mean(amounts)
            else:
                seasonal_factors[month] = 1.0
        
        # 计算波动性
        volatility = np.std(amounts) / np.mean(amounts) if np.mean(amounts) > 0 else 0
        
        return SpendingPattern(
            category_name=transactions[0].category.name if transactions[0].category else '未分类',
            typical_amount_range=(typical_min, typical_max),
            typical_frequency=typical_frequency,
            typical_times=typical_times,
            seasonal_factors=seasonal_factors,
            volatility=volatility
        )

    async def _detect_amount_anomaly(
        self,
        transaction: Transaction,
        patterns: Dict[str, SpendingPattern],
        user_id: str,
        db: AsyncSession
    ) -> Optional[AnomalyDetection]:
        """检测金额异常"""

        category_name = transaction.category.name if transaction.category else '未分类'
        amount = float(transaction.amount)

        if category_name not in patterns:
            return None

        pattern = patterns[category_name]
        typical_min, typical_max = pattern.typical_amount_range

        # 检查是否超出典型范围
        if amount < typical_min or amount > typical_max:
            # 计算异常程度
            if amount > typical_max:
                deviation = (amount - typical_max) / typical_max
                severity = self._calculate_severity(deviation, 'amount_high')
                description = f"{category_name}支出金额异常偏高: {amount:.2f}元 (典型范围: {typical_min:.2f}-{typical_max:.2f}元)"
            else:
                deviation = (typical_min - amount) / typical_min
                severity = self._calculate_severity(deviation, 'amount_low')
                description = f"{category_name}支出金额异常偏低: {amount:.2f}元 (典型范围: {typical_min:.2f}-{typical_max:.2f}元)"

            # 计算置信度
            confidence = min(deviation, 1.0)

            return AnomalyDetection(
                anomaly_type=AnomalyType.UNUSUAL_AMOUNT,
                severity=severity,
                confidence=confidence,
                description=description,
                affected_transaction_id=transaction.id,
                category_name=category_name,
                amount=amount,
                detection_time=datetime.now(),
                suggested_actions=[
                    "检查交易是否正确",
                    "确认是否为特殊情况",
                    "考虑调整该分类的预算"
                ],
                context={
                    'typical_range': [typical_min, typical_max],
                    'deviation': deviation,
                    'pattern_volatility': pattern.volatility
                }
            )

        return None

    async def _detect_frequency_anomaly(
        self,
        transaction: Transaction,
        patterns: Dict[str, SpendingPattern],
        user_id: str,
        db: AsyncSession
    ) -> Optional[AnomalyDetection]:
        """检测频率异常"""

        category_name = transaction.category.name if transaction.category else '未分类'

        if category_name not in patterns:
            return None

        pattern = patterns[category_name]

        # 获取本月该分类的交易次数
        current_month = datetime.now().replace(day=1)
        next_month = (current_month + timedelta(days=32)).replace(day=1)

        query = select(func.count(Transaction.id)).where(
            and_(
                Transaction.user_id == user_id,
                Transaction.category_id == transaction.category_id,
                Transaction.transaction_date >= current_month,
                Transaction.transaction_date < next_month
            )
        )

        result = await db.execute(query)
        current_frequency = result.scalar() or 0

        # 检查频率是否异常
        if current_frequency > pattern.typical_frequency * self.thresholds['frequency_multiplier']:
            deviation = current_frequency / pattern.typical_frequency
            severity = self._calculate_severity(deviation - 1, 'frequency')

            return AnomalyDetection(
                anomaly_type=AnomalyType.UNUSUAL_FREQUENCY,
                severity=severity,
                confidence=min(deviation / self.thresholds['frequency_multiplier'], 1.0),
                description=f"{category_name}本月交易频率异常: {current_frequency}次 (典型: {pattern.typical_frequency:.1f}次)",
                affected_transaction_id=transaction.id,
                category_name=category_name,
                amount=float(transaction.amount),
                detection_time=datetime.now(),
                suggested_actions=[
                    "检查是否有重复交易",
                    "确认消费习惯是否发生变化",
                    "考虑设置该分类的频率提醒"
                ],
                context={
                    'current_frequency': current_frequency,
                    'typical_frequency': pattern.typical_frequency,
                    'deviation_ratio': deviation
                }
            )

        return None

    async def _detect_time_anomaly(
        self,
        transaction: Transaction,
        patterns: Dict[str, SpendingPattern],
        user_id: str,
        db: AsyncSession
    ) -> Optional[AnomalyDetection]:
        """检测时间异常"""

        category_name = transaction.category.name if transaction.category else '未分类'
        transaction_hour = transaction.transaction_date.hour

        if category_name not in patterns:
            return None

        pattern = patterns[category_name]

        # 检查是否在典型时间段内
        if pattern.typical_times:
            min_deviation = min(
                abs(transaction_hour - typical_hour)
                for typical_hour in pattern.typical_times
            )

            if min_deviation > self.thresholds['time_deviation_hours']:
                severity = self._calculate_severity(
                    min_deviation / 12, 'time'  # 12小时为最大偏差
                )

                return AnomalyDetection(
                    anomaly_type=AnomalyType.UNUSUAL_TIME,
                    severity=severity,
                    confidence=min(min_deviation / 12, 1.0),
                    description=f"{category_name}在异常时间发生交易: {transaction_hour}:00 (典型时间: {pattern.typical_times})",
                    affected_transaction_id=transaction.id,
                    category_name=category_name,
                    amount=float(transaction.amount),
                    detection_time=datetime.now(),
                    suggested_actions=[
                        "确认交易时间是否正确",
                        "检查是否为紧急或特殊情况",
                        "考虑调整消费时间习惯"
                    ],
                    context={
                        'transaction_hour': transaction_hour,
                        'typical_hours': pattern.typical_times,
                        'min_deviation': min_deviation
                    }
                )

        return None

    async def _detect_duplicate_transaction(
        self,
        transaction: Transaction,
        user_id: str,
        db: AsyncSession
    ) -> Optional[AnomalyDetection]:
        """检测重复交易"""

        # 在时间窗口内查找相似交易
        time_window_start = transaction.transaction_date - timedelta(
            seconds=self.thresholds['duplicate_time_window']
        )
        time_window_end = transaction.transaction_date + timedelta(
            seconds=self.thresholds['duplicate_time_window']
        )

        query = select(Transaction).where(
            and_(
                Transaction.user_id == user_id,
                Transaction.id != transaction.id,
                Transaction.amount == transaction.amount,
                Transaction.category_id == transaction.category_id,
                Transaction.transaction_date >= time_window_start,
                Transaction.transaction_date <= time_window_end
            )
        )

        result = await db.execute(query)
        similar_transactions = result.scalars().all()

        if similar_transactions:
            # 进一步检查描述相似性
            for similar_trans in similar_transactions:
                similarity = self._calculate_description_similarity(
                    transaction.description, similar_trans.description
                )

                if similarity > 0.8:  # 高相似度
                    return AnomalyDetection(
                        anomaly_type=AnomalyType.DUPLICATE_TRANSACTION,
                        severity=AlertSeverity.HIGH,
                        confidence=similarity,
                        description=f"检测到可能的重复交易: {transaction.description} ({transaction.amount}元)",
                        affected_transaction_id=transaction.id,
                        category_name=transaction.category.name if transaction.category else '未分类',
                        amount=float(transaction.amount),
                        detection_time=datetime.now(),
                        suggested_actions=[
                            "检查是否为重复记录",
                            "确认交易是否真实发生",
                            "如果是重复，请删除多余记录"
                        ],
                        context={
                            'similar_transaction_id': similar_trans.id,
                            'similarity_score': similarity,
                            'time_difference': abs(
                                (transaction.transaction_date - similar_trans.transaction_date).total_seconds()
                            )
                        }
                    )

        return None

    async def _detect_budget_anomaly(
        self,
        transaction: Transaction,
        user_id: str,
        db: AsyncSession
    ) -> Optional[AnomalyDetection]:
        """检测预算超支异常"""

        if not transaction.category:
            return None

        # 获取当前月份的预算
        current_month = datetime.now().replace(day=1)
        next_month = (current_month + timedelta(days=32)).replace(day=1)

        from ..models.budget import Budget
        query = select(Budget).where(
            and_(
                Budget.user_id == user_id,
                Budget.category_id == transaction.category_id,
                Budget.period_start <= current_month,
                Budget.period_end >= current_month
            )
        )

        result = await db.execute(query)
        budget = result.scalar_one_or_none()

        if not budget:
            return None

        # 计算本月已用预算
        query = select(func.sum(Transaction.amount)).where(
            and_(
                Transaction.user_id == user_id,
                Transaction.category_id == transaction.category_id,
                Transaction.transaction_date >= current_month,
                Transaction.transaction_date < next_month,
                Transaction.transaction_type == 'expense'
            )
        )

        result = await db.execute(query)
        used_budget = float(result.scalar() or 0)

        budget_amount = float(budget.amount)
        usage_ratio = used_budget / budget_amount if budget_amount > 0 else 0

        # 检查预算使用情况
        if usage_ratio >= self.thresholds['budget_critical_ratio']:
            severity = AlertSeverity.CRITICAL
            description = f"{transaction.category.name}预算已超支: {used_budget:.2f}/{budget_amount:.2f}元 ({usage_ratio:.1%})"
        elif usage_ratio >= self.thresholds['budget_warning_ratio']:
            severity = AlertSeverity.HIGH
            description = f"{transaction.category.name}预算即将超支: {used_budget:.2f}/{budget_amount:.2f}元 ({usage_ratio:.1%})"
        else:
            return None

        return AnomalyDetection(
            anomaly_type=AnomalyType.BUDGET_EXCEEDED,
            severity=severity,
            confidence=min(usage_ratio, 1.0),
            description=description,
            affected_transaction_id=transaction.id,
            category_name=transaction.category.name,
            amount=float(transaction.amount),
            detection_time=datetime.now(),
            suggested_actions=[
                "检查预算设置是否合理",
                "考虑调整消费计划",
                "评估是否需要增加预算"
            ],
            context={
                'budget_amount': budget_amount,
                'used_budget': used_budget,
                'usage_ratio': usage_ratio,
                'remaining_budget': budget_amount - used_budget
            }
        )

    async def _get_recent_transactions(
        self, user_id: str, db: AsyncSession, days: int
    ) -> List[Transaction]:
        """获取最近的交易"""

        cutoff_date = datetime.now() - timedelta(days=days)

        query = select(Transaction).where(
            and_(
                Transaction.user_id == user_id,
                Transaction.transaction_type == 'expense',
                Transaction.transaction_date >= cutoff_date
            )
        ).options(selectinload(Transaction.category)).order_by(desc(Transaction.transaction_date))

        result = await db.execute(query)
        return result.scalars().all()

    async def _detect_spending_spike(
        self,
        recent_transactions: List[Transaction],
        patterns: Dict[str, SpendingPattern],
        user_id: str,
        db: AsyncSession
    ) -> Optional[AnomalyDetection]:
        """检测支出激增"""

        if len(recent_transactions) < 5:
            return None

        # 计算最近几天的日均支出
        recent_daily_amounts = {}
        for trans in recent_transactions:
            date_key = trans.transaction_date.date()
            if date_key not in recent_daily_amounts:
                recent_daily_amounts[date_key] = 0
            recent_daily_amounts[date_key] += float(trans.amount)

        recent_avg = np.mean(list(recent_daily_amounts.values()))

        # 获取历史日均支出
        historical_avg = await self._get_historical_daily_average(user_id, db)

        if historical_avg == 0:
            return None

        spike_ratio = recent_avg / historical_avg

        if spike_ratio > self.thresholds['spike_multiplier']:
            severity = self._calculate_severity(spike_ratio - 1, 'spike')

            return AnomalyDetection(
                anomaly_type=AnomalyType.SPENDING_SPIKE,
                severity=severity,
                confidence=min(spike_ratio / self.thresholds['spike_multiplier'], 1.0),
                description=f"检测到支出激增: 近期日均{recent_avg:.2f}元，历史日均{historical_avg:.2f}元 (增长{spike_ratio:.1f}倍)",
                affected_transaction_id=None,
                category_name=None,
                amount=recent_avg,
                detection_time=datetime.now(),
                suggested_actions=[
                    "检查最近的大额支出",
                    "确认是否有特殊消费需求",
                    "考虑调整消费计划"
                ],
                context={
                    'recent_daily_average': recent_avg,
                    'historical_daily_average': historical_avg,
                    'spike_ratio': spike_ratio,
                    'days_analyzed': len(recent_daily_amounts)
                }
            )

        return None

    async def _detect_category_anomalies(
        self,
        recent_transactions: List[Transaction],
        patterns: Dict[str, SpendingPattern],
        user_id: str,
        db: AsyncSession
    ) -> List[AnomalyDetection]:
        """检测分类异常"""

        anomalies = []

        # 按分类分组最近交易
        category_groups = {}
        for trans in recent_transactions:
            category_name = trans.category.name if trans.category else '未分类'
            if category_name not in category_groups:
                category_groups[category_name] = []
            category_groups[category_name].append(trans)

        # 检查每个分类
        for category_name, transactions in category_groups.items():
            if category_name not in patterns:
                # 新分类异常
                if len(transactions) > 3:  # 短期内多次新分类消费
                    total_amount = sum(float(t.amount) for t in transactions)
                    anomaly = AnomalyDetection(
                        anomaly_type=AnomalyType.UNUSUAL_CATEGORY,
                        severity=AlertSeverity.MEDIUM,
                        confidence=0.7,
                        description=f"检测到新的消费分类: {category_name} (近期{len(transactions)}笔，共{total_amount:.2f}元)",
                        affected_transaction_id=transactions[0].id,
                        category_name=category_name,
                        amount=total_amount,
                        detection_time=datetime.now(),
                        suggested_actions=[
                            "确认分类是否正确",
                            "考虑为新分类设置预算",
                            "检查是否需要调整消费习惯"
                        ],
                        context={
                            'transaction_count': len(transactions),
                            'total_amount': total_amount,
                            'is_new_category': True
                        }
                    )
                    anomalies.append(anomaly)

        return anomalies

    async def _get_historical_daily_average(
        self, user_id: str, db: AsyncSession, days: int = 90
    ) -> float:
        """获取历史日均支出"""

        cutoff_date = datetime.now() - timedelta(days=days)

        query = select(func.sum(Transaction.amount)).where(
            and_(
                Transaction.user_id == user_id,
                Transaction.transaction_type == 'expense',
                Transaction.transaction_date >= cutoff_date
            )
        )

        result = await db.execute(query)
        total_amount = float(result.scalar() or 0)

        return total_amount / days if days > 0 else 0

    def _calculate_severity(self, deviation: float, anomaly_context: str) -> AlertSeverity:
        """计算异常严重程度"""

        # 根据不同类型的异常调整阈值
        thresholds = {
            'amount_high': [0.5, 1.0, 2.0],
            'amount_low': [0.3, 0.6, 1.0],
            'frequency': [1.0, 2.0, 3.0],
            'time': [0.3, 0.6, 0.8],
            'spike': [1.0, 2.0, 3.0]
        }

        severity_thresholds = thresholds.get(anomaly_context, [0.5, 1.0, 2.0])

        if deviation >= severity_thresholds[2]:
            return AlertSeverity.CRITICAL
        elif deviation >= severity_thresholds[1]:
            return AlertSeverity.HIGH
        elif deviation >= severity_thresholds[0]:
            return AlertSeverity.MEDIUM
        else:
            return AlertSeverity.LOW

    def _calculate_description_similarity(self, desc1: str, desc2: str) -> float:
        """计算描述相似度"""

        if not desc1 or not desc2:
            return 0.0

        # 简单的词汇重叠相似度
        words1 = set(desc1.lower().split())
        words2 = set(desc2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0
