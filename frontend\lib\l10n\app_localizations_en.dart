import 'app_localizations.dart';

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  // Common
  @override
  String get success => 'Success';

  @override
  String get error => 'Error';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Information';

  @override
  String get loading => 'Loading...';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get add => 'Add';

  @override
  String get search => 'Search';

  @override
  String get filter => 'Filter';

  @override
  String get export => 'Export';

  @override
  String get import => 'Import';

  @override
  String get refresh => 'Refresh';

  @override
  String get back => 'Back';

  @override
  String get next => 'Next';

  @override
  String get previous => 'Previous';

  @override
  String get submit => 'Submit';

  @override
  String get reset => 'Reset';

  // Auth
  @override
  String get login => 'Login';

  @override
  String get logout => 'Logout';

  @override
  String get register => 'Register';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get username => 'Username';

  @override
  String get loginSuccess => 'Login successful';

  @override
  String get loginFailed => 'Login failed';

  @override
  String get registerSuccess => 'Registration successful';

  @override
  String get registerFailed => 'Registration failed';

  @override
  String get invalidCredentials => 'Invalid username or password';

  @override
  String get emailAlreadyExists => 'Email already exists';

  @override
  String get usernameAlreadyExists => 'Username already exists';

  // Transaction
  @override
  String get transaction => 'Transaction';

  @override
  String get transactions => 'Transactions';

  @override
  String get amount => 'Amount';

  @override
  String get description => 'Description';

  @override
  String get category => 'Category';

  @override
  String get date => 'Date';

  @override
  String get type => 'Type';

  @override
  String get income => 'Income';

  @override
  String get expense => 'Expense';

  @override
  String get addTransaction => 'Add Transaction';

  @override
  String get editTransaction => 'Edit Transaction';

  @override
  String get deleteTransaction => 'Delete Transaction';

  @override
  String get transactionAdded => 'Transaction added successfully';

  @override
  String get transactionUpdated => 'Transaction updated successfully';

  @override
  String get transactionDeleted => 'Transaction deleted successfully';

  // Category
  @override
  String get categories => 'Categories';

  @override
  String get categoryName => 'Category Name';

  @override
  String get categoryColor => 'Category Color';

  @override
  String get categoryType => 'Category Type';

  @override
  String get addCategory => 'Add Category';

  @override
  String get editCategory => 'Edit Category';

  @override
  String get deleteCategory => 'Delete Category';

  @override
  String get categoryAdded => 'Category added successfully';

  @override
  String get categoryUpdated => 'Category updated successfully';

  @override
  String get categoryDeleted => 'Category deleted successfully';

  // Budget
  @override
  String get budget => 'Budget';

  @override
  String get budgets => 'Budgets';

  @override
  String get budgetAmount => 'Budget Amount';

  @override
  String get spentAmount => 'Spent Amount';

  @override
  String get remainingAmount => 'Remaining Amount';

  @override
  String get budgetPeriod => 'Budget Period';

  @override
  String get monthly => 'Monthly';

  @override
  String get yearly => 'Yearly';

  @override
  String get addBudget => 'Add Budget';

  @override
  String get editBudget => 'Edit Budget';

  @override
  String get deleteBudget => 'Delete Budget';

  @override
  String get budgetExceeded => 'Budget Exceeded';

  @override
  String get budgetWarning => 'Budget Warning';

  // AI
  @override
  String get aiCategorization => 'AI Categorization';

  @override
  String get aiConfig => 'AI Configuration';

  @override
  String get apiKey => 'API Key';

  @override
  String get modelName => 'Model Name';

  @override
  String get provider => 'Provider';

  @override
  String get categorize => 'Categorize';

  @override
  String get categorizing => 'Categorizing...';

  @override
  String get categorizationSuccess => 'Categorization successful';

  @override
  String get categorizationFailed => 'Categorization failed';

  @override
  String get invalidApiKey => 'Invalid API key';

  @override
  String get aiServiceUnavailable => 'AI service unavailable';

  // Analytics
  @override
  String get analytics => 'Analytics';

  @override
  String get spendingPatterns => 'Spending Patterns';

  @override
  String get budgetRecommendations => 'Budget Recommendations';

  @override
  String get financialHealth => 'Financial Health';

  @override
  String get trends => 'Trends';

  @override
  String get insights => 'Insights';

  @override
  String get predictions => 'Predictions';

  @override
  String get comparison => 'Comparison';

  @override
  String get healthScore => 'Health Score';

  @override
  String get excellent => 'Excellent';

  @override
  String get good => 'Good';

  @override
  String get average => 'Average';

  @override
  String get needsImprovement => 'Needs Improvement';

  // Errors
  @override
  String get networkError => 'Network Error';

  @override
  String get serverError => 'Server Error';

  @override
  String get validationError => 'Validation Error';

  @override
  String get permissionDenied => 'Permission Denied';

  @override
  String get notFound => 'Not Found';

  @override
  String get rateLimitExceeded => 'Rate Limit Exceeded';

  @override
  String get insufficientData => 'Insufficient Data';

  @override
  String get operationFailed => 'Operation Failed';

  // Security
  @override
  String get securityDashboard => 'Security Dashboard';

  @override
  String get overview => 'Overview';

  @override
  String get alerts => 'Alerts';

  @override
  String get auditLog => 'Audit Log';

  @override
  String get keyRotation => 'Key Rotation';

  @override
  String get securityStatus => 'Security Status';

  @override
  String get active => 'Active';

  @override
  String get inactive => 'Inactive';

  @override
  String get blockedIPs => 'Blocked IPs';

  @override
  String get suspiciousIPs => 'Suspicious IPs';

  @override
  String get totalAlerts => 'Total Alerts';

  @override
  String get alertsByThreatLevel => 'Alerts by Threat Level';

  @override
  String get critical => 'Critical';

  @override
  String get high => 'High';

  @override
  String get medium => 'Medium';

  @override
  String get low => 'Low';

  @override
  String get recentAlerts => 'Recent Alerts';

  @override
  String get viewAll => 'View All';

  @override
  String get noRecentAlerts => 'No Recent Alerts';

  @override
  String get noSecurityAlerts => 'No Security Alerts';

  @override
  String get scheduleRotation => 'Schedule Rotation';
}
