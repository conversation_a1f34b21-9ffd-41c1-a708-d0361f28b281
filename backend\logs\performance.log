2025-07-30 10:13:46 | DEBUG | app.utils.structured_logger:_log_structured:200 | {"timestamp": "2025-07-30T10:13:46.494731", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.0009732246398925781 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "user_id": null, "session_id": null, "request_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.0009732246398925781, "unit": "seconds", "endpoint": "GET /", "status_code": 200, "user_id": null}}
2025-07-30 10:33:32 | DEBUG | app.utils.structured_logger:_log_structured:200 | {"timestamp": "2025-07-30T10:33:32.390684", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.03194069862365723 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "user_id": null, "session_id": null, "request_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.03194069862365723, "unit": "seconds", "endpoint": "POST /api/v1/auth/register", "status_code": 400, "user_id": null}}
2025-07-30 10:33:45 | DEBUG | app.utils.structured_logger:_log_structured:200 | {"timestamp": "2025-07-30T10:33:45.671323", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.006429195404052734 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "user_id": null, "session_id": null, "request_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.006429195404052734, "unit": "seconds", "endpoint": "POST /api/v1/auth/register", "status_code": 400, "user_id": null}}
2025-07-30 10:34:25 | DEBUG | app.utils.structured_logger:_log_structured:200 | {"timestamp": "2025-07-30T10:34:25.912130", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.006928443908691406 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "user_id": null, "session_id": null, "request_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.006928443908691406, "unit": "seconds", "endpoint": "POST /api/v1/auth/register", "status_code": 400, "user_id": null}}
2025-07-30 10:35:19 | DEBUG | app.utils.structured_logger:_log_structured:200 | {"timestamp": "2025-07-30T10:35:19.068837", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.009354352951049805 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "user_id": null, "session_id": null, "request_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.009354352951049805, "unit": "seconds", "endpoint": "POST /api/v1/auth/register", "status_code": 400, "user_id": null}}
2025-07-30 10:35:31 | DEBUG | app.utils.structured_logger:_log_structured:200 | {"timestamp": "2025-07-30T10:35:31.412310", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.30794572830200195 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "user_id": null, "session_id": null, "request_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.30794572830200195, "unit": "seconds", "endpoint": "POST /api/v1/auth/login", "status_code": 200, "user_id": null}}
2025-07-30 10:35:39 | DEBUG | app.utils.structured_logger:_log_structured:200 | {"timestamp": "2025-07-30T10:35:39.662261", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.21198654174804688 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "user_id": null, "session_id": null, "request_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.21198654174804688, "unit": "seconds", "endpoint": "POST /api/v1/batch/transactions", "status_code": 500, "user_id": "cc80f617-b43c-4746-97f2-e6c22edf2d50"}}
2025-07-30 10:36:03 | DEBUG | app.utils.structured_logger:_log_structured:200 | {"timestamp": "2025-07-30T10:36:03.603398", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.0009944438934326172 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "user_id": null, "session_id": null, "request_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.0009944438934326172, "unit": "seconds", "endpoint": "GET /", "status_code": 200, "user_id": null}}
2025-07-30 10:36:11 | DEBUG | app.utils.structured_logger:_log_structured:200 | {"timestamp": "2025-07-30T10:36:11.935421", "level": "DEBUG", "category": "performance", "message": "性能指标: api_response_time = 0.3089447021484375 seconds", "module": "E:\\relax\\python\\源码\\记账报销软件\\backend\\app\\utils\\performance_monitor.py", "function": "record_request", "line": 111, "user_id": null, "session_id": null, "request_id": null, "ip_address": null, "user_agent": null, "endpoint": null, "method": null, "status_code": null, "response_time": null, "error_type": null, "error_message": null, "stack_trace": null, "extra_data": {"metric_name": "api_response_time", "value": 0.3089447021484375, "unit": "seconds", "endpoint": "POST /api/v1/auth/login", "status_code": 200, "user_id": null}}
