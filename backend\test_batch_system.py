"""
批处理系统测试脚本
测试批量导入、导出、处理等功能
"""

import asyncio
import json
import csv
import io
from datetime import datetime, timedelta
import httpx
from loguru import logger

# 测试配置
BASE_URL = "http://localhost:8003"
TEST_USER = {
    "email": "<EMAIL>",
    "password": "testpassword123"
}

class BatchSystemTester:
    def __init__(self):
        self.client = httpx.AsyncClient(base_url=BASE_URL)
        self.token = None
        self.headers = {}

    async def setup(self):
        """设置测试环境"""
        try:
            # 注册测试用户
            register_response = await self.client.post("/api/v1/auth/register", json=TEST_USER)
            if register_response.status_code not in [200, 400]:  # 400表示用户已存在
                logger.error(f"注册失败: {register_response.text}")
                return False

            # 登录获取token
            login_response = await self.client.post("/api/v1/auth/login", json=TEST_USER)
            if login_response.status_code != 200:
                logger.error(f"登录失败: {login_response.text}")
                return False

            token_data = login_response.json()
            self.token = token_data["access_token"]
            self.headers = {"Authorization": f"Bearer {self.token}"}
            
            logger.info("测试环境设置成功")
            return True
            
        except Exception as e:
            logger.error(f"设置测试环境失败: {e}")
            return False

    async def test_csv_import(self):
        """测试CSV导入功能"""
        logger.info("=== 测试CSV导入功能 ===")
        
        try:
            # 创建测试CSV数据
            csv_data = """transaction_date,amount,transaction_type,description,category_name,account_name,tags,location,notes
2024-01-01,100.50,expense,午餐,餐饮,现金账户,工作餐,公司附近,商务午餐
2024-01-02,5000.00,income,工资,工资,银行卡,月薪,,月度工资
2024-01-03,200.00,expense,购物,购物,信用卡,日用品,超市,生活用品
2024-01-04,50.00,expense,交通,交通,公交卡,地铁,上班,通勤费用
2024-01-05,1000.00,expense,房租,住房,银行卡,租金,小区,月租金"""

            # 准备文件数据
            files = {
                'file': ('test_transactions.csv', csv_data, 'text/csv')
            }
            data = {
                'delimiter': ',',
                'encoding': 'utf-8'
            }

            # 发送导入请求
            response = await self.client.post(
                "/api/v1/batch/import/csv",
                files=files,
                data=data,
                headers={"Authorization": f"Bearer {self.token}"}
            )

            if response.status_code == 200:
                result = response.json()
                job_id = result["job_id"]
                logger.info(f"CSV导入任务创建成功: {job_id}")
                
                # 监控任务状态
                await self.monitor_job_status(job_id)
                return True
            else:
                logger.error(f"CSV导入失败: {response.text}")
                return False

        except Exception as e:
            logger.error(f"CSV导入测试失败: {e}")
            return False

    async def test_json_import(self):
        """测试JSON导入功能"""
        logger.info("=== 测试JSON导入功能 ===")
        
        try:
            # 创建测试JSON数据
            json_data = [
                {
                    "transaction_date": "2024-02-01T12:00:00",
                    "amount": 150.75,
                    "transaction_type": "expense",
                    "description": "晚餐",
                    "category_name": "餐饮",
                    "account_name": "现金账户",
                    "tags": ["聚餐"],
                    "location": "餐厅",
                    "notes": "朋友聚餐"
                },
                {
                    "transaction_date": "2024-02-02T09:00:00",
                    "amount": 3000.00,
                    "transaction_type": "income",
                    "description": "奖金",
                    "category_name": "奖金",
                    "account_name": "银行卡",
                    "tags": ["年终奖"],
                    "location": "",
                    "notes": "年终奖金"
                }
            ]

            json_content = json.dumps(json_data, ensure_ascii=False, indent=2)

            # 准备文件数据
            files = {
                'file': ('test_transactions.json', json_content, 'application/json')
            }

            # 发送导入请求
            response = await self.client.post(
                "/api/v1/batch/import/json",
                files=files,
                headers={"Authorization": f"Bearer {self.token}"}
            )

            if response.status_code == 200:
                result = response.json()
                job_id = result["job_id"]
                logger.info(f"JSON导入任务创建成功: {job_id}")
                
                # 监控任务状态
                await self.monitor_job_status(job_id)
                return True
            else:
                logger.error(f"JSON导入失败: {response.text}")
                return False

        except Exception as e:
            logger.error(f"JSON导入测试失败: {e}")
            return False

    async def test_batch_transactions(self):
        """测试批量交易处理"""
        logger.info("=== 测试批量交易处理 ===")
        
        try:
            # 创建批量交易数据
            transactions = [
                {
                    "amount": 300.00,
                    "transaction_type": "expense",
                    "description": "批量测试1",
                    "transaction_date": datetime.now().isoformat()
                },
                {
                    "amount": 400.00,
                    "transaction_type": "expense",
                    "description": "批量测试2",
                    "transaction_date": datetime.now().isoformat()
                },
                {
                    "amount": 500.00,
                    "transaction_type": "income",
                    "description": "批量测试3",
                    "transaction_date": datetime.now().isoformat()
                }
            ]

            # 发送批量处理请求
            response = await self.client.post(
                "/api/v1/batch/transactions",
                json={
                    "transactions": transactions,
                    "operation_type": "create"
                },
                headers=self.headers
            )

            if response.status_code == 200:
                result = response.json()
                job_id = result["job_id"]
                logger.info(f"批量交易处理任务创建成功: {job_id}")
                
                # 监控任务状态
                await self.monitor_job_status(job_id)
                return True
            else:
                logger.error(f"批量交易处理失败: {response.text}")
                return False

        except Exception as e:
            logger.error(f"批量交易处理测试失败: {e}")
            return False

    async def test_export_functions(self):
        """测试导出功能"""
        logger.info("=== 测试导出功能 ===")
        
        try:
            # 测试CSV导出
            csv_response = await self.client.get(
                "/api/v1/batch/export/csv",
                headers=self.headers
            )
            
            if csv_response.status_code == 200:
                logger.info("CSV导出成功")
                logger.info(f"CSV内容预览: {csv_response.text[:200]}...")
            else:
                logger.error(f"CSV导出失败: {csv_response.text}")

            # 测试JSON导出
            json_response = await self.client.get(
                "/api/v1/batch/export/json",
                headers=self.headers
            )
            
            if json_response.status_code == 200:
                logger.info("JSON导出成功")
                logger.info(f"JSON内容预览: {json_response.text[:200]}...")
            else:
                logger.error(f"JSON导出失败: {json_response.text}")

            return csv_response.status_code == 200 and json_response.status_code == 200

        except Exception as e:
            logger.error(f"导出功能测试失败: {e}")
            return False

    async def test_job_management(self):
        """测试任务管理功能"""
        logger.info("=== 测试任务管理功能 ===")
        
        try:
            # 获取任务列表
            response = await self.client.get(
                "/api/v1/batch/jobs",
                headers=self.headers
            )

            if response.status_code == 200:
                jobs = response.json()
                logger.info(f"获取到 {len(jobs)} 个批处理任务")
                
                for job in jobs[:3]:  # 只显示前3个任务
                    logger.info(f"任务 {job['job_id'][:8]}... - 状态: {job['status']} - 进度: {job['progress']}%")
                
                return True
            else:
                logger.error(f"获取任务列表失败: {response.text}")
                return False

        except Exception as e:
            logger.error(f"任务管理测试失败: {e}")
            return False

    async def monitor_job_status(self, job_id: str, max_wait_time: int = 30):
        """监控任务状态"""
        logger.info(f"监控任务状态: {job_id}")
        
        start_time = datetime.now()
        while (datetime.now() - start_time).seconds < max_wait_time:
            try:
                response = await self.client.get(
                    f"/api/v1/batch/jobs/{job_id}",
                    headers=self.headers
                )
                
                if response.status_code == 200:
                    job = response.json()
                    status = job["status"]
                    progress = job["progress"]
                    
                    logger.info(f"任务状态: {status} - 进度: {progress}%")
                    
                    if status in ["completed", "failed", "cancelled"]:
                        logger.info(f"任务完成: 成功 {job['success_count']}, 失败 {job['error_count']}")
                        if job.get("errors"):
                            logger.warning(f"错误信息: {job['errors'][:2]}")  # 只显示前2个错误
                        break
                
                await asyncio.sleep(2)  # 等待2秒后再次检查
                
            except Exception as e:
                logger.error(f"监控任务状态失败: {e}")
                break

    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始批处理系统测试")
        
        # 设置测试环境
        if not await self.setup():
            logger.error("测试环境设置失败，退出测试")
            return

        test_results = []
        
        # 运行各项测试
        test_results.append(("CSV导入", await self.test_csv_import()))
        test_results.append(("JSON导入", await self.test_json_import()))
        test_results.append(("批量交易处理", await self.test_batch_transactions()))
        test_results.append(("导出功能", await self.test_export_functions()))
        test_results.append(("任务管理", await self.test_job_management()))

        # 输出测试结果
        logger.info("\n=== 测试结果汇总 ===")
        passed = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name}: {status}")
            if result:
                passed += 1

        logger.info(f"\n总计: {passed}/{len(test_results)} 项测试通过")
        
        await self.client.aclose()

async def main():
    """主函数"""
    tester = BatchSystemTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
