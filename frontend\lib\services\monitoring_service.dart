import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/api_config.dart';

class MonitoringService {
  static const String _baseUrl = '${ApiConfig.baseUrl}/monitoring';
  
  Future<String?> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  Future<Map<String, String>> _getHeaders() async {
    final token = await _getAuthToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// 获取系统健康状态
  Future<Map<String, dynamic>> getSystemHealth() async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/health'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to get system health: $e');
    }
  }

  /// 获取系统指标
  Future<Map<String, dynamic>> getMetrics({String format = 'json'}) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/metrics?format=$format'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        if (format.toLowerCase() == 'prometheus') {
          return {'data': response.body, 'format': 'prometheus'};
        } else {
          return json.decode(response.body) as Map<String, dynamic>;
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to get metrics: $e');
    }
  }

  /// 获取性能概览
  Future<Map<String, dynamic>> getPerformanceOverview() async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/performance/overview'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to get performance overview: $e');
    }
  }

  /// 获取性能趋势数据
  Future<Map<String, dynamic>> getPerformanceTrends({int hours = 24}) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/performance/trends?hours=$hours'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to get performance trends: $e');
    }
  }

  /// 获取端点性能统计
  Future<Map<String, dynamic>> getEndpointPerformance({String? endpoint}) async {
    try {
      final headers = await _getHeaders();
      final queryParams = endpoint != null ? '?endpoint=$endpoint' : '';
      final response = await http.get(
        Uri.parse('$_baseUrl/performance/endpoints$queryParams'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to get endpoint performance: $e');
    }
  }

  /// 导出指标数据
  Future<Map<String, dynamic>> exportMetrics({
    DateTime? startTime,
    DateTime? endTime,
    String format = 'json',
    bool includeSystem = true,
    bool includeHttp = true,
    bool includeDatabase = true,
    bool includeCache = true,
    bool includeAi = true,
  }) async {
    try {
      final headers = await _getHeaders();
      final requestBody = <String, dynamic>{
        'format': format,
        'include_system': includeSystem,
        'include_http': includeHttp,
        'include_database': includeDatabase,
        'include_cache': includeCache,
        'include_ai': includeAi,
      };

      if (startTime != null) {
        requestBody['start_time'] = startTime.toIso8601String();
      }

      if (endTime != null) {
        requestBody['end_time'] = endTime.toIso8601String();
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/metrics/export'),
        headers: headers,
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        if (format.toLowerCase() == 'prometheus') {
          return {'data': response.body, 'format': 'prometheus'};
        } else {
          return json.decode(response.body) as Map<String, dynamic>;
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to export metrics: $e');
    }
  }

  /// 获取指标汇总
  Future<Map<String, dynamic>> getMetricsSummary() async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/metrics/summary'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to get metrics summary: $e');
    }
  }

  /// 清理旧指标数据
  Future<Map<String, dynamic>> cleanupOldMetrics({int days = 7}) async {
    try {
      final headers = await _getHeaders();
      final response = await http.delete(
        Uri.parse('$_baseUrl/metrics/cleanup?days=$days'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to cleanup old metrics: $e');
    }
  }

  /// 获取性能警报
  Future<Map<String, dynamic>> getPerformanceAlerts() async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/alerts/performance'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to get performance alerts: $e');
    }
  }

  /// 获取监控仪表板数据
  Future<Map<String, dynamic>> getDashboardData() async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/dashboard/data'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to get dashboard data: $e');
    }
  }

  /// 获取实时系统状态
  Future<Map<String, dynamic>> getRealTimeStatus() async {
    try {
      // 并行获取多个数据源
      final results = await Future.wait([
        getSystemHealth(),
        getPerformanceOverview(),
        getPerformanceAlerts(),
      ]);

      return {
        'health': results[0],
        'performance': results[1],
        'alerts': results[2],
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('Failed to get real-time status: $e');
    }
  }

  /// 获取系统资源使用情况
  Future<Map<String, dynamic>> getResourceUsage() async {
    try {
      final overview = await getPerformanceOverview();
      final systemHealth = overview['system_health'] as Map<String, dynamic>? ?? {};
      
      return {
        'cpu': {
          'current': systemHealth['cpu_percent'] ?? 0,
          'status': _getResourceStatus(systemHealth['cpu_percent']?.toDouble() ?? 0),
        },
        'memory': {
          'current': systemHealth['memory_percent'] ?? 0,
          'status': _getResourceStatus(systemHealth['memory_percent']?.toDouble() ?? 0),
        },
        'disk': {
          'current': systemHealth['disk_usage'] ?? 0,
          'status': _getResourceStatus(systemHealth['disk_usage']?.toDouble() ?? 0),
        },
        'connections': {
          'current': systemHealth['active_connections'] ?? 0,
          'status': 'normal',
        },
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('Failed to get resource usage: $e');
    }
  }

  /// 获取API性能统计
  Future<Map<String, dynamic>> getApiPerformanceStats() async {
    try {
      final endpointData = await getEndpointPerformance();
      final endpointStats = endpointData['endpoint_stats'] as Map<String, dynamic>? ?? {};
      
      // 计算总体统计
      int totalRequests = 0;
      int totalErrors = 0;
      double totalResponseTime = 0;
      int endpointCount = 0;
      
      for (final stats in endpointStats.values) {
        if (stats is Map<String, dynamic>) {
          totalRequests += (stats['request_count'] as int? ?? 0);
          totalErrors += (stats['error_count'] as int? ?? 0);
          totalResponseTime += (stats['avg_response_time'] as double? ?? 0);
          endpointCount++;
        }
      }
      
      return {
        'total_requests': totalRequests,
        'total_errors': totalErrors,
        'error_rate': totalRequests > 0 ? (totalErrors / totalRequests * 100) : 0,
        'avg_response_time': endpointCount > 0 ? (totalResponseTime / endpointCount) : 0,
        'endpoint_count': endpointCount,
        'endpoints': endpointStats,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('Failed to get API performance stats: $e');
    }
  }

  /// 获取缓存性能统计
  Future<Map<String, dynamic>> getCachePerformanceStats() async {
    try {
      final overview = await getPerformanceOverview();
      final cacheStats = overview['cache_stats'] as Map<String, dynamic>? ?? {};
      
      final hits = cacheStats['hits'] as int? ?? 0;
      final misses = cacheStats['misses'] as int? ?? 0;
      final total = hits + misses;
      final hitRate = total > 0 ? (hits / total * 100) : 0;
      
      return {
        'hits': hits,
        'misses': misses,
        'total': total,
        'hit_rate': hitRate,
        'status': _getCacheStatus(hitRate),
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('Failed to get cache performance stats: $e');
    }
  }

  /// 获取数据库性能统计
  Future<Map<String, dynamic>> getDatabasePerformanceStats() async {
    try {
      final overview = await getPerformanceOverview();
      final dbStats = overview['database_stats'] as Map<String, dynamic>? ?? {};
      
      return {
        'total_users': dbStats['total_users'] ?? 0,
        'total_transactions': dbStats['total_transactions'] ?? 0,
        'today_transactions': dbStats['today_transactions'] ?? 0,
        'connection_pool_size': dbStats['connection_pool_size'] ?? 0,
        'active_connections': dbStats['active_connections'] ?? 0,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('Failed to get database performance stats: $e');
    }
  }

  /// 检查系统是否健康
  Future<bool> isSystemHealthy() async {
    try {
      final health = await getSystemHealth();
      final status = health['status'] as String? ?? 'unknown';
      return status == 'healthy';
    } catch (e) {
      return false;
    }
  }

  /// 获取系统运行时间
  Future<Duration> getSystemUptime() async {
    try {
      final health = await getSystemHealth();
      final uptime = health['uptime'] as double? ?? 0;
      return Duration(seconds: uptime.toInt());
    } catch (e) {
      return Duration.zero;
    }
  }

  String _getResourceStatus(double percentage) {
    if (percentage > 90) return 'critical';
    if (percentage > 80) return 'warning';
    if (percentage > 60) return 'caution';
    return 'normal';
  }

  String _getCacheStatus(double hitRate) {
    if (hitRate > 80) return 'excellent';
    if (hitRate > 60) return 'good';
    if (hitRate > 40) return 'fair';
    return 'poor';
  }

  // 日志相关API
  Future<Map<String, dynamic>> searchLogs({
    String? category,
    String? level,
    DateTime? startTime,
    DateTime? endTime,
    String? userId,
    int limit = 100,
  }) async {
    try {
      final queryParams = <String, String>{
        'limit': limit.toString(),
      };

      if (category != null) queryParams['category'] = category;
      if (level != null) queryParams['level'] = level;
      if (startTime != null) queryParams['start_time'] = startTime.toIso8601String();
      if (endTime != null) queryParams['end_time'] = endTime.toIso8601String();
      if (userId != null) queryParams['user_id'] = userId;

      final uri = Uri.parse('$_baseUrl/logs/search').replace(
        queryParameters: queryParams,
      );

      final headers = await _getHeaders();
      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to search logs: $e');
    }
  }

  Future<Map<String, dynamic>> getLogStatistics({int hours = 24}) async {
    try {
      final uri = Uri.parse('$_baseUrl/logs/statistics').replace(
        queryParameters: {'hours': hours.toString()},
      );

      final headers = await _getHeaders();
      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to get log statistics: $e');
    }
  }

  Future<Map<String, dynamic>> cleanupOldLogs({int days = 30}) async {
    try {
      final uri = Uri.parse('$_baseUrl/logs/cleanup').replace(
        queryParameters: {'days': days.toString()},
      );

      final headers = await _getHeaders();
      final response = await http.delete(uri, headers: headers);

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to cleanup logs: $e');
    }
  }
}
