#!/usr/bin/env python3
"""
测试高级分析功能
验证支出模式分析、预算建议、趋势预测等功能
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal
import uuid

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.connection import get_async_session
from app.models.user import User
from app.models.category import Category
from app.models.transaction import Transaction
from app.services.analytics import get_analytics_service
from sqlalchemy import select


async def create_test_data(db):
    """创建测试数据"""
    print("📊 创建测试数据...")
    
    try:
        # 创建测试用户
        test_user = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="analytics_test_user",
            password_hash="test_hash",
            is_active=True
        )
        db.add(test_user)
        await db.flush()
        
        # 创建测试类别
        categories = [
            Category(
                id=str(uuid.uuid4()),
                user_id=test_user.id,
                name="餐饮",
                category_type="expense",
                color="#FF6B6B",
                is_active=True
            ),
            Category(
                id=str(uuid.uuid4()),
                user_id=test_user.id,
                name="交通",
                category_type="expense", 
                color="#4ECDC4",
                is_active=True
            ),
            Category(
                id=str(uuid.uuid4()),
                user_id=test_user.id,
                name="购物",
                category_type="expense",
                color="#45B7D1",
                is_active=True
            )
        ]
        
        for category in categories:
            db.add(category)
        
        await db.flush()
        
        # 创建测试交易（过去6个月的数据）
        base_date = datetime.now() - timedelta(days=180)
        
        transactions = []
        for i in range(100):  # 创建100笔交易
            transaction_date = base_date + timedelta(days=i * 1.8)
            category = categories[i % len(categories)]
            
            # 模拟不同金额范围
            if i % 10 == 0:  # 大额支出
                amount = Decimal(str(200 + (i % 500)))
            elif i % 5 == 0:  # 中等支出
                amount = Decimal(str(50 + (i % 100)))
            else:  # 小额支出
                amount = Decimal(str(10 + (i % 50)))
            
            transaction = Transaction(
                id=str(uuid.uuid4()),
                user_id=test_user.id,
                category_id=category.id,
                amount=amount,
                transaction_type="expense",
                description=f"测试交易 {i+1}",
                transaction_date=transaction_date,
                created_at=transaction_date
            )
            transactions.append(transaction)
        
        for transaction in transactions:
            db.add(transaction)
        
        await db.commit()
        
        print(f"  ✅ 创建测试用户: {test_user.email}")
        print(f"  ✅ 创建{len(categories)}个类别")
        print(f"  ✅ 创建{len(transactions)}笔交易")
        
        return test_user.id
        
    except Exception as e:
        await db.rollback()
        print(f"  ❌ 创建测试数据失败: {e}")
        raise


async def test_spending_patterns_analysis(user_id: str, db):
    """测试支出模式分析"""
    print("\n🔍 测试支出模式分析...")
    
    try:
        analytics_service = await get_analytics_service()
        
        analysis = await analytics_service.analyze_spending_patterns(
            user_id=user_id,
            db=db,
            months=6
        )
        
        print(f"  📊 分析期间: {analysis['analysis_period']}")
        print(f"  📈 总交易数: {analysis['total_transactions']}")
        print(f"  💰 总金额: {analysis['total_amount']:.2f}元")
        print(f"  📊 平均金额: {analysis['average_amount']:.2f}元")
        print(f"  📊 中位数: {analysis['median_amount']:.2f}元")
        
        # 显示类别分析
        print(f"  🏷️ 类别分析:")
        for i, cat in enumerate(analysis['category_analysis'][:3]):
            print(f"     {i+1}. {cat['category']}: {cat['total_amount']:.2f}元 ({cat['percentage']:.1f}%)")
        
        # 显示趋势分析
        trend = analysis['trend_analysis']
        print(f"  📈 趋势分析: {trend['description']}")
        print(f"  📊 稳定性: {trend['stability']}")
        
        # 显示异常检测
        anomalies = analysis['anomalies']
        print(f"  ⚠️ 异常支出: {len(anomalies)}个")
        
        if analysis['total_transactions'] > 0:
            print("  🎉 支出模式分析测试通过!")
            return True
        else:
            print("  ❌ 支出模式分析测试失败!")
            return False
        
    except Exception as e:
        print(f"  ❌ 支出模式分析测试失败: {e}")
        return False


async def test_budget_recommendations(user_id: str, db):
    """测试预算建议"""
    print("\n💡 测试预算建议...")
    
    try:
        analytics_service = await get_analytics_service()
        
        recommendations = await analytics_service.generate_budget_recommendations(
            user_id=user_id,
            db=db,
            analysis_months=3
        )
        
        print(f"  📊 分析期间: {recommendations['analysis_period']}")
        
        recs = recommendations['recommendations']
        print(f"  💡 建议数量: {len(recs)}")
        
        # 显示前几个建议
        for i, rec in enumerate(recs[:3]):
            print(f"     {i+1}. {rec['type']}: {rec.get('category', '总体')} - {rec['reason']}")
            print(f"        优先级: {rec['priority']}")
        
        print("  🎉 预算建议测试通过!")
        return True
        
    except Exception as e:
        print(f"  ❌ 预算建议测试失败: {e}")
        return False


async def test_spending_predictions(user_id: str, db):
    """测试支出预测"""
    print("\n🔮 测试支出预测...")
    
    try:
        analytics_service = await get_analytics_service()
        
        predictions = await analytics_service.predict_future_spending(
            user_id=user_id,
            db=db,
            prediction_months=3
        )
        
        print(f"  📊 预测月数: {predictions['prediction_months']}")
        print(f"  📈 历史数据: {predictions['historical_data_months']}个月")
        
        preds = predictions['predictions']
        print(f"  🔮 预测结果:")
        
        for pred in preds:
            print(f"     {pred['month']}: {pred['predicted_amount']:.2f}元")
            print(f"       置信区间: {pred['confidence_interval']['lower']:.2f} - {pred['confidence_interval']['upper']:.2f}元")
        
        if len(preds) > 0:
            print("  🎉 支出预测测试通过!")
            return True
        else:
            print("  ❌ 支出预测测试失败!")
            return False
        
    except Exception as e:
        print(f"  ❌ 支出预测测试失败: {e}")
        return False


async def test_smart_insights(user_id: str, db):
    """测试智能洞察"""
    print("\n🧠 测试智能洞察...")
    
    try:
        analytics_service = await get_analytics_service()
        
        insights = await analytics_service.generate_smart_insights(
            user_id=user_id,
            db=db
        )
        
        print(f"  🧠 洞察数量: {insights['summary']['total_insights']}")
        print(f"  🔴 高优先级: {insights['summary']['high_priority']}")
        print(f"  🟡 中优先级: {insights['summary']['medium_priority']}")
        print(f"  🟢 低优先级: {insights['summary']['low_priority']}")
        
        # 显示洞察内容
        for i, insight in enumerate(insights['insights'][:3]):
            print(f"     {i+1}. [{insight['priority']}] {insight['title']}")
            print(f"        {insight['message']}")
        
        print("  🎉 智能洞察测试通过!")
        return True
        
    except Exception as e:
        print(f"  ❌ 智能洞察测试失败: {e}")
        return False


async def cleanup_test_data(user_id: str, db):
    """清理测试数据"""
    print("\n🧹 清理测试数据...")
    
    try:
        # 删除交易
        await db.execute(
            Transaction.__table__.delete().where(Transaction.user_id == user_id)
        )
        
        # 删除类别
        await db.execute(
            Category.__table__.delete().where(Category.user_id == user_id)
        )
        
        # 删除用户
        await db.execute(
            User.__table__.delete().where(User.id == user_id)
        )
        
        await db.commit()
        print("  ✅ 测试数据清理完成")
        
    except Exception as e:
        print(f"  ❌ 清理测试数据失败: {e}")


async def main():
    """主测试函数"""
    print("🚀 开始测试高级分析功能...\n")
    
    async with get_async_session() as db:
        user_id = None
        results = []
        
        try:
            # 创建测试数据
            user_id = await create_test_data(db)
            
            # 执行各项测试
            tests = [
                ("支出模式分析", test_spending_patterns_analysis),
                ("预算建议", test_budget_recommendations),
                ("支出预测", test_spending_predictions),
                ("智能洞察", test_smart_insights),
            ]
            
            for test_name, test_func in tests:
                try:
                    result = await test_func(user_id, db)
                    results.append((test_name, result))
                except Exception as e:
                    print(f"  ❌ {test_name}测试异常: {e}")
                    results.append((test_name, False))
            
        finally:
            # 清理测试数据
            if user_id:
                await cleanup_test_data(user_id, db)
    
    # 输出测试结果
    print("\n📊 高级分析功能测试结果汇总:")
    print("=" * 50)
    
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 50)
    
    if all_passed:
        print("🎉 所有高级分析测试通过！分析功能正常工作。")
        return 0
    else:
        print("❌ 部分分析测试失败，请检查相关功能。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
