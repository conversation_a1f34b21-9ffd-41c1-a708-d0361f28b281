"""
结构化日志系统
提供统一的日志记录、格式化和管理功能
"""

import json
import time
import traceback
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from enum import Enum
from pathlib import Path
from loguru import logger
import asyncio
import aiofiles
from dataclasses import dataclass, asdict
import gzip
import os


class LogLevel(Enum):
    """日志级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogCategory(Enum):
    """日志分类"""
    SYSTEM = "system"
    API = "api"
    DATABASE = "database"
    CACHE = "cache"
    AI = "ai"
    SECURITY = "security"
    PERFORMANCE = "performance"
    USER = "user"
    BUSINESS = "business"


@dataclass
class LogEntry:
    """日志条目"""
    timestamp: str
    level: str
    category: str
    message: str
    module: str
    function: str
    line: int
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    endpoint: Optional[str] = None
    method: Optional[str] = None
    status_code: Optional[int] = None
    response_time: Optional[float] = None
    error_type: Optional[str] = None
    error_message: Optional[str] = None
    stack_trace: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = None


class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, log_dir: str = "logs", max_file_size: int = 100 * 1024 * 1024,
                 retention_days: int = 30, enable_compression: bool = True):
        self.log_dir = Path(log_dir)
        self.max_file_size = max_file_size
        self.retention_days = retention_days
        self.enable_compression = enable_compression
        
        # 创建日志目录
        self.log_dir.mkdir(exist_ok=True)
        
        # 配置loguru
        self._configure_loguru()
        
        # 启动日志清理任务（延迟到有事件循环时）
        self._cleanup_task_started = False
    
    def _configure_loguru(self):
        """配置loguru日志器"""
        # 移除默认处理器
        logger.remove()
        
        # 添加控制台处理器
        logger.add(
            sink=lambda msg: print(msg, end=""),
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            level="INFO"
        )
        
        # 添加文件处理器
        for category in LogCategory:
            log_file = self.log_dir / f"{category.value}.log"
            logger.add(
                sink=str(log_file),
                format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
                level="DEBUG",
                rotation=self.max_file_size,
                retention=f"{self.retention_days} days",
                compression="gz" if self.enable_compression else None,
                filter=lambda record, cat=category: self._filter_by_category(record, cat)
            )
        
        # 添加结构化JSON日志文件
        json_log_file = self.log_dir / "structured.jsonl"
        logger.add(
            sink=str(json_log_file),
            format="{message}",
            level="DEBUG",
            rotation=self.max_file_size,
            retention=f"{self.retention_days} days",
            compression="gz" if self.enable_compression else None,
            serialize=True
        )
    
    def _filter_by_category(self, record, category: LogCategory) -> bool:
        """按分类过滤日志"""
        return record["extra"].get("category") == category.value
    
    def _ensure_cleanup_task(self):
        """确保清理任务已启动"""
        if not self._cleanup_task_started:
            try:
                asyncio.create_task(self._start_cleanup_task())
                self._cleanup_task_started = True
            except RuntimeError:
                # 没有事件循环，稍后再试
                pass

    async def _start_cleanup_task(self):
        """启动日志清理任务"""
        while True:
            try:
                await self._cleanup_old_logs()
                # 每天清理一次
                await asyncio.sleep(24 * 3600)
            except Exception as e:
                logger.error(f"日志清理任务失败: {e}")
                await asyncio.sleep(3600)  # 出错后1小时重试
    
    async def _cleanup_old_logs(self):
        """清理旧日志文件"""
        cutoff_date = datetime.now() - timedelta(days=self.retention_days)
        
        for log_file in self.log_dir.glob("*.log*"):
            try:
                file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                if file_time < cutoff_date:
                    log_file.unlink()
                    logger.info(f"删除旧日志文件: {log_file}")
            except Exception as e:
                logger.error(f"删除日志文件失败 {log_file}: {e}")
    
    def _create_log_entry(self, level: LogLevel, category: LogCategory, message: str,
                         **kwargs) -> LogEntry:
        """创建日志条目"""
        # 获取调用栈信息
        frame = traceback.extract_stack()[-3]  # 跳过当前函数和调用函数
        
        return LogEntry(
            timestamp=datetime.now().isoformat(),
            level=level.value,
            category=category.value,
            message=message,
            module=frame.filename.split('/')[-1],
            function=frame.name,
            line=frame.lineno,
            **kwargs
        )
    
    def _log_structured(self, entry: LogEntry):
        """记录结构化日志"""
        # 确保清理任务已启动
        self._ensure_cleanup_task()

        # 转换为字典
        log_dict = asdict(entry)

        # 添加到loguru的extra字段（避免重复参数）
        extra_fields = {
            'user_id': entry.user_id,
            'request_id': entry.request_id,
        }
        # 添加其他字段，但避免重复
        for key, value in log_dict.items():
            if key not in extra_fields:
                extra_fields[key] = value

        logger.bind(**extra_fields).log(entry.level, json.dumps(log_dict, ensure_ascii=False))
    
    def debug(self, message: str, category: LogCategory = LogCategory.SYSTEM, **kwargs):
        """记录调试日志"""
        entry = self._create_log_entry(LogLevel.DEBUG, category, message, **kwargs)
        self._log_structured(entry)
    
    def info(self, message: str, category: LogCategory = LogCategory.SYSTEM, **kwargs):
        """记录信息日志"""
        entry = self._create_log_entry(LogLevel.INFO, category, message, **kwargs)
        self._log_structured(entry)
    
    def warning(self, message: str, category: LogCategory = LogCategory.SYSTEM, **kwargs):
        """记录警告日志"""
        entry = self._create_log_entry(LogLevel.WARNING, category, message, **kwargs)
        self._log_structured(entry)
    
    def error(self, message: str, category: LogCategory = LogCategory.SYSTEM, 
              error: Optional[Exception] = None, **kwargs):
        """记录错误日志"""
        if error:
            kwargs.update({
                'error_type': type(error).__name__,
                'error_message': str(error),
                'stack_trace': traceback.format_exc()
            })
        
        entry = self._create_log_entry(LogLevel.ERROR, category, message, **kwargs)
        self._log_structured(entry)
    
    def critical(self, message: str, category: LogCategory = LogCategory.SYSTEM,
                error: Optional[Exception] = None, **kwargs):
        """记录严重错误日志"""
        if error:
            kwargs.update({
                'error_type': type(error).__name__,
                'error_message': str(error),
                'stack_trace': traceback.format_exc()
            })
        
        entry = self._create_log_entry(LogLevel.CRITICAL, category, message, **kwargs)
        self._log_structured(entry)
    
    def log_api_request(self, method: str, endpoint: str, status_code: int,
                       response_time: float, user_id: Optional[str] = None,
                       ip_address: Optional[str] = None, user_agent: Optional[str] = None,
                       request_id: Optional[str] = None, **kwargs):
        """记录API请求日志"""
        message = f"{method} {endpoint} - {status_code} ({response_time:.3f}s)"
        
        entry = self._create_log_entry(
            LogLevel.INFO, LogCategory.API, message,
            method=method,
            endpoint=endpoint,
            status_code=status_code,
            response_time=response_time,
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            request_id=request_id,
            **kwargs
        )
        self._log_structured(entry)
    
    def log_database_operation(self, operation: str, table: str, duration: float,
                              user_id: Optional[str] = None, **kwargs):
        """记录数据库操作日志"""
        message = f"数据库操作: {operation} on {table} ({duration:.3f}s)"
        
        entry = self._create_log_entry(
            LogLevel.INFO, LogCategory.DATABASE, message,
            user_id=user_id,
            extra_data={
                'operation': operation,
                'table': table,
                'duration': duration,
                **kwargs
            }
        )
        self._log_structured(entry)
    
    def log_cache_operation(self, operation: str, key: str, hit: bool = None,
                           duration: Optional[float] = None, **kwargs):
        """记录缓存操作日志"""
        hit_status = "HIT" if hit else "MISS" if hit is not None else "N/A"
        duration_str = f" ({duration:.3f}s)" if duration else ""
        message = f"缓存操作: {operation} {key} - {hit_status}{duration_str}"
        
        entry = self._create_log_entry(
            LogLevel.DEBUG, LogCategory.CACHE, message,
            extra_data={
                'operation': operation,
                'key': key,
                'hit': hit,
                'duration': duration,
                **kwargs
            }
        )
        self._log_structured(entry)
    
    def log_ai_request(self, provider: str, model: str, operation: str,
                      duration: float, success: bool, user_id: Optional[str] = None,
                      **kwargs):
        """记录AI请求日志"""
        status = "SUCCESS" if success else "FAILED"
        message = f"AI请求: {provider}/{model} {operation} - {status} ({duration:.3f}s)"
        
        entry = self._create_log_entry(
            LogLevel.INFO, LogCategory.AI, message,
            user_id=user_id,
            extra_data={
                'provider': provider,
                'model': model,
                'operation': operation,
                'duration': duration,
                'success': success,
                **kwargs
            }
        )
        self._log_structured(entry)
    
    def log_security_event(self, event_type: str, severity: str, user_id: Optional[str] = None,
                          ip_address: Optional[str] = None, **kwargs):
        """记录安全事件日志"""
        message = f"安全事件: {event_type} - {severity}"
        
        level = LogLevel.CRITICAL if severity == "critical" else \
                LogLevel.WARNING if severity == "warning" else LogLevel.INFO
        
        entry = self._create_log_entry(
            level, LogCategory.SECURITY, message,
            user_id=user_id,
            ip_address=ip_address,
            extra_data={
                'event_type': event_type,
                'severity': severity,
                **kwargs
            }
        )
        self._log_structured(entry)
    
    def log_performance_metric(self, metric_name: str, value: float, unit: str,
                              **kwargs):
        """记录性能指标日志"""
        message = f"性能指标: {metric_name} = {value} {unit}"
        
        entry = self._create_log_entry(
            LogLevel.DEBUG, LogCategory.PERFORMANCE, message,
            extra_data={
                'metric_name': metric_name,
                'value': value,
                'unit': unit,
                **kwargs
            }
        )
        self._log_structured(entry)
    
    def log_user_action(self, action: str, user_id: str, resource: Optional[str] = None,
                       **kwargs):
        """记录用户操作日志"""
        resource_str = f" on {resource}" if resource else ""
        message = f"用户操作: {action}{resource_str}"
        
        entry = self._create_log_entry(
            LogLevel.INFO, LogCategory.USER, message,
            user_id=user_id,
            extra_data={
                'action': action,
                'resource': resource,
                **kwargs
            }
        )
        self._log_structured(entry)
    
    def log_business_event(self, event: str, user_id: Optional[str] = None, **kwargs):
        """记录业务事件日志"""
        message = f"业务事件: {event}"
        
        entry = self._create_log_entry(
            LogLevel.INFO, LogCategory.BUSINESS, message,
            user_id=user_id,
            extra_data={
                'event': event,
                **kwargs
            }
        )
        self._log_structured(entry)
    
    async def search_logs(self, category: Optional[LogCategory] = None,
                         level: Optional[LogLevel] = None,
                         start_time: Optional[datetime] = None,
                         end_time: Optional[datetime] = None,
                         user_id: Optional[str] = None,
                         limit: int = 100) -> List[Dict[str, Any]]:
        """搜索日志"""
        logs = []
        json_log_file = self.log_dir / "structured.jsonl"
        
        if not json_log_file.exists():
            return logs
        
        try:
            async with aiofiles.open(json_log_file, 'r', encoding='utf-8') as f:
                async for line in f:
                    try:
                        log_entry = json.loads(line.strip())
                        
                        # 应用过滤条件
                        if category and log_entry.get('category') != category.value:
                            continue
                        
                        if level and log_entry.get('level') != level.value:
                            continue
                        
                        if user_id and log_entry.get('user_id') != user_id:
                            continue
                        
                        if start_time or end_time:
                            log_time = datetime.fromisoformat(log_entry.get('timestamp', ''))
                            if start_time and log_time < start_time:
                                continue
                            if end_time and log_time > end_time:
                                continue
                        
                        logs.append(log_entry)
                        
                        if len(logs) >= limit:
                            break
                            
                    except json.JSONDecodeError:
                        continue
        
        except Exception as e:
            logger.error(f"搜索日志失败: {e}")
        
        return logs
    
    async def get_log_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取日志统计信息"""
        start_time = datetime.now() - timedelta(hours=hours)
        logs = await self.search_logs(start_time=start_time, limit=10000)
        
        stats = {
            'total_logs': len(logs),
            'by_level': {},
            'by_category': {},
            'error_count': 0,
            'warning_count': 0,
            'top_errors': {},
            'time_range': {
                'start': start_time.isoformat(),
                'end': datetime.now().isoformat(),
                'hours': hours
            }
        }
        
        for log in logs:
            level = log.get('level', 'UNKNOWN')
            category = log.get('category', 'unknown')
            
            # 按级别统计
            stats['by_level'][level] = stats['by_level'].get(level, 0) + 1
            
            # 按分类统计
            stats['by_category'][category] = stats['by_category'].get(category, 0) + 1
            
            # 错误和警告计数
            if level == 'ERROR':
                stats['error_count'] += 1
                error_type = log.get('error_type', 'Unknown')
                stats['top_errors'][error_type] = stats['top_errors'].get(error_type, 0) + 1
            elif level == 'WARNING':
                stats['warning_count'] += 1
        
        return stats


# 全局结构化日志实例
structured_logger = StructuredLogger()


# 便捷函数
# 创建全局结构化日志实例
structured_logger = StructuredLogger()


def log_debug(message: str, category: LogCategory = LogCategory.SYSTEM, **kwargs):
    structured_logger.debug(message, category, **kwargs)


def log_info(message: str, category: LogCategory = LogCategory.SYSTEM, **kwargs):
    structured_logger.info(message, category, **kwargs)


def log_warning(message: str, category: LogCategory = LogCategory.SYSTEM, **kwargs):
    structured_logger.warning(message, category, **kwargs)


def log_error(message: str, category: LogCategory = LogCategory.SYSTEM,
              error: Optional[Exception] = None, **kwargs):
    structured_logger.error(message, category, error, **kwargs)


def log_critical(message: str, category: LogCategory = LogCategory.SYSTEM,
                error: Optional[Exception] = None, **kwargs):
    structured_logger.critical(message, category, error, **kwargs)
