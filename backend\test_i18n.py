#!/usr/bin/env python3
"""
测试国际化功能
验证多语言支持、翻译功能、语言检测等
"""

import sys
import os
import asyncio
from pathlib import Path

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.i18n import get_i18n_manager, SupportedLanguage, t, set_language


async def test_basic_i18n_functionality():
    """测试基本国际化功能"""
    print("🌍 测试基本国际化功能...")
    
    try:
        i18n_manager = get_i18n_manager()
        
        # 测试支持的语言
        languages = i18n_manager.get_supported_languages()
        print(f"  📋 支持的语言: {list(languages.keys())}")
        
        # 测试默认语言
        print(f"  🔤 默认语言: {i18n_manager.default_language.value}")
        print(f"  🔤 当前语言: {i18n_manager.current_language.value}")
        
        # 测试翻译文件是否加载
        loaded_languages = list(i18n_manager.translations.keys())
        print(f"  📁 已加载翻译文件: {loaded_languages}")
        
        if len(loaded_languages) >= 2:
            print("  ✅ 基本国际化功能测试通过!")
            return True
        else:
            print("  ❌ 翻译文件加载不完整!")
            return False
        
    except Exception as e:
        print(f"  ❌ 基本国际化功能测试失败: {e}")
        return False


async def test_translation_functionality():
    """测试翻译功能"""
    print("\n🔤 测试翻译功能...")
    
    try:
        # 测试中文翻译
        set_language(SupportedLanguage.CHINESE)
        chinese_text = t("common.success")
        print(f"  🇨🇳 中文: common.success = '{chinese_text}'")
        
        # 测试英文翻译
        set_language(SupportedLanguage.ENGLISH)
        english_text = t("common.success")
        print(f"  🇺🇸 英文: common.success = '{english_text}'")
        
        # 测试繁体中文翻译
        set_language(SupportedLanguage.TRADITIONAL_CHINESE)
        traditional_text = t("common.success")
        print(f"  🇹🇼 繁体中文: common.success = '{traditional_text}'")
        
        # 验证翻译结果不同
        if chinese_text != english_text:
            print("  ✅ 翻译功能测试通过!")
            return True
        else:
            print("  ❌ 翻译结果相同，可能存在问题!")
            return False
        
    except Exception as e:
        print(f"  ❌ 翻译功能测试失败: {e}")
        return False


async def test_nested_key_translation():
    """测试嵌套键翻译"""
    print("\n🔗 测试嵌套键翻译...")
    
    try:
        set_language(SupportedLanguage.CHINESE)
        
        # 测试嵌套键
        test_keys = [
            "auth.login",
            "auth.register", 
            "transaction.add_transaction",
            "budget.budget_exceeded",
            "ai.categorization_success"
        ]
        
        translations = {}
        for key in test_keys:
            translation = t(key)
            translations[key] = translation
            print(f"  🔑 {key} = '{translation}'")
        
        # 验证翻译不是原始键
        valid_translations = sum(1 for k, v in translations.items() if v != k)
        
        if valid_translations >= len(test_keys) * 0.8:  # 至少80%的键有有效翻译
            print("  ✅ 嵌套键翻译测试通过!")
            return True
        else:
            print(f"  ❌ 只有{valid_translations}/{len(test_keys)}个键有有效翻译!")
            return False
        
    except Exception as e:
        print(f"  ❌ 嵌套键翻译测试失败: {e}")
        return False


async def test_parameter_substitution():
    """测试参数替换"""
    print("\n📝 测试参数替换...")
    
    try:
        i18n_manager = get_i18n_manager()
        
        # 添加测试翻译
        i18n_manager.add_translation(
            SupportedLanguage.CHINESE,
            "test.greeting",
            "你好，{name}！欢迎使用{app_name}。"
        )
        
        i18n_manager.add_translation(
            SupportedLanguage.ENGLISH,
            "test.greeting", 
            "Hello, {name}! Welcome to {app_name}."
        )
        
        # 测试中文参数替换
        set_language(SupportedLanguage.CHINESE)
        chinese_greeting = t("test.greeting", name="张三", app_name="AI记账")
        print(f"  🇨🇳 中文参数替换: '{chinese_greeting}'")
        
        # 测试英文参数替换
        set_language(SupportedLanguage.ENGLISH)
        english_greeting = t("test.greeting", name="John", app_name="AI Accounting")
        print(f"  🇺🇸 英文参数替换: '{english_greeting}'")
        
        # 验证参数是否被正确替换
        if "张三" in chinese_greeting and "John" in english_greeting:
            print("  ✅ 参数替换测试通过!")
            return True
        else:
            print("  ❌ 参数替换失败!")
            return False
        
    except Exception as e:
        print(f"  ❌ 参数替换测试失败: {e}")
        return False


async def test_fallback_mechanism():
    """测试回退机制"""
    print("\n🔄 测试回退机制...")
    
    try:
        # 测试不存在的键
        set_language(SupportedLanguage.CHINESE)
        
        non_existent_key = "non.existent.key"
        result = t(non_existent_key)
        
        print(f"  🔍 不存在的键 '{non_existent_key}' 返回: '{result}'")
        
        # 应该返回原始键
        if result == non_existent_key:
            print("  ✅ 回退机制测试通过!")
            return True
        else:
            print("  ❌ 回退机制失败!")
            return False
        
    except Exception as e:
        print(f"  ❌ 回退机制测试失败: {e}")
        return False


async def test_translation_file_creation():
    """测试翻译文件创建"""
    print("\n📁 测试翻译文件创建...")
    
    try:
        i18n_manager = get_i18n_manager()
        translations_dir = i18n_manager.translations_dir
        
        print(f"  📂 翻译文件目录: {translations_dir}")
        
        # 检查翻译文件是否存在
        expected_files = [
            f"{SupportedLanguage.CHINESE}.json",
            f"{SupportedLanguage.ENGLISH}.json",
            f"{SupportedLanguage.TRADITIONAL_CHINESE}.json"
        ]
        
        existing_files = []
        for filename in expected_files:
            file_path = translations_dir / filename
            if file_path.exists():
                existing_files.append(filename)
                file_size = file_path.stat().st_size
                print(f"    ✅ {filename} (大小: {file_size} 字节)")
            else:
                print(f"    ❌ {filename} 不存在")
        
        if len(existing_files) >= 2:
            print("  ✅ 翻译文件创建测试通过!")
            return True
        else:
            print("  ❌ 翻译文件创建不完整!")
            return False
        
    except Exception as e:
        print(f"  ❌ 翻译文件创建测试失败: {e}")
        return False


async def test_language_switching():
    """测试语言切换"""
    print("\n🔄 测试语言切换...")
    
    try:
        i18n_manager = get_i18n_manager()
        
        # 记录初始语言
        initial_language = i18n_manager.current_language
        print(f"  🔤 初始语言: {initial_language.value}")
        
        # 切换到不同语言并测试
        languages_to_test = [
            SupportedLanguage.ENGLISH,
            SupportedLanguage.CHINESE,
            SupportedLanguage.TRADITIONAL_CHINESE
        ]
        
        switch_results = []
        for lang in languages_to_test:
            set_language(lang)
            current = i18n_manager.current_language
            success = current == lang
            switch_results.append(success)
            
            status = "✅" if success else "❌"
            print(f"    {status} 切换到 {lang.value}: {current.value}")
        
        # 恢复初始语言
        set_language(initial_language)
        
        if all(switch_results):
            print("  ✅ 语言切换测试通过!")
            return True
        else:
            print("  ❌ 部分语言切换失败!")
            return False
        
    except Exception as e:
        print(f"  ❌ 语言切换测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始测试国际化功能...\n")
    
    # 执行各项测试
    tests = [
        ("基本国际化功能", test_basic_i18n_functionality),
        ("翻译功能", test_translation_functionality),
        ("嵌套键翻译", test_nested_key_translation),
        ("参数替换", test_parameter_substitution),
        ("回退机制", test_fallback_mechanism),
        ("翻译文件创建", test_translation_file_creation),
        ("语言切换", test_language_switching),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n📊 国际化功能测试结果汇总:")
    print("=" * 50)
    
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 50)
    
    if all_passed:
        print("🎉 所有国际化测试通过！多语言功能正常工作。")
        return 0
    else:
        print("❌ 部分国际化测试失败，请检查相关功能。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
