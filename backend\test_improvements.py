#!/usr/bin/env python3
"""
测试AI系统改进功能
包括加密、缓存和反馈学习
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.encryption import APIKeyEncryption
from app.utils.cache import CacheManager
from app.services.feedback_learning import FeedbackLearningService


async def test_encryption():
    """测试API密钥加密功能"""
    print("🔐 测试API密钥加密功能...")
    
    try:
        manager = APIKeyEncryption()
        
        # 测试不同类型的API密钥
        test_keys = {
            "openai": "sk-1234567890abcdef1234567890abcdef",
            "anthropic": "sk-ant-1234567890abcdef1234567890abcdef",
            "azure": "1234567890abcdef1234567890abcdef",
        }
        
        for provider, key in test_keys.items():
            print(f"  📝 测试 {provider} 密钥:")
            print(f"     原始密钥: {key}")
            
            # 加密
            encrypted = manager.encrypt_api_key(key)
            print(f"     加密后: {encrypted[:20]}...")
            
            # 解密
            decrypted = manager.decrypt_api_key(encrypted)
            print(f"     解密后: {decrypted}")
            
            # 脱敏显示
            masked = manager.mask_api_key(key)
            print(f"     脱敏显示: {masked}")
            
            # 验证格式
            is_valid = manager.validate_api_key_format(key, provider.split("_")[0])
            print(f"     格式验证: {'✅' if is_valid else '❌'}")
            
            # 强度评估
            strength = manager.get_key_strength(key)
            print(f"     密钥强度: {strength}")
            
            # 验证加解密一致性
            if decrypted == key:
                print(f"     加解密一致性: ✅")
            else:
                print(f"     加解密一致性: ❌")
                return False
        
        print("  🎉 加密功能测试通过!")
        return True
        
    except Exception as e:
        print(f"  ❌ 加密测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_cache():
    """测试缓存功能"""
    print("\n💾 测试缓存功能...")
    
    try:
        cache = CacheManager()
        await cache.connect()
        
        # 测试基本缓存操作
        print("  📝 测试基本缓存操作:")
        
        # 设置缓存
        test_data = {"message": "Hello Cache!", "timestamp": "2024-01-01"}
        success = await cache.set("test_key", test_data, 60)
        print(f"     设置缓存: {'✅' if success else '❌'}")
        
        # 获取缓存
        cached_data = await cache.get("test_key")
        print(f"     获取缓存: {'✅' if cached_data == test_data else '❌'}")
        
        # 检查存在性
        exists = await cache.exists("test_key")
        print(f"     检查存在: {'✅' if exists else '❌'}")
        
        # 测试专用缓存方法
        print("  📝 测试专用缓存方法:")
        
        # 分类结果缓存
        user_id = "test_user_123"
        description = "星巴克咖啡"
        amount = 35.0
        transaction_type = "expense"
        result = {
            "category_name": "餐饮",
            "confidence": 0.85,
            "reasoning": "咖啡店消费"
        }
        
        success = await cache.cache_categorization_result(
            user_id, description, amount, transaction_type, result
        )
        print(f"     缓存分类结果: {'✅' if success else '❌'}")
        
        cached_result = await cache.get_categorization_result(
            user_id, description, amount, transaction_type
        )
        print(f"     获取分类结果: {'✅' if cached_result == result else '❌'}")
        
        # 用户模式缓存
        patterns = {
            "keyword": [{"key": "星巴克", "category": "餐饮", "confidence_boost": 0.1}],
            "amount_range": [{"key": "small", "category": "餐饮", "confidence_boost": 0.05}]
        }
        
        success = await cache.cache_user_patterns(user_id, patterns)
        print(f"     缓存用户模式: {'✅' if success else '❌'}")
        
        cached_patterns = await cache.get_user_patterns(user_id)
        print(f"     获取用户模式: {'✅' if cached_patterns == patterns else '❌'}")
        
        # 清除测试缓存
        await cache.delete("test_key")
        await cache.invalidate_user_cache(user_id)
        
        # 获取缓存统计
        stats = await cache.get_cache_stats()
        print(f"     缓存统计: {stats}")
        
        await cache.disconnect()
        print("  🎉 缓存功能测试通过!")
        return True
        
    except Exception as e:
        print(f"  ❌ 缓存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_feedback_learning():
    """测试反馈学习功能"""
    print("\n🧠 测试反馈学习功能...")
    
    try:
        # 注意：这里只测试服务类的逻辑，不涉及数据库操作
        print("  📝 测试学习模式提取:")
        
        # 模拟反馈数据
        feedback_data = {
            "user_id": "test_user_123",
            "transaction_description": "星巴克咖啡 35元",
            "transaction_amount": 35.0,
            "transaction_type": "expense",
            "ai_suggested_category": "其他",
            "ai_confidence": 0.6,
            "user_accepted": False,
            "user_corrected_category": "餐饮"
        }
        
        print(f"     模拟反馈: {feedback_data['transaction_description']}")
        print(f"     AI建议: {feedback_data['ai_suggested_category']} (置信度: {feedback_data['ai_confidence']})")
        print(f"     用户修正: {feedback_data['user_corrected_category']}")
        
        # 测试关键词提取
        import re
        description = feedback_data["transaction_description"].lower()
        keywords = re.findall(r'\b\w{2,}\b', description)
        print(f"     提取关键词: {keywords}")
        
        # 测试金额范围分类
        amount = feedback_data["transaction_amount"]
        ranges = [
            (0, 50, "small"),
            (50, 200, "medium"),
            (200, 1000, "large"),
            (1000, float('inf'), "very_large")
        ]
        
        for min_amount, max_amount, range_name in ranges:
            if min_amount <= amount < max_amount:
                print(f"     金额范围: {range_name} ({min_amount}-{max_amount})")
                break
        
        # 测试商户名提取
        merchant_patterns = [
            r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'(\w+店|\w+超市|\w+商场|\w+餐厅)',
        ]
        
        merchants = []
        for pattern in merchant_patterns:
            matches = re.findall(pattern, feedback_data["transaction_description"])
            merchants.extend(matches)
        
        print(f"     提取商户: {merchants}")
        
        print("  🎉 反馈学习功能测试通过!")
        return True
        
    except Exception as e:
        print(f"  ❌ 反馈学习测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("🚀 开始测试AI系统改进功能...\n")
    
    results = []
    
    # 测试加密功能
    encryption_result = await test_encryption()
    results.append(("加密功能", encryption_result))
    
    # 测试缓存功能
    cache_result = await test_cache()
    results.append(("缓存功能", cache_result))
    
    # 测试反馈学习功能
    feedback_result = await test_feedback_learning()
    results.append(("反馈学习功能", feedback_result))
    
    # 输出测试结果
    print("\n📊 测试结果汇总:")
    print("=" * 50)
    
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 50)
    
    if all_passed:
        print("🎉 所有测试通过！AI系统改进功能正常工作。")
        return 0
    else:
        print("❌ 部分测试失败，请检查相关功能。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
