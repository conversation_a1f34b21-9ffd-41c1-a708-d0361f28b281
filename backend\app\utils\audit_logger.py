"""
审计日志系统
记录系统中的所有安全相关事件和用户操作
"""

import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib
import gzip

from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc

from ..utils.cache import get_cache_manager_sync


class AuditEventType(Enum):
    """审计事件类型"""
    # 认证相关
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILED = "login_failed"
    LOGOUT = "logout"
    PASSWORD_CHANGE = "password_change"
    ACCOUNT_LOCKED = "account_locked"
    
    # API密钥相关
    API_KEY_CREATED = "api_key_created"
    API_KEY_UPDATED = "api_key_updated"
    API_KEY_DELETED = "api_key_deleted"
    API_KEY_ROTATION = "api_key_rotation"
    
    # 数据操作
    TRANSACTION_CREATED = "transaction_created"
    TRANSACTION_UPDATED = "transaction_updated"
    TRANSACTION_DELETED = "transaction_deleted"
    BULK_OPERATION = "bulk_operation"
    
    # 安全事件
    SECURITY_BREACH = "security_breach"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    UNAUTHORIZED_ACCESS = "unauthorized_access"

    # API事件
    API_ACCESS = "api_access"
    API_ERROR = "api_error"
    
    # 系统事件
    SYSTEM_START = "system_start"
    SYSTEM_SHUTDOWN = "system_shutdown"
    SYSTEM_ERROR = "system_error"
    CONFIG_CHANGE = "config_change"
    BACKUP_CREATED = "backup_created"
    
    # AI相关
    AI_CATEGORIZATION = "ai_categorization"
    AI_CONFIG_CHANGE = "ai_config_change"
    AI_ERROR = "ai_error"


class AuditLevel(Enum):
    """审计级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class AuditEvent:
    """审计事件"""
    id: str
    timestamp: datetime
    event_type: AuditEventType
    level: AuditLevel
    user_id: Optional[str]
    session_id: Optional[str]
    ip_address: Optional[str]
    user_agent: Optional[str]
    resource: Optional[str]
    action: Optional[str]
    details: Dict[str, Any]
    result: str  # success, failure, error
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['event_type'] = self.event_type.value
        data['level'] = self.level.value
        return data

    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)


class AuditLogger:
    """审计日志记录器"""

    def __init__(self):
        self.cache_manager = get_cache_manager_sync()
        self.log_directory = Path("logs/audit")
        self.log_directory.mkdir(parents=True, exist_ok=True)
        
        # 配置审计日志文件
        self.current_log_file = None
        self.log_rotation_size = 100 * 1024 * 1024  # 100MB
        self.log_retention_days = 365  # 保留365天
        
        # 内存缓存最近的审计事件
        self.recent_events: List[AuditEvent] = []
        self.max_recent_events = 1000
        
        # 异步写入队列
        self.write_queue = asyncio.Queue()
        self.writer_task = None
        
        # 启动异步写入任务
        self._start_writer_task()

    def _start_writer_task(self):
        """启动异步写入任务"""
        if self.writer_task is None or self.writer_task.done():
            self.writer_task = asyncio.create_task(self._async_writer())

    async def _async_writer(self):
        """异步写入审计日志"""
        while True:
            try:
                # 从队列获取事件
                event = await self.write_queue.get()
                if event is None:  # 停止信号
                    break
                
                # 写入日志文件
                await self._write_to_file(event)
                
                # 标记任务完成
                self.write_queue.task_done()
                
            except Exception as e:
                logger.error(f"写入审计日志失败: {e}")

    async def _write_to_file(self, event: AuditEvent):
        """写入日志文件"""
        try:
            # 获取当前日志文件
            log_file = self._get_current_log_file()
            
            # 写入事件
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(event.to_json() + '\n')
            
            # 检查是否需要轮换日志文件
            if log_file.stat().st_size > self.log_rotation_size:
                await self._rotate_log_file()
                
        except Exception as e:
            logger.error(f"写入审计日志文件失败: {e}")

    def _get_current_log_file(self) -> Path:
        """获取当前日志文件"""
        if self.current_log_file is None:
            today = datetime.now().strftime("%Y-%m-%d")
            self.current_log_file = self.log_directory / f"audit_{today}.log"
        
        return self.current_log_file

    async def _rotate_log_file(self):
        """轮换日志文件"""
        try:
            if self.current_log_file and self.current_log_file.exists():
                # 压缩当前日志文件
                timestamp = datetime.now().strftime("%H%M%S")
                compressed_file = self.current_log_file.with_suffix(f'.{timestamp}.log.gz')
                
                with open(self.current_log_file, 'rb') as f_in:
                    with gzip.open(compressed_file, 'wb') as f_out:
                        f_out.writelines(f_in)
                
                # 删除原文件
                self.current_log_file.unlink()
                
                # 重置当前日志文件
                self.current_log_file = None
                
                logger.info(f"日志文件已轮换: {compressed_file}")
                
        except Exception as e:
            logger.error(f"轮换日志文件失败: {e}")

    async def log_event(
        self,
        event_type: AuditEventType,
        level: AuditLevel = AuditLevel.MEDIUM,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        resource: Optional[str] = None,
        action: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        result: str = "success",
        error_message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """记录审计事件"""
        try:
            # 生成事件ID
            event_id = self._generate_event_id(event_type, user_id)
            
            # 创建审计事件
            event = AuditEvent(
                id=event_id,
                timestamp=datetime.now(),
                event_type=event_type,
                level=level,
                user_id=user_id,
                session_id=session_id,
                ip_address=ip_address,
                user_agent=user_agent,
                resource=resource,
                action=action,
                details=details or {},
                result=result,
                error_message=error_message,
                metadata=metadata
            )
            
            # 添加到内存缓存
            self.recent_events.append(event)
            if len(self.recent_events) > self.max_recent_events:
                self.recent_events.pop(0)
            
            # 添加到写入队列
            await self.write_queue.put(event)
            
            # 如果是高级别事件，立即处理
            if level in [AuditLevel.HIGH, AuditLevel.CRITICAL]:
                await self._handle_high_level_event(event)
            
            # 缓存最近事件
            await self._cache_recent_event(event)
            
        except Exception as e:
            logger.error(f"记录审计事件失败: {e}")

    def _generate_event_id(self, event_type: AuditEventType, user_id: Optional[str]) -> str:
        """生成事件ID"""
        timestamp = datetime.now().isoformat()
        data = f"{event_type.value}_{user_id}_{timestamp}"
        return hashlib.md5(data.encode()).hexdigest()

    async def _handle_high_level_event(self, event: AuditEvent):
        """处理高级别事件"""
        try:
            # 发送实时警报
            await self._send_security_alert(event)
            
            # 记录到特殊日志
            await self._log_to_security_log(event)
            
            # 触发自动响应
            if event.level == AuditLevel.CRITICAL:
                await self._trigger_incident_response(event)
                
        except Exception as e:
            logger.error(f"处理高级别事件失败: {e}")

    async def _send_security_alert(self, event: AuditEvent):
        """发送安全警报"""
        # 这里应该发送邮件、短信或推送通知
        logger.warning(f"安全警报: {event.event_type.value} - {event.details}")

    async def _log_to_security_log(self, event: AuditEvent):
        """记录到安全日志"""
        security_log = self.log_directory / "security.log"
        with open(security_log, 'a', encoding='utf-8') as f:
            f.write(f"[{event.timestamp.isoformat()}] {event.level.value.upper()}: {event.to_json()}\n")

    async def _trigger_incident_response(self, event: AuditEvent):
        """触发事件响应"""
        # 这里应该触发自动化的安全响应流程
        logger.critical(f"触发事件响应: {event.event_type.value}")

    async def _cache_recent_event(self, event: AuditEvent):
        """缓存最近事件"""
        try:
            cache_key = f"audit:recent:{event.user_id or 'system'}"
            
            # 获取现有缓存
            cached_events = await self.cache_manager.get(cache_key) or []
            if isinstance(cached_events, str):
                cached_events = json.loads(cached_events)
            
            # 添加新事件
            cached_events.append(event.to_dict())
            
            # 保持最近50个事件
            if len(cached_events) > 50:
                cached_events = cached_events[-50:]
            
            # 更新缓存
            await self.cache_manager.set(
                cache_key,
                json.dumps(cached_events, ensure_ascii=False),
                ttl=3600  # 1小时
            )
            
        except Exception as e:
            logger.error(f"缓存审计事件失败: {e}")

    # 便捷方法
    async def log_login_success(self, user_id: str, ip_address: str, user_agent: str):
        """记录登录成功"""
        await self.log_event(
            event_type=AuditEventType.LOGIN_SUCCESS,
            level=AuditLevel.LOW,
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            action="login",
            details={"login_method": "email_password"}
        )

    async def log_login_failed(self, email: str, ip_address: str, reason: str):
        """记录登录失败"""
        await self.log_event(
            event_type=AuditEventType.LOGIN_FAILED,
            level=AuditLevel.MEDIUM,
            ip_address=ip_address,
            action="login",
            result="failure",
            details={"email": email, "failure_reason": reason}
        )

    async def log_api_key_operation(self, user_id: str, operation: str, provider: str):
        """记录API密钥操作"""
        event_type_map = {
            "create": AuditEventType.API_KEY_CREATED,
            "update": AuditEventType.API_KEY_UPDATED,
            "delete": AuditEventType.API_KEY_DELETED,
            "rotate": AuditEventType.API_KEY_ROTATION
        }
        
        await self.log_event(
            event_type=event_type_map.get(operation, AuditEventType.API_KEY_UPDATED),
            level=AuditLevel.MEDIUM,
            user_id=user_id,
            action=operation,
            resource="api_key",
            details={"provider": provider}
        )

    async def log_security_event(
        self,
        user_id: Optional[str],
        event_type: str,
        details: Dict[str, Any],
        level: AuditLevel = AuditLevel.HIGH
    ):
        """记录安全事件"""
        # 映射事件类型
        security_event_map = {
            "key_rotation_scheduled": AuditEventType.API_KEY_ROTATION,
            "key_rotation_completed": AuditEventType.API_KEY_ROTATION,
            "key_rotation_failed": AuditEventType.API_KEY_ROTATION,
            "key_rotation_cancelled": AuditEventType.API_KEY_ROTATION,
            "suspicious_activity": AuditEventType.SUSPICIOUS_ACTIVITY,
            "unauthorized_access": AuditEventType.UNAUTHORIZED_ACCESS
        }

        mapped_event_type = security_event_map.get(event_type, AuditEventType.SECURITY_BREACH)

        await self.log_event(
            event_type=mapped_event_type,
            level=level,
            user_id=user_id,
            details=details
        )

    async def get_recent_events(
        self,
        user_id: Optional[str] = None,
        event_type: Optional[AuditEventType] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """获取最近事件"""
        try:
            # 从缓存获取
            if user_id:
                cache_key = f"audit:recent:{user_id}"
                cached_events = await self.cache_manager.get(cache_key)
                if cached_events:
                    events = json.loads(cached_events) if isinstance(cached_events, str) else cached_events
                    
                    # 过滤事件类型
                    if event_type:
                        events = [e for e in events if e.get('event_type') == event_type.value]
                    
                    return events[:limit]
            
            # 从内存获取
            events = []
            for event in reversed(self.recent_events):
                if user_id and event.user_id != user_id:
                    continue
                if event_type and event.event_type != event_type:
                    continue
                
                events.append(event.to_dict())
                
                if len(events) >= limit:
                    break
            
            return events
            
        except Exception as e:
            logger.error(f"获取最近事件失败: {e}")
            return []

    async def search_events(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        user_id: Optional[str] = None,
        event_type: Optional[AuditEventType] = None,
        level: Optional[AuditLevel] = None,
        limit: int = 1000
    ) -> List[Dict[str, Any]]:
        """搜索审计事件"""
        # 这里应该实现从日志文件或数据库搜索
        # 目前返回内存中的事件
        events = []
        
        for event in reversed(self.recent_events):
            # 时间过滤
            if start_time and event.timestamp < start_time:
                continue
            if end_time and event.timestamp > end_time:
                continue
            
            # 用户过滤
            if user_id and event.user_id != user_id:
                continue
            
            # 事件类型过滤
            if event_type and event.event_type != event_type:
                continue
            
            # 级别过滤
            if level and event.level != level:
                continue
            
            events.append(event.to_dict())
            
            if len(events) >= limit:
                break
        
        return events

    async def cleanup_old_logs(self):
        """清理旧日志"""
        try:
            cutoff_date = datetime.now() - timedelta(days=self.log_retention_days)
            
            for log_file in self.log_directory.glob("audit_*.log*"):
                if log_file.stat().st_mtime < cutoff_date.timestamp():
                    log_file.unlink()
                    logger.info(f"已删除旧日志文件: {log_file}")
                    
        except Exception as e:
            logger.error(f"清理旧日志失败: {e}")

    async def stop(self):
        """停止审计日志记录器"""
        # 发送停止信号
        await self.write_queue.put(None)
        
        # 等待写入任务完成
        if self.writer_task:
            await self.writer_task


# 全局审计日志记录器实例
_audit_logger = None


def get_audit_logger() -> AuditLogger:
    """获取审计日志记录器实例"""
    global _audit_logger
    if _audit_logger is None:
        _audit_logger = AuditLogger()
    return _audit_logger
